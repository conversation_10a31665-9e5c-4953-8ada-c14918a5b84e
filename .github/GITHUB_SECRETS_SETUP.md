# 🔐 GitHub Secrets 配置指南

## 必须配置的 Secrets

在 GitHub 仓库的 `Settings` → `Secrets and variables` → `Actions` 中添加以下 secrets：

### 🔧 应用配置
```bash
CONFIG_JSON
```
**说明**: 应用程序的完整配置文件内容，JSON 格式
**示例**: 
```json
{
  "BOT_API_ESERVER_ADDRESS": ":8000",
  "BOT_API_TELEGRAM_BOTTOKEN": "your-bot-token",
  "BOT_API_DATABASE_HOST": "your-db-host",
  "BOT_API_REDIS_HOST": "your-redis-host"
}
```

### 🖥️ 远程服务器配置
```bash
SERVER_HOST
SERVER_USER  
SERVER_SSH_KEY
SERVER_PATH
```

**详细说明**:

#### `SERVER_HOST`
- **说明**: 远程服务器的 IP 地址或域名
- **示例**: `*************` 或 `your-server.com`

#### `SERVER_USER`
- **说明**: SSH 用户名
- **示例**: `root` 或 `ubuntu` 或 `deploy`

#### `SERVER_SSH_KEY`
- **说明**: SSH 私钥内容 (完整的私钥文件内容)
- **示例**: 
```
-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAFwAAAAdzc2gtcn
...
-----END OPENSSH PRIVATE KEY-----
```

#### `SERVER_PATH`
- **说明**: 远程服务器上项目的部署路径
- **示例**: `/home/<USER>/botapi_authpay`

### 🔧 可选配置
```bash
SERVER_PORT
```
- **说明**: SSH 端口号 (默认: 22)
- **示例**: `22` 或 `2222`

## 🚀 SSH 密钥生成步骤

### 1. 在本地生成 SSH 密钥对
```bash
# 生成新的 SSH 密钥对
ssh-keygen -t rsa -b 4096 -C "github-actions@your-repo"

# 按提示保存到指定位置，例如：
# /Users/<USER>/.ssh/github_actions_rsa
```

### 2. 将公钥添加到远程服务器
```bash
# 复制公钥到服务器
ssh-copy-id -i ~/.ssh/github_actions_rsa.pub user@your-server

# 或手动添加到服务器的 ~/.ssh/authorized_keys
cat ~/.ssh/github_actions_rsa.pub >> ~/.ssh/authorized_keys
```

### 3. 将私钥添加到 GitHub Secrets
```bash
# 复制私钥内容
cat ~/.ssh/github_actions_rsa

# 将输出的完整内容添加到 GitHub Secrets 的 SERVER_SSH_KEY
```

## 📁 远程服务器准备

### 1. 创建项目目录
```bash
# 在远程服务器上创建项目目录
mkdir -p /home/<USER>/botapi_authpay
cd /home/<USER>/botapi_authpay
```

### 2. 复制必要文件
```bash
# 将以下文件复制到远程服务器:
# - docker-compose.prod.yml
# - manifest/i18n/ (国际化文件)

# 创建必要的目录
mkdir -p logs manifest/i18n
```

### 3. 安装 Docker 和 Docker Compose
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install docker.io docker-compose-plugin

# 启动 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker

# 将用户加入 docker 组
sudo usermod -aG docker $USER
```

### 4. 创建外部网络
```bash
# 创建 docker-compose 中使用的外部网络
docker network create xpay_app-network
```

## 🔍 配置验证

### 测试 SSH 连接
```bash
# 本地测试 SSH 连接
ssh -i ~/.ssh/github_actions_rsa user@your-server
```

### 测试配置文件
```bash
# 验证 CONFIG_JSON 格式
echo '$CONFIG_JSON_CONTENT' | jq .
```

## 📋 配置检查清单

- [ ] `CONFIG_JSON` - 应用配置文件内容
- [ ] `SERVER_HOST` - 服务器地址  
- [ ] `SERVER_USER` - SSH 用户名
- [ ] `SERVER_SSH_KEY` - SSH 私钥
- [ ] `SERVER_PATH` - 部署路径
- [ ] `SERVER_PORT` - SSH 端口 (可选)
- [ ] 远程服务器已安装 Docker
- [ ] 远程服务器已创建外部网络
- [ ] SSH 密钥已正确配置
- [ ] 项目目录已创建

## 🚨 安全提醒

1. **私钥安全**: SSH 私钥只添加到 GitHub Secrets，不要提交到代码仓库
2. **最小权限**: 为部署创建专门的用户，避免使用 root
3. **定期轮换**: 定期更换 SSH 密钥和访问凭证
4. **网络安全**: 确保服务器防火墙配置正确
5. **备份重要**: 定期备份配置文件和数据

## 🔄 工作流程

配置完成后，工作流程如下：

1. **推送代码到 main 分支**
2. **自动触发 CI/CD 流程**:
   - 运行测试
   - 构建 Docker 镜像 (包含配置文件)
   - 推送镜像到 GitHub Container Registry
   - SSH 连接到远程服务器
   - 部署最新镜像
   - 复制配置文件到服务器
   - 启动服务

3. **验证部署**:
   - 检查服务状态
   - 查看日志
   - 验证功能正常

配置完成后，每次推送到 main 分支都会自动部署到远程服务器！🎉