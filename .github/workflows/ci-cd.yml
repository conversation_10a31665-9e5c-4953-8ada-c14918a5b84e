name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
    tags: ["v*"]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查和测试
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        go-version: [1.24]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.7

      - name: Set up Go
        uses: actions/setup-go@v5.0.2
        with:
          go-version: ${{ matrix.go-version }}

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Install dependencies
        run: go mod download

      # - name: Run govulncheck
      #   run: |
      #     go install golang.org/x/vuln/cmd/govulncheck@latest
      #     govulncheck ./...

      # - name: Run linter
      #   uses: golangci/golangci-lint-action@v6.1.0
      #   continue-on-error: true
      #   with:
      #     version: latest
      #     args: --timeout=5m

      # - name: Run tests
      #   continue-on-error: true
      #   run: |
      #     go test -v -race -coverprofile=coverage.out ./...
      #     go tool cover -html=coverage.out -o coverage.html

      # - name: Upload coverage reports
      #   continue-on-error: true
      #   uses: codecov/codecov-action@v4.5.0
      #   with:
      #     file: ./coverage.out
      #     flags: unittests
      #     name: codecov-umbrella

      - name: Build binaries
        run: |
          mkdir -p bin
          go build -o bin/bot_gateway ./cmd/bot_gateway
          go build -o bin/message_processor ./cmd/message_processor
          go build -o bin/task ./cmd/task
          go build -o bin/price-monitor ./cmd/price-monitor

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4.3.4
        with:
          name: binaries-${{ matrix.go-version }}
          path: bin/

  # 构建和推送 Docker 镜像
  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'
    permissions:
      contents: read
      packages: write
    strategy:
      max-parallel: 1  # 限制为顺序执行
      matrix:
        service: [bot-gateway, message-processor, task, price-monitor]
        include:
          - service: bot-gateway
            dockerfile: Dockerfile.bot_gateway
            context: .
          - service: message-processor
            dockerfile: Dockerfile.message_processor
            context: .
          - service: task
            dockerfile: Dockerfile.task
            context: .
          - service: price-monitor
            dockerfile: Dockerfile.price_monitor
            context: .

    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.7

      - name: Create config from secrets
        run: |
          echo "创建配置文件..."
          echo '${{ secrets.CONFIG_JSON }}' > config_variables.json
          ls -la config_variables.json
          echo "配置文件创建完成"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3.6.1

      - name: Log in to Container Registry
        uses: docker/login-action@v3.3.0
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5.5.1
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-

      - name: Build and push Docker image
        uses: docker/build-push-action@v6.7.0
        with:
          context: ${{ matrix.context }}
          file: ${{ matrix.dockerfile }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/arm64

    # - name: Run Trivy vulnerability scanner
    #   uses: aquasecurity/trivy-action@master
    #   continue-on-error: true
    #   with:
    #     image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}:main
    #     format: 'sarif'
    #     output: 'trivy-results.sarif'

    # - name: Upload Trivy scan results to GitHub Security tab
    #   uses: github/codeql-action/upload-sarif@v3.26.6
    #   if: always() && hashFiles('trivy-results.sarif') != ''
    #   with:
    #     sarif_file: 'trivy-results.sarif'

  # # 部署到开发环境
  # deploy-dev:
  #   needs: build-and-push
  #   runs-on: ubuntu-latest
  #   if: github.ref == 'refs/heads/develop'
  #   environment: development

  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v4.1.7

  #   - name: Create development config
  #     run: |
  #       # 创建开发环境的 config_variables.json
  #       # 这里可以从 GitHub Secrets 中获取配置
  #       echo "Creating development configuration..."
  #       # 示例：可以从模板创建或从 secrets 中获取

  #   - name: Deploy to development
  #     run: |
  #       echo "Deploying to development environment..."
  #       ./scripts/deploy.sh development ${{ github.sha }}

  # 部署到主服务器 (main 分支)
  deploy-main:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.7

      - name: Create production config for remote server
        run: |
          echo "创建远程服务器配置文件..."
          echo '${{ secrets.CONFIG_JSON }}' > config_variables.json
          ls -la config_variables.json

      - name: Deploy to remote server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ secrets.SERVER_PORT || 22 }}
          script: |
            set -e  # 遇到错误立即退出

            echo "🚀 开始部署到远程服务器..."




            # 检查必要的命令
            if ! command -v docker &> /dev/null; then
              echo "❌ 错误：Docker 未安装"
              exit 1
            fi

            if ! command -v docker compose &> /dev/null; then
              echo "❌ 错误：Docker Compose 未安装"
              exit 1
            fi

            # 进入部署目录
            if [ ! -d "${{ secrets.SERVER_PATH }}" ]; then
              echo "❌ 错误：部署目录不存在: ${{ secrets.SERVER_PATH }}"
              exit 1
            fi


            # 检查log目录是否存在 
            if [ ! -d "./logs" ]; then
              echo "创建日志目录..."
              mkdir -p ./logs
            fi


            cd ${{ secrets.SERVER_PATH }}

            # 设置环境变量
            export GITHUB_REPOSITORY=${{ github.repository }}
            export IMAGE_TAG=main

            # 登录到 GitHub Container Registry
            echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin

            # 拉取最新镜像
            echo "📥 拉取最新镜像..."
            docker compose -f docker-compose.prod.yml pull

            # 停止现有服务
            echo "🛑 停止现有服务..."
            docker compose -f docker-compose.prod.yml down

            # 启动服务
            echo "▶️  启动服务..."
            docker compose -f docker-compose.prod.yml up -d

            # 检查服务状态
            echo "🔍 检查服务状态..."
            sleep 10
            docker compose -f docker-compose.prod.yml ps

            echo "✅ 部署完成！"

      - name: Copy config to remote server
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ secrets.SERVER_PORT || 22 }}
          source: "config_variables.json"
          target: ${{ secrets.SERVER_PATH }}

  # # 部署到生产环境 (标签版本)
  # deploy-prod:
  #   needs: build-and-push
  #   runs-on: ubuntu-latest
  #   if: startsWith(github.ref, 'refs/tags/v')
  #   environment: production

  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v4.1.7

  #   - name: Create production config
  #     run: |
  #       echo "创建生产环境配置..."
  #       echo '${{ secrets.CONFIG_JSON }}' > config_variables.json

  #   - name: Deploy to production
  #     run: |
  #       echo "部署到生产环境..."
  #       ./scripts/deploy.sh production ${GITHUB_REF#refs/tags/}
