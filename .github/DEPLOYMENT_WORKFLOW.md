# 🚀 完整部署工作流程说明

## 📊 当前工作流程

### **触发条件**
```yaml
推送到 main 分支 → 自动构建镜像 → 自动部署到远程服务器 ✅
推送到 develop 分支 → 自动构建镜像 → 自动部署到开发环境 ✅  
创建 v* 标签 → 自动构建镜像 → 自动部署到生产环境 ✅
提交 PR 到 main → 运行测试，不构建镜像 ✅
```

### **工作流程图**
```
代码推送 (main)
    ↓
运行测试 (test job)
    ↓
构建镜像 (build-and-push job)
    ├── 从 GitHub Secrets 创建 config_variables.json
    ├── 构建 4 个服务的 Docker 镜像
    ├── 推送到 ghcr.io/your-repo/service:sha
    └── 扫描漏洞 (Trivy)
    ↓
部署到远程服务器 (deploy-main job)
    ├── SSH 连接到远程服务器
    ├── 复制配置文件到服务器
    ├── 拉取最新镜像
    ├── 停止现有服务
    ├── 启动新服务
    └── 检查服务状态
```

## 🔧 技术实现细节

### **配置文件处理**
```bash
# 1. CI/CD 构建阶段
echo '${{ secrets.CONFIG_JSON }}' > config_variables.json

# 2. Docker 镜像包含 entrypoint.sh
COPY --from=builder /build/entrypoint.sh .
ENTRYPOINT [ "./entrypoint.sh" ]

# 3. 容器启动时自动处理配置
./entrypoint.sh → 解析 config_variables.json → 生成 config.yaml → 启动应用
```

### **镜像标签策略**
```yaml
# 推送到 main 分支时的镜像标签:
ghcr.io/your-repo/bot-gateway:main-abc1234     # 带 commit sha
ghcr.io/your-repo/bot-gateway:main             # 分支标签

# 版本标签时的镜像标签:
ghcr.io/your-repo/bot-gateway:v1.0.0          # 版本号
ghcr.io/your-repo/bot-gateway:1.0             # 主版本号
ghcr.io/your-repo/bot-gateway:1                # 大版本号
```

### **服务架构**
```yaml
services:
  bot-gateway:      # Telegram 机器人网关
  message-processor: # 消息处理器  
  task:             # 定时任务服务
  price-monitor:    # 价格监控服务
  
# 所有服务通过内部网络通信，无外部端口暴露
# 配置文件、日志、国际化文件通过卷映射
```

## 📋 部署检查清单

### **GitHub 配置检查**
- [ ] Repository Settings → Actions → General → Workflow permissions 设置为 "Read and write permissions"
- [ ] Secrets 配置完整：CONFIG_JSON, SERVER_HOST, SERVER_USER, SERVER_SSH_KEY, SERVER_PATH
- [ ] Environment protection rules 配置 (可选)

### **远程服务器检查**  
- [ ] Docker 和 Docker Compose 已安装
- [ ] 外部网络已创建：`docker network create xpay_app-network`
- [ ] 项目目录已创建且权限正确
- [ ] SSH 密钥已配置且可正常连接
- [ ] 防火墙规则允许必要端口 (SSH, Docker)

### **首次部署验证**
```bash
# 1. 检查镜像是否成功推送
docker pull ghcr.io/your-repo/bot-gateway:main

# 2. 检查远程服务器连接
ssh user@server-ip "docker --version"

# 3. 检查网络是否存在
ssh user@server-ip "docker network ls | grep xpay_app-network"

# 4. 检查项目目录
ssh user@server-ip "ls -la /path/to/project/"
```

## 🚨 故障排除

### **常见问题及解决方案**

#### **镜像构建失败**
```bash
# 检查 CONFIG_JSON 格式
echo '$CONFIG_JSON' | jq .

# 检查 Dockerfile 语法
docker build -f Dockerfile.bot_gateway -t test .
```

#### **SSH 连接失败**
```bash
# 测试 SSH 连接
ssh -i private_key user@server-ip

# 检查 SSH 密钥格式
head -1 private_key  # 应该是 -----BEGIN OPENSSH PRIVATE KEY-----
```

#### **容器启动失败**
```bash
# 检查容器日志
docker logs container-name

# 检查配置文件是否存在
docker exec container-name ls -la /home/<USER>

# 检查网络连接
docker network inspect xpay_app-network
```

#### **服务无法通信**
```bash
# 检查服务状态
docker-compose -f docker-compose.prod.yml ps

# 检查内部网络
docker exec bot-gateway ping message-processor

# 检查端口监听
docker exec bot-gateway netstat -tlnp
```

## 📈 监控和维护

### **部署后检查**
```bash
# 1. 检查所有服务状态
docker-compose -f docker-compose.prod.yml ps

# 2. 查看服务日志
docker-compose -f docker-compose.prod.yml logs --tail=50

# 3. 检查资源使用
docker stats

# 4. 检查磁盘空间
df -h
docker system df
```

### **定期维护**
```bash
# 清理未使用的镜像
docker image prune -a

# 清理未使用的容器
docker container prune

# 备份配置文件
cp config_variables.json config_variables.json.backup.$(date +%Y%m%d)

# 更新系统和 Docker
sudo apt update && sudo apt upgrade
```

## 🎯 性能优化建议

1. **镜像优化**: 使用多阶段构建，减小镜像大小
2. **缓存策略**: 利用 Docker 层缓存和 GitHub Actions 缓存
3. **并行构建**: 4 个服务并行构建，提高效率
4. **网络优化**: 使用内部网络，减少网络开销
5. **资源限制**: 根据服务器配置设置容器资源限制

配置完成后，你的 CI/CD 流程就是业界标准的自动化部署了！🎉