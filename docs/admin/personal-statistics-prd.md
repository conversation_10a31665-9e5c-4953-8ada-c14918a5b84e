# Personal Statistics Admin Function - Technical PRD

## 1. Overview

### 1.1 Function Purpose
The Personal Statistics Admin Function provides administrators with comprehensive user-level analytics and data insights for individual users in the gaming bot platform. This function enables admins to access detailed user financial activity, gaming behavior, and account status information for support, compliance, and operational purposes.

### 1.2 Target Users
- System administrators with admin privileges (verified through `tenants.telegram_account`)
- Customer support personnel requiring user account details
- Compliance officers monitoring user activities

### 1.3 Current Implementation Status
- **Framework**: Basic structure implemented in `internal/service/admin.go`
- **Authorization**: Admin permission verification complete
- **UI Handler**: Callback handler structure in place (`internal/admin/handler_callback.go`)
- **Data Models**: `PersonalStats` struct defined but minimal (placeholder implementation)
- **Status**: Placeholder implementation - core functionality not yet developed

## 2. Technical Specifications

### 2.1 Data Architecture

#### 2.1.1 Core Data Sources
```go
// Primary user data from users table
type UserProfile struct {
    ID               uint64          `json:"id"`
    TelegramID       uint64          `json:"telegram_id"`
    FirstName        string          `json:"first_name"`
    Account          string          `json:"account"`
    TotalDepositAmount     decimal.Decimal `json:"total_deposit_amount"`
    TotalWithdrawAmount    decimal.Decimal `json:"total_withdraw_amount"`
    TotalBettingVolume     decimal.Decimal `json:"total_betting_volume"`
    WithdrawBettingVolume  decimal.Decimal `json:"withdraw_betting_volume"`
    IsStop          int             `json:"is_stop"`  // User status
    CreatedAt       *gtime.Time     `json:"created_at"`
}

// Transaction history aggregation
type UserTransactionSummary struct {
    TodayDeposit     decimal.Decimal `json:"today_deposit"`
    TodayWithdraw    decimal.Decimal `json:"today_withdraw"`
    YesterdayDeposit decimal.Decimal `json:"yesterday_deposit"`
    YesterdayWithdraw decimal.Decimal `json:"yesterday_withdraw"`
    MonthDeposit     decimal.Decimal `json:"month_deposit"`
    MonthWithdraw    decimal.Decimal `json:"month_withdraw"`
    LastMonthDeposit decimal.Decimal `json:"last_month_deposit"`
    LastMonthWithdraw decimal.Decimal `json:"last_month_withdraw"`
}

// Gaming statistics from game tables
type UserGameSummary struct {
    TodayGameFlow    map[string]decimal.Decimal `json:"today_game_flow"`
    YesterdayGameFlow map[string]decimal.Decimal `json:"yesterday_game_flow"`
    TodayWinLoss     decimal.Decimal `json:"today_win_loss"`
    TotalWinLoss     decimal.Decimal `json:"total_win_loss"`
}
```

#### 2.1.2 Enhanced PersonalStats Structure
```go
type PersonalStats struct {
    // User Identity
    UserID        uint64  `json:"user_id"`
    PlatformID    string  `json:"platform_id"`
    TelegramID    int64   `json:"telegram_id"`
    FirstName     string  `json:"first_name"`
    Account       string  `json:"account"`
    
    // Financial Summary
    TotalDeposit    string `json:"total_deposit"`
    TotalWithdraw   string `json:"total_withdraw"`
    CurrentBalance  string `json:"current_balance"`
    TotalWinLoss    string `json:"total_win_loss"`
    
    // Time-based Analytics
    TodayDeposit     string `json:"today_deposit"`
    TodayWithdraw    string `json:"today_withdraw"`
    YesterdayDeposit string `json:"yesterday_deposit"`
    YesterdayWithdraw string `json:"yesterday_withdraw"`
    MonthDeposit     string `json:"month_deposit"`
    MonthWithdraw    string `json:"month_withdraw"`
    LastMonthDeposit string `json:"last_month_deposit"`
    LastMonthWithdraw string `json:"last_month_withdraw"`
    
    // Gaming Analytics
    GameFlowToday    map[string]string `json:"game_flow_today"`
    GameFlowYesterday map[string]string `json:"game_flow_yesterday"`
    TodayWinLoss     string `json:"today_win_loss"`
    RemainingFlowRequirement string `json:"remaining_flow_requirement"`
    
    // Account Status
    UserStatus    string `json:"user_status"` // "正常" or "已封号"
    Currency      string `json:"currency"`
    CreatedAt     string `json:"created_at"`
    LastActiveAt  string `json:"last_active_at"`
}
```

### 2.2 Database Query Requirements

#### 2.2.1 User-Specific Data Aggregation Queries
```sql
-- Primary user information with financial summaries
SELECT 
    u.id as user_id,
    u.account,
    u.total_deposit_amount,
    u.total_withdraw_amount,
    u.total_betting_volume,
    u.withdraw_betting_volume,
    u.is_stop,
    u.created_at,
    u.last_login_time,
    -- Calculate current balance from wallets
    COALESCE(w.available_balance, 0) as current_balance
FROM users u
LEFT JOIN wallets w ON u.main_wallet_id = w.wallet_id
WHERE u.id = ? AND u.tenant_id = ?;

-- Time-based transaction aggregation
SELECT 
    DATE(created_at) as transaction_date,
    type,
    direction,
    SUM(CASE WHEN direction = 'in' THEN amount ELSE 0 END) as total_in,
    SUM(CASE WHEN direction = 'out' THEN amount ELSE 0 END) as total_out
FROM transactions 
WHERE user_id = ? 
    AND tenant_id = ?
    AND type IN ('deposit', 'withdrawal')
    AND status = 1
    AND created_at >= DATE_SUB(NOW(), INTERVAL 60 DAY)
GROUP BY DATE(created_at), type, direction;

-- Gaming flow analysis
SELECT 
    DATE(created_at) as game_date,
    game_type,
    SUM(amount) as total_betting_amount
FROM game_transactions 
WHERE user_id = ? 
    AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at), game_type;
```

#### 2.2.2 Privacy-Compliant Data Access
```go
// Service layer access control
func (s *adminService) GetPersonalStats(ctx context.Context, userIdentifier string) (*PersonalStats, error) {
    // 1. Verify admin permission
    isAdmin, err := s.IsCurrentUserAdmin(ctx)
    if err != nil || !isAdmin {
        return nil, ErrNotAdmin
    }
    
    // 2. Get tenant context for data isolation
    tenantId, ok := tenant.GetTenantIdFromContext(ctx)
    if !ok {
        return nil, ErrTenantNotFound
    }
    
    // 3. Resolve user identifier (telegram ID or username)
    userID, err := s.resolveUserIdentifier(ctx, userIdentifier, tenantId)
    if err != nil {
        return nil, err
    }
    
    // 4. Aggregate user statistics with tenant isolation
    return s.aggregateUserStatistics(ctx, userID, tenantId)
}
```

### 2.3 User Identification Workflow

#### 2.3.1 Multi-Format User Resolution
```go
func (s *adminService) resolveUserIdentifier(ctx context.Context, identifier string, tenantId int) (uint64, error) {
    var userID uint64
    var err error
    
    // Try to parse as Telegram ID (numeric)
    if telegramID, parseErr := strconv.ParseInt(identifier, 10, 64); parseErr == nil {
        // Search by Telegram ID
        var user *entity.Users
        err = dao.Users.Ctx(ctx).
            Where("telegram_id = ? AND tenant_id = ? AND deleted_at IS NULL", telegramID, tenantId).
            Scan(&user)
        if err == nil && user != nil {
            return uint64(user.Id), nil
        }
    }
    
    // Try to parse as username (with or without @)
    username := strings.TrimPrefix(identifier, "@")
    var user *entity.Users
    err = dao.Users.Ctx(ctx).
        Where("account = ? AND tenant_id = ? AND deleted_at IS NULL", username, tenantId).
        Scan(&user)
    if err == nil && user != nil {
        return uint64(user.Id), nil
    }
    
    return 0, gerror.New("用户不存在")
}
```

## 3. UI/UX Design Specifications

### 3.1 Admin Interface Flow

#### 3.1.1 Entry Point
- **Trigger**: Admin clicks "个人统计数据" button in admin center
- **Action**: Replace current message with user input prompt
- **Message**: Request user Telegram ID or username input

#### 3.1.2 User Input Handling
```go
// Message handler for user input
func HandlePersonalStatsInput(ctx context.Context, message *tgbotapi.Message) error {
    userInput := strings.TrimSpace(message.Text)
    
    // Validate input format
    if userInput == "" {
        return sendErrorMessage(ctx, message.Chat.ID, "请输入有效的用户ID或用户名")
    }
    
    // Get personal statistics
    stats, err := service.Admin().GetPersonalStats(ctx, userInput)
    if err != nil {
        if err == service.ErrUserNotFound {
            return sendErrorPopup(ctx, message.Chat.ID, "用户不存在")
        }
        return sendErrorMessage(ctx, message.Chat.ID, "查询失败，请稍后重试")
    }
    
    // Send formatted statistics message
    return sendPersonalStatsMessage(ctx, message.Chat.ID, stats)
}
```

#### 3.1.3 Statistics Display Format
```
Eriq的总数据

平台ID：12345
TelegramID：5322691835

今日存款：1234.23        今日取款：1234.23
昨日存款：1234.23        昨日取款：1234.23
本月存款：1234.23        本月取款：0
上月存款：0             上月取款：0

总存款：10
总取款：5
总余额：20
总盈亏：15

○ 今日游戏1流水：0      ○ 今日游戏2流水：1234      ○ 今日游戏3流水：123
昨日游戏1流水：0         昨日游戏2流水：2         昨日游戏3流水：3

今日输赢：2
剩余流水要求：0

所有单位为CNY
用户状态：正常

【查看Ta指定日期范围流水】
【查看Ta的注单】【查看Ta的账变】
【改变用户状态】
【管理员中心】
```

### 3.2 Interactive Features

#### 3.2.1 User Status Management
```go
func (s *adminService) ToggleUserStatus(ctx context.Context, userID uint64) error {
    // Get current user status
    var user *entity.Users
    err := dao.Users.Ctx(ctx).Where("id = ?", userID).Scan(&user)
    if err != nil {
        return err
    }
    
    // Toggle status (0 = active, 1 = suspended)
    newStatus := 1 - user.IsStop
    statusText := "已封号"
    if newStatus == 0 {
        statusText = "已解封"
    }
    
    // Update user status
    _, err = dao.Users.Ctx(ctx).
        Where("id = ?", userID).
        Update(g.Map{"is_stop": newStatus})
    if err != nil {
        return err
    }
    
    // Send confirmation popup
    return sendStatusChangeConfirmation(ctx, statusText)
}
```

#### 3.2.2 Sub-function Navigation
- **查看Ta指定日期范围流水**: Navigate to date range flow analysis
- **查看Ta的注单**: Navigate to user bet history
- **查看Ta的账变**: Navigate to account change logs
- **改变用户状态**: Toggle user account status (active/suspended)

## 4. Privacy & Data Protection

### 4.1 Data Access Controls

#### 4.1.1 Admin Permission Verification
```go
func (s *adminService) verifyAdminAccess(ctx context.Context) error {
    isAdmin, err := s.IsCurrentUserAdmin(ctx)
    if err != nil {
        g.Log().Errorf(ctx, "Admin verification failed: %v", err)
        return gerror.Wrap(err, "权限验证失败")
    }
    if !isAdmin {
        g.Log().Warningf(ctx, "Non-admin user attempted to access personal stats")
        return ErrNotAdmin
    }
    return nil
}
```

#### 4.1.2 Tenant Data Isolation
- All queries must include `tenant_id` filtering
- Cross-tenant data access prevention
- Audit logging for all admin data access

#### 4.1.3 Sensitive Data Handling
```go
// Mask sensitive information based on context
func (s *adminService) maskSensitiveData(stats *PersonalStats, accessLevel string) {
    if accessLevel != "full_admin" {
        // Mask partial Telegram ID for support staff
        stats.TelegramID = maskTelegramID(stats.TelegramID)
    }
}

func maskTelegramID(telegramID int64) int64 {
    idStr := strconv.FormatInt(telegramID, 10)
    if len(idStr) > 4 {
        // Show only last 4 digits
        return telegramID % 10000
    }
    return telegramID
}
```

### 4.2 Audit and Compliance

#### 4.2.1 Access Logging
```go
func (s *adminService) logPersonalStatsAccess(ctx context.Context, adminUserID uint64, targetUserID uint64, action string) {
    logEntry := &entity.AdminOperationLog{
        AdminUserID:   adminUserID,
        TargetUserID:  targetUserID,
        Action:        action,
        IPAddress:     getClientIP(ctx),
        UserAgent:     getUserAgent(ctx),
        Timestamp:     gtime.Now(),
    }
    
    dao.AdminOperationLog.Ctx(ctx).Insert(logEntry)
}
```

#### 4.2.2 Data Retention Policy
- Personal statistics queries cached for 5 minutes maximum
- No persistent storage of aggregated personal data
- Audit logs retained for 90 days minimum

## 5. Implementation Roadmap

### 5.1 Phase 1: Core Statistics Engine (Week 1-2)
1. **Database Schema Analysis**
   - Review and optimize existing indexes for performance
   - Create necessary aggregation views
   
2. **Service Layer Implementation**
   - Complete `GetPersonalStats` method implementation
   - Implement user resolution logic
   - Add financial data aggregation
   
3. **Basic UI Integration**
   - Complete user input handling
   - Implement statistics message formatting
   - Add error handling and validation

### 5.2 Phase 2: Gaming Analytics Integration (Week 3)
1. **Game Data Integration**
   - Connect to game provider APIs
   - Implement betting flow calculations
   - Add win/loss statistics
   
2. **Advanced Features**
   - User status management
   - Date range queries
   - Account change tracking

### 5.3 Phase 3: Enhanced Features & Optimization (Week 4)
1. **Performance Optimization**
   - Implement caching strategies
   - Add database query optimization
   - Load testing and performance tuning
   
2. **Additional Analytics**
   - Referral statistics
   - Commission tracking
   - Advanced reporting features

### 5.4 Phase 4: Testing & Documentation (Week 5)
1. **Comprehensive Testing**
   - Unit tests for service layer
   - Integration tests for admin workflows
   - Privacy compliance verification
   
2. **Documentation**
   - API documentation
   - Admin user guides
   - Operational procedures

## 6. Testing Strategy

### 6.1 Unit Testing Requirements

#### 6.1.1 Service Layer Tests
```go
func TestGetPersonalStats(t *testing.T) {
    tests := []struct {
        name           string
        userIdentifier string
        expectedError  error
        setupData      func()
    }{
        {
            name:           "Valid Telegram ID",
            userIdentifier: "123456789",
            expectedError:  nil,
        },
        {
            name:           "Valid Username",
            userIdentifier: "@testuser",
            expectedError:  nil,
        },
        {
            name:           "User Not Found",
            userIdentifier: "nonexistent",
            expectedError:  ErrUserNotFound,
        },
        {
            name:           "Non-Admin Access",
            userIdentifier: "123456789",
            expectedError:  ErrNotAdmin,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Test implementation
        })
    }
}
```

#### 6.1.2 Data Accuracy Tests
- Verify financial calculation accuracy
- Test time zone handling for date-based aggregations
- Validate decimal precision for monetary values

#### 6.1.3 Privacy Compliance Tests
- Test admin permission enforcement
- Verify tenant data isolation
- Validate sensitive data masking

### 6.2 Integration Testing

#### 6.2.1 Admin Workflow Tests
- Complete admin center navigation flow
- User input validation and error handling
- Statistics display accuracy

#### 6.2.2 Performance Testing
- Load testing with multiple concurrent admin users
- Database query performance under load
- Memory usage and caching effectiveness

### 6.3 Security Testing

#### 6.3.1 Authorization Tests
- Non-admin access prevention
- Cross-tenant data access prevention
- Session management validation

#### 6.3.2 Data Protection Tests
- Audit logging verification
- Sensitive data masking validation
- Data retention compliance

## 7. Operational Considerations

### 7.1 Monitoring and Alerting
- Track admin personal statistics access frequency
- Monitor query performance and timeouts
- Alert on suspicious access patterns

### 7.2 Maintenance Procedures
- Regular cache cleanup
- Database optimization maintenance
- Audit log archival processes

### 7.3 Disaster Recovery
- Backup procedures for aggregated statistics
- Recovery procedures for admin access
- Data integrity verification processes

## 8. Technical Dependencies

### 8.1 External Dependencies
- Telegram Bot API for message handling
- Game provider APIs for betting statistics
- Database connection pools for performance

### 8.2 Internal Dependencies
- Admin service layer (`internal/service/admin.go`)
- Tenant management system
- User management system
- Transaction processing system

### 8.3 Configuration Requirements
- Database connection settings
- Cache configuration
- Admin permission settings
- Audit logging configuration

## 9. Handler Message Processing Integration

### 9.1 Message Processing Workflow

The Personal Statistics function requires user input to identify which user's statistics to display.

#### 9.1.1 Initial Callback Handler Flow
```go
// handlePersonalStats (current implementation)
1. Verify admin permission
2. Display instruction message requesting user identifier
3. Wait for user text message input (Telegram ID or username)
```

#### 9.1.2 Message Input Processing Flow (TO BE IMPLEMENTED)
```go
// Message handler for personal statistics query
1. Detect context: User in personal statistics mode
2. Parse message format: "telegram_id" or "@username" or "username"
3. Validate input format
4. Extract user identifier
5. Call service layer function
6. Display formatted statistics or error message
```

### 9.2 Input Parsing Patterns

#### 9.2.1 User Identifier Formats
```regex
// Pattern 1: Telegram ID (numeric)
^\d+$

// Pattern 2: Username with @ prefix
^@[a-zA-Z0-9_]+$

// Pattern 3: Username without @ prefix
^[a-zA-Z0-9_]+$

// Examples:
// 5322691835         → Telegram ID lookup
// @agoukuaile        → Username lookup (with @)
// agoukuaile         → Username lookup (without @)
```

### 9.3 Service Integration Contract

#### 9.3.1 Service Call Pattern
```go
// Query personal statistics
stats, err := service.Admin().GetPersonalStats(ctx, userIdentifier)

// Error handling patterns
switch {
case errors.Is(err, service.ErrNotAdmin):
    // Send permission denied message
case errors.Is(err, service.ErrUserNotFound):
    // Send user not found popup: "用户不存在"
case err != nil:
    // Send generic error message
default:
    // Display formatted statistics with action buttons
}
```

### 9.4 Response Formatting Pattern

#### 9.4.1 Statistics Display Template
```go
// Build comprehensive statistics message
text := fmt.Sprintf(`%s的总数据

平台ID：%s
TelegramID：%d

今日存款：%s        今日取款：%s
昨日存款：%s        昨日取款：%s
本月存款：%s        本月取款：%s
上月存款：%s        上月取款：%s

总存款：%s
总取款：%s
总余额：%s
总盈亏：%s

○ 今日游戏1流水：%s      ○ 今日游戏2流水：%s      ○ 今日游戏3流水：%s
昨日游戏1流水：%s         昨日游戏2流水：%s         昨日游戏3流水：%s

今日输赢：%s
剩余流水要求：%s

所有单位为CNY
用户状态：%s`,
    stats.FirstName, stats.PlatformID, stats.TelegramID,
    // ... all statistics fields
)

// Add action buttons keyboard
keyboard := BuildPersonalStatsKeyboard(ctx, stats.UserID)
```

#### 9.4.2 Action Buttons
```go
func BuildPersonalStatsKeyboard(ctx context.Context, userID uint64) tgbotapi.InlineKeyboardMarkup {
    return tgbotapi.NewInlineKeyboardMarkup(
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData(
                "查看Ta指定日期范围流水", 
                fmt.Sprintf("admin_user_flow_range:%d", userID),
            ),
        ),
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData(
                "查看Ta的注单", 
                fmt.Sprintf("admin_user_bets:%d", userID),
            ),
            tgbotapi.NewInlineKeyboardButtonData(
                "查看Ta的账变", 
                fmt.Sprintf("admin_user_transactions:%d", userID),
            ),
        ),
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData(
                "改变用户状态", 
                fmt.Sprintf("admin_toggle_user_status:%d", userID),
            ),
        ),
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData(
                "管理员中心", 
                "admin_center",
            ),
        ),
    )
}
```

### 9.5 State Management Requirements

#### 9.5.1 Tracking Query Context
```go
// Store admin state to track personal statistics mode
type AdminPersonalStatsState struct {
    AdminUserID int64
    Mode        string    // "awaiting_user_identifier"
    QueryUserID uint64    // Store after successful query
    ExpireAt    time.Time
}

// State transitions:
// 1. Click "Personal Statistics" → Set mode = "awaiting_user_identifier"
// 2. Valid user query → Set QueryUserID, change mode to "viewing_stats"
// 3. Click sub-function → Keep QueryUserID for context
// 4. Click "Admin Center" → Clear state
```

### 9.6 Sub-Function Handlers

#### 9.6.1 Toggle User Status Handler
```go
func handleToggleUserStatus(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
    // Extract user ID from callback data
    userID := extractUserIDFromCallback(callbackQuery.Data)
    
    // Call service to toggle status
    err := service.Admin().ToggleUserStatus(ctx, userID)
    if err != nil {
        return callback.NewAnswerCallback(callbackQuery.ID, "状态更改失败"), nil
    }
    
    // Get updated stats and refresh display
    stats, _ := service.Admin().GetPersonalStats(ctx, strconv.FormatUint(userID, 10))
    
    // Update message with new stats
    return &callback.EditMessageResponse{
        CallbackQueryID: callbackQuery.ID,
        ChatID:          callbackQuery.Message.Chat.ID,
        MessageID:       callbackQuery.Message.MessageID,
        Text:            BuildPersonalStatsMessage(ctx, stats),
        ParseMode:       "HTML",
        InlineKeyboard:  BuildPersonalStatsKeyboard(ctx, userID),
    }, nil
}
```

### 9.7 Integration Testing Requirements

#### 9.7.1 Query Flow Tests
```go
func TestPersonalStatsQueryFlow(t *testing.T) {
    // 1. Simulate callback "admin_personal_stats"
    // 2. Verify instruction message displayed
    // 3. Test various input formats:
    //    - Valid Telegram ID
    //    - Valid username with @
    //    - Valid username without @
    //    - Invalid formats
    //    - Non-existent users
    // 4. Verify statistics display accuracy
    // 5. Test action button functionality
}
```

#### 9.7.2 Privacy and Security Tests
- Admin permission verification
- Tenant data isolation
- Audit logging for queries
- Sensitive data handling

## Conclusion

The Personal Statistics Admin Function provides a comprehensive view of individual user activity while maintaining strict privacy controls and data protection standards. The implementation follows the existing system architecture and leverages established patterns for admin functionality, ensuring consistency and maintainability within the larger gaming bot platform.