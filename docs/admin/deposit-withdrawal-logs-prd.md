# 存取款日志管理功能技术产品需求文档 (PRD)

## 1. 项目概述

### 1.1 功能概述
管理员中心的存取款日志功能为系统管理员提供完整的平台资金流水查询、审计和监控能力。该功能支持查看所有租户下用户的存款和取款记录，提供实时的交易监控、合规审计和风险管控支持。

### 1.2 交易审计目的
- **合规监管**: 满足金融监管要求，提供完整的资金流动审计链
- **风险控制**: 实时监控异常交易模式，预防洗钱和欺诈行为
- **运营分析**: 提供数据支撑进行业务决策和用户行为分析
- **问题调查**: 快速定位和解决用户投诉及系统异常
- **财务对账**: 确保平台资金流水的准确性和完整性

## 2. 当前实现状态

### 2.1 存款日志功能状态
**当前状态**: 🟡 部分实现
- ✅ Service层接口定义 (`GetDepositLogs`)
- ✅ Handler callback处理器 (`handleDepositLogs`)
- ✅ 基础UI框架和权限验证
- ✅ i18n多语言支持
- ❌ 实际数据查询实现 (占位符状态)
- ❌ 分页和过滤功能
- ❌ 实时数据更新

### 2.2 取款日志功能状态
**当前状态**: 🟡 部分实现
- ✅ Service层接口定义 (`GetWithdrawLogs`)
- ✅ Handler callback处理器 (`handleWithdrawLogs`)
- ✅ 基础UI框架和权限验证
- ✅ i18n多语言支持
- ❌ 实际数据查询实现 (占位符状态)
- ❌ 分页和过滤功能
- ❌ 审核状态显示

### 2.3 核心数据结构
```go
// 当前LogResult结构体
type LogResult struct {
    Items      []any `json:"items"`       // 日志条目列表
    TotalCount int64 `json:"total_count"` // 总记录数
    Page       int   `json:"page"`        // 当前页码
    PageSize   int   `json:"page_size"`   // 每页大小
}
```

## 3. 技术规格说明

### 3.1 存款日志技术规格

#### 3.1.1 数据源映射
**主要数据表**: `user_recharges`
**关联表**: `users`, `tokens`, `tenants`

**核心字段映射**:
```go
type DepositLogItem struct {
    UserID              uint64          `json:"user_id"`               // 用户ID
    FirstName           string          `json:"first_name"`            // 用户名(firstname)
    TelegramID          int64           `json:"telegram_id"`           // Telegram ID
    TokenSymbol         string          `json:"token_symbol"`          // 币种符号
    Amount              decimal.Decimal `json:"amount"`                // 充值数量
    ConvertedAmount     decimal.Decimal `json:"converted_amount"`      // 折合CNY金额
    State               uint            `json:"state"`                 // 状态: 1-处理中, 2-已完成
    CreatedAt           *gtime.Time     `json:"created_at"`           // 充值时间
    CompletedAt         *gtime.Time     `json:"completed_at"`         // 完成时间
    TxHash              string          `json:"tx_hash"`              // 交易哈希
    RechargeType        string          `json:"recharge_type"`        // 充值类型
}
```

#### 3.1.2 查询优化策略
```sql
-- 优化索引建议
CREATE INDEX idx_user_recharges_tenant_time ON user_recharges(tenant_id, created_at DESC);
CREATE INDEX idx_user_recharges_state_time ON user_recharges(state, created_at DESC);
CREATE INDEX idx_user_recharges_amount ON user_recharges(converted_amount DESC);
```

### 3.2 取款日志技术规格

#### 3.2.1 数据源映射
**主要数据表**: `user_withdraws`
**关联表**: `users`, `tokens`, `tenants`

**核心字段映射**:
```go
type WithdrawLogItem struct {
    UserID              uint64          `json:"user_id"`               // 用户ID
    FirstName           string          `json:"first_name"`            // 用户名(firstname)  
    TelegramID          int64           `json:"telegram_id"`           // Telegram ID
    OrderNo             string          `json:"order_no"`              // 订单号
    Amount              decimal.Decimal `json:"amount"`                // 申请金额
    HandlingFee         decimal.Decimal `json:"handling_fee"`          // 手续费
    ActualAmount        decimal.Decimal `json:"actual_amount"`         // 实际到账
    State               uint            `json:"state"`                 // 状态码
    StateText           string          `json:"state_text"`            // 状态描述
    CreatedAt           *gtime.Time     `json:"created_at"`           // 申请时间
    CompletedAt         *gtime.Time     `json:"completed_at"`         // 完成时间
    TxHash              string          `json:"tx_hash"`              // 交易哈希
    RefuseReasonZh      string          `json:"refuse_reason_zh"`     // 拒绝原因
    AdminRemark         string          `json:"admin_remark"`         // 管理员备注
}
```

#### 3.2.2 状态映射定义
```go
var WithdrawStateMap = map[uint]string{
    1: "待审核(Pending)",
    2: "处理中(Processing)", 
    3: "已拒绝(Rejected)",
    4: "已完成(Completed)",
    5: "失败(Failed)",
}
```

### 3.3 数据库查询规格

#### 3.3.1 存款日志查询
```go
func (s *adminService) GetDepositLogs(ctx context.Context, page, pageSize int) (*LogResult, error) {
    // 获取租户ID
    tenantId, ok := tenant.GetTenantIdFromContext(ctx)
    if !ok {
        return nil, gerror.New("failed to get tenant ID")
    }

    // 计算偏移量
    offset := (page - 1) * pageSize
    
    // 主查询
    var deposits []DepositLogItem
    err := dao.UserRecharges.Ctx(ctx).
        LeftJoin("users u", "user_recharges.user_id = u.user_id").
        Where("user_recharges.tenant_id = ?", tenantId).
        OrderBy("user_recharges.created_at DESC").
        Limit(offset, pageSize).
        Fields(`
            user_recharges.user_id,
            u.first_name,
            u.telegram_id,
            user_recharges.token_symbol,
            user_recharges.amount,
            user_recharges.converted_amount,
            user_recharges.state,
            user_recharges.created_at,
            user_recharges.completed_at,
            user_recharges.tx_hash,
            user_recharges.rechange_type
        `).
        Scan(&deposits)
        
    if err != nil {
        return nil, gerror.Wrap(err, "failed to query deposit logs")
    }

    // 计算总数
    totalCount, err := dao.UserRecharges.Ctx(ctx).
        Where("tenant_id = ?", tenantId).
        Count()
    if err != nil {
        return nil, gerror.Wrap(err, "failed to count deposits")
    }

    return &LogResult{
        Items:      convertToInterface(deposits),
        TotalCount: int64(totalCount),
        Page:       page,
        PageSize:   pageSize,
    }, nil
}
```

#### 3.3.2 取款日志查询
```go
func (s *adminService) GetWithdrawLogs(ctx context.Context, page, pageSize int) (*LogResult, error) {
    // 获取租户ID
    tenantId, ok := tenant.GetTenantIdFromContext(ctx)
    if !ok {
        return nil, gerror.New("failed to get tenant ID")
    }

    // 计算偏移量  
    offset := (page - 1) * pageSize
    
    // 主查询
    var withdraws []WithdrawLogItem
    err := dao.UserWithdraws.Ctx(ctx).
        LeftJoin("users u", "user_withdraws.user_id = u.user_id").
        Where("user_withdraws.tenant_id = ?", tenantId).
        OrderBy("user_withdraws.created_at DESC").
        Limit(offset, pageSize).
        Fields(`
            user_withdraws.user_id,
            u.first_name,
            u.telegram_id,
            user_withdraws.order_no,
            user_withdraws.amount,
            user_withdraws.handling_fee,
            user_withdraws.actual_amount,
            user_withdraws.state,
            user_withdraws.created_at,
            user_withdraws.completed_at,
            user_withdraws.tx_hash,
            user_withdraws.refuse_reason_zh,
            user_withdraws.admin_remark
        `).
        Scan(&withdraws)
        
    if err != nil {
        return nil, gerror.Wrap(err, "failed to query withdraw logs")
    }

    // 状态文本映射
    for i := range withdraws {
        withdraws[i].StateText = WithdrawStateMap[withdraws[i].State]
    }

    // 计算总数
    totalCount, err := dao.UserWithdraws.Ctx(ctx).
        Where("tenant_id = ?", tenantId).
        Count()
    if err != nil {
        return nil, gerror.Wrap(err, "failed to count withdraws")  
    }

    return &LogResult{
        Items:      convertToInterface(withdraws),
        TotalCount: int64(totalCount),
        Page:       page,
        PageSize:   pageSize,
    }, nil
}
```

## 4. UI/UX设计规格

### 4.1 存款日志UI设计

#### 4.1.1 消息格式模板
```go
func BuildDepositLogsMessage(ctx context.Context, logs *LogResult) string {
    if len(logs.Items) == 0 {
        return service.I18n().T(ctx, "AdminDepositLogsNoData")
    }
    
    var builder strings.Builder
    builder.WriteString(service.I18n().T(ctx, "AdminDepositLogsHeader"))
    builder.WriteString("\n\n")
    builder.WriteString(service.I18n().Tf(ctx, "AdminCurrentTime", gtime.Now().Format("Y-m-d H:i:s")))
    builder.WriteString("\n\n")
    
    for _, item := range logs.Items {
        deposit := item.(DepositLogItem)
        builder.WriteString(fmt.Sprintf(
            "存款人：%s ID：%d\n存款金额：%s\n存款时间：%s\n\n",
            deposit.FirstName,
            deposit.TelegramID, 
            deposit.ConvertedAmount.String(),
            deposit.CreatedAt.Format("Y-m-d H:i:s"),
        ))
    }
    
    // 分页信息
    builder.WriteString(service.I18n().Tf(ctx, "AdminLogsPaginationInfo", 
        logs.Page, logs.TotalCount))
    
    return builder.String()
}
```

#### 4.1.2 分页键盘设计
```go
func BuildDepositLogsKeyboard(ctx context.Context, currentPage int, totalPages int) tgbotapi.InlineKeyboardMarkup {
    var rows [][]tgbotapi.InlineKeyboardButton
    
    // 分页按钮行
    if totalPages > 1 {
        var paginationRow []tgbotapi.InlineKeyboardButton
        
        if currentPage > 1 {
            paginationRow = append(paginationRow, tgbotapi.NewInlineKeyboardButtonData(
                "⬅️ "+service.I18n().T(ctx, "AdminButtonPrev"),
                fmt.Sprintf("admin_deposit_logs_page_%d", currentPage-1),
            ))
        }
        
        if currentPage < totalPages {
            paginationRow = append(paginationRow, tgbotapi.NewInlineKeyboardButtonData(
                service.I18n().T(ctx, "AdminButtonNext")+" ➡️",
                fmt.Sprintf("admin_deposit_logs_page_%d", currentPage+1),
            ))
        }
        
        if len(paginationRow) > 0 {
            rows = append(rows, paginationRow)
        }
    }
    
    // 功能按钮行
    rows = append(rows, []tgbotapi.InlineKeyboardButton{
        tgbotapi.NewInlineKeyboardButtonData(
            "🔄 "+service.I18n().T(ctx, "AdminButtonRefresh"),
            "admin_deposit_logs_refresh",
        ),
    })
    
    // 返回按钮
    rows = append(rows, []tgbotapi.InlineKeyboardButton{
        tgbotapi.NewInlineKeyboardButtonData(
            "🔙 "+service.I18n().T(ctx, "AdminButtonBackToAdmin"),
            "admin_center",
        ),
    })
    
    return tgbotapi.NewInlineKeyboardMarkup(rows...)
}
```

### 4.2 取款日志UI设计

#### 4.2.1 消息格式模板
```go
func BuildWithdrawLogsMessage(ctx context.Context, logs *LogResult) string {
    if len(logs.Items) == 0 {
        return service.I18n().T(ctx, "AdminWithdrawLogsNoData")
    }
    
    var builder strings.Builder
    builder.WriteString(service.I18n().T(ctx, "AdminWithdrawLogsHeader"))
    builder.WriteString("\n\n")
    builder.WriteString(service.I18n().Tf(ctx, "AdminCurrentTime", gtime.Now().Format("Y-m-d H:i:s")))
    builder.WriteString("\n\n")
    
    for _, item := range logs.Items {
        withdraw := item.(WithdrawLogItem)
        
        builder.WriteString(fmt.Sprintf(
            "取款人：%s ID：%d\n取款金额：%s\n取款时间：%s\n管理员反馈：%s\n",
            withdraw.FirstName,
            withdraw.TelegramID,
            withdraw.Amount.String(),
            withdraw.CreatedAt.Format("Y-m-d H:i:s"),
            withdraw.StateText,
        ))
        
        // 显示拒绝原因或交易哈希
        if withdraw.State == 3 && withdraw.RefuseReasonZh != "" {
            builder.WriteString(fmt.Sprintf("备注：%s\n", withdraw.RefuseReasonZh))
        } else if withdraw.State == 4 && withdraw.TxHash != "" {
            builder.WriteString(fmt.Sprintf("备注：https://tronscan.org/#/transaction/%s\n", withdraw.TxHash))
        }
        
        builder.WriteString("\n")
    }
    
    // 分页信息
    builder.WriteString(service.I18n().Tf(ctx, "AdminLogsPaginationInfo", 
        logs.Page, logs.TotalCount))
    
    return builder.String()
}
```

### 4.3 导航和用户体验

#### 4.3.1 分页导航逻辑
- **每页显示**: 20条记录 (符合需求文档)
- **分页策略**: 仅在多页时显示导航按钮
- **导航智能**: 首页只显示"下一页"，末页只显示"上一页"
- **中间页**: 同时显示"上一页"和"下一页"

#### 4.3.2 实时更新机制
- **刷新按钮**: 手动刷新获取最新数据
- **自动更新**: 暂时不实现，避免影响用户浏览
- **状态提示**: 加载过程中显示"正在查询"提示

## 5. 高级功能规格

### 5.1 过滤和搜索功能

#### 5.1.1 过滤器设计 (未来扩展)
```go
type LogFilter struct {
    DateFrom    *gtime.Time     `json:"date_from"`    // 开始日期
    DateTo      *gtime.Time     `json:"date_to"`      // 结束日期
    UserID      uint64          `json:"user_id"`      // 用户ID过滤
    MinAmount   decimal.Decimal `json:"min_amount"`   // 最小金额
    MaxAmount   decimal.Decimal `json:"max_amount"`   // 最大金额
    State       uint            `json:"state"`        // 状态过滤
    TokenSymbol string          `json:"token_symbol"` // 币种过滤
}
```

#### 5.1.2 搜索功能 (未来扩展)
- 按用户名或Telegram ID搜索
- 按订单号搜索 (取款日志)
- 按交易哈希搜索
- 按金额范围搜索

### 5.2 导出功能 (未来扩展)

#### 5.2.1 Excel导出
```go
func (s *adminService) ExportDepositLogs(ctx context.Context, filter *LogFilter) ([]byte, error) {
    // 导出Excel格式的存款日志
    // 包含所有查询字段和统计信息
}

func (s *adminService) ExportWithdrawLogs(ctx context.Context, filter *LogFilter) ([]byte, error) {
    // 导出Excel格式的取款日志  
    // 包含审核状态和备注信息
}
```

#### 5.2.2 PDF报表 (未来扩展)
- 生成带有图表的PDF报表
- 包含汇总统计和趋势分析
- 支持自定义报表模板

## 6. 性能考虑和优化

### 6.1 数据库优化策略

#### 6.1.1 索引优化
```sql
-- 存款日志优化索引
CREATE INDEX idx_user_recharges_admin_query ON user_recharges(
    tenant_id, created_at DESC, state
);

-- 取款日志优化索引  
CREATE INDEX idx_user_withdraws_admin_query ON user_withdraws(
    tenant_id, created_at DESC, state
);

-- 用户关联索引
CREATE INDEX idx_users_lookup ON users(user_id, first_name, telegram_id);
```

#### 6.1.2 查询优化
- **分页查询**: 使用LIMIT和OFFSET进行分页
- **JOIN优化**: 使用LEFT JOIN避免数据丢失
- **字段选择**: 只查询必要字段，避免SELECT *
- **缓存策略**: 对统计数据实施缓存

### 6.2 内存和性能管理

#### 6.2.1 分页性能
- **默认页大小**: 20条记录 (平衡加载速度和信息密度)
- **最大页大小**: 100条记录 (防止内存过载)
- **查询超时**: 30秒数据库查询超时
- **连接池**: 使用数据库连接池管理

#### 6.2.2 缓存策略
```go
// 统计数据缓存 (5分钟有效期)
type LogStats struct {
    TotalDeposits  int64           `json:"total_deposits"`
    TotalWithdraws int64           `json:"total_withdraws"`
    TodayDeposits  decimal.Decimal `json:"today_deposits"`
    TodayWithdraws decimal.Decimal `json:"today_withdraws"`
    CachedAt       *gtime.Time     `json:"cached_at"`
}
```

### 6.3 实时性能监控

#### 6.3.1 性能指标
- **查询响应时间**: 目标 < 2秒
- **内存使用**: 单次查询 < 50MB
- **并发处理**: 支持10个管理员同时查询
- **数据一致性**: 数据延迟 < 5分钟

#### 6.3.2 监控告警
- 查询时间超过5秒触发告警
- 数据库连接数超过阈值告警
- 内存使用率超过80%告警

## 7. 管理员工作流程

### 7.1 日常监控工作流

#### 7.1.1 存款监控流程
1. **定期查看**: 每2小时查看存款日志
2. **异常识别**: 检查大额存款(>$10,000)和异常频率
3. **状态验证**: 确认处理中的存款是否及时完成
4. **问题升级**: 发现异常立即记录并上报

#### 7.1.2 取款审核流程  
1. **实时审核**: 查看待审核取款申请
2. **风险评估**: 评估用户历史和取款模式
3. **合规检查**: 验证KYC和反洗钱要求
4. **决策记录**: 记录审核决策和原因

### 7.2 调查和审计工作流

#### 7.2.1 用户投诉处理
1. **快速定位**: 使用用户ID或时间范围快速查找
2. **交叉验证**: 对比存取款记录和账户变化
3. **证据收集**: 截图保存相关日志记录
4. **问题解决**: 根据调查结果进行账户调整

#### 7.2.2 合规审计支持
1. **数据导出**: 生成指定时间段的完整记录
2. **报表生成**: 创建监管要求的格式化报表
3. **文档整理**: 维护审计跟踪链
4. **合规报告**: 定期提交合规统计报告

## 8. 实施路线图

### 8.1 第一阶段 - 核心功能实现 (2周)

#### Week 1: 基础数据查询
- [ ] 完善`GetDepositLogs`方法实现
- [ ] 完善`GetWithdrawLogs`方法实现  
- [ ] 实现基础分页功能
- [ ] 添加错误处理和日志记录
- [ ] 单元测试编写

#### Week 2: UI/UX完善
- [ ] 实现消息格式化函数
- [ ] 完善分页键盘逻辑
- [ ] 添加状态文本映射
- [ ] 集成i18n翻译
- [ ] 用户体验测试

### 8.2 第二阶段 - 性能优化 (1周)

#### Week 3: 优化和监控
- [ ] 数据库索引优化
- [ ] 查询性能调优
- [ ] 添加缓存机制
- [ ] 性能监控集成
- [ ] 压力测试

### 8.3 第三阶段 - 高级功能 (2周)

#### Week 4-5: 扩展功能 (可选)
- [ ] 过滤器功能实现
- [ ] 搜索功能开发
- [ ] 导出功能集成
- [ ] 实时更新机制
- [ ] 管理员操作审计

## 9. 测试策略

### 9.1 功能测试

#### 9.1.1 单元测试
```go
func TestGetDepositLogs(t *testing.T) {
    // 测试正常查询
    // 测试分页功能
    // 测试权限验证
    // 测试错误处理
}

func TestGetWithdrawLogs(t *testing.T) {
    // 测试正常查询
    // 测试状态映射
    // 测试空数据处理
    // 测试性能边界
}
```

#### 9.1.2 集成测试
- 端到端Telegram交互测试
- 数据库连接和查询测试
- 多租户数据隔离测试
- 并发访问测试

### 9.2 性能测试

#### 9.2.1 负载测试
- 单管理员大量数据查询测试
- 多管理员并发查询测试
- 数据库连接池压力测试
- 内存使用性能测试

#### 9.2.2 压力测试
- 极限数据量查询测试 (>100万记录)
- 高并发用户访问测试 (>50并发)
- 长时间运行稳定性测试
- 异常情况恢复测试

### 9.3 安全测试

#### 9.3.1 权限测试
- 非管理员访问阻止测试
- 跨租户数据隔离测试
- 会话超时处理测试
- 权限提升攻击防护测试

#### 9.3.2 数据安全测试
- SQL注入防护测试
- 敏感数据脱敏测试
- 审计日志完整性测试
- 数据传输加密测试

## 10. 运维和监控

### 10.1 系统监控

#### 10.1.1 关键指标监控
```go
type LogMetrics struct {
    QueryResponseTime   time.Duration `json:"query_response_time"`   // 查询响应时间
    ConcurrentQueries   int           `json:"concurrent_queries"`    // 并发查询数
    DatabaseConnections int           `json:"database_connections"`  // 数据库连接数
    MemoryUsage        int64          `json:"memory_usage"`          // 内存使用量
    ErrorRate          float64        `json:"error_rate"`            // 错误率
}
```

#### 10.1.2 告警规则
- 查询响应时间 > 5秒: 高级告警
- 错误率 > 5%: 紧急告警  
- 数据库连接数 > 80%: 警告告警
- 内存使用 > 90%: 紧急告警

### 10.2 日志管理

#### 10.2.1 操作审计日志
```go
type AdminOperationLog struct {
    AdminID     uint64      `json:"admin_id"`     // 管理员ID
    Operation   string      `json:"operation"`    // 操作类型
    Parameters  interface{} `json:"parameters"`   // 操作参数
    ResultCount int64       `json:"result_count"` // 结果数量
    Duration    int64       `json:"duration"`     // 执行时长(毫秒)
    Timestamp   *gtime.Time `json:"timestamp"`    // 操作时间
    IPAddress   string      `json:"ip_address"`   // IP地址
    UserAgent   string      `json:"user_agent"`   // 用户代理
}
```

#### 10.2.2 系统日志策略
- **访问日志**: 记录所有管理员查询操作
- **错误日志**: 记录所有系统错误和异常
- **性能日志**: 记录慢查询和性能瓶颈
- **安全日志**: 记录权限验证和安全事件

### 10.3 备份和恢复

#### 10.3.1 数据备份策略
- **实时备份**: 核心交易数据实时备份
- **增量备份**: 每小时增量备份
- **全量备份**: 每日全量备份
- **异地备份**: 重要数据异地备份

#### 10.3.2 灾难恢复计划
- **RTO目标**: 系统恢复时间 < 4小时
- **RPO目标**: 数据恢复点 < 1小时
- **备用系统**: 准备热备用系统
- **故障切换**: 自动故障检测和切换

## 11. 合规和安全要求

### 11.1 数据保护合规

#### 11.1.1 隐私保护
- **数据脱敏**: 敏感信息在日志中脱敏处理
- **访问控制**: 严格的管理员权限控制
- **审计跟踪**: 完整的数据访问审计链
- **数据保留**: 符合法规要求的数据保留政策

#### 11.1.2 金融监管合规
- **AML合规**: 反洗钱监控和报告
- **KYC要求**: 用户身份验证记录
- **交易监控**: 可疑交易识别和报告
- **监管报告**: 定期向监管机构提交报告

### 11.2 安全防护措施

#### 11.2.1 访问安全
- **多因素认证**: 管理员账户MFA保护
- **会话管理**: 安全的会话超时和管理
- **IP白名单**: 限制管理员访问IP范围
- **操作审计**: 所有管理员操作记录审计

#### 11.2.2 数据安全
- **传输加密**: 数据传输TLS加密
- **存储加密**: 敏感数据存储加密
- **访问日志**: 完整的数据访问日志
- **数据完整性**: 数据完整性验证机制

## 12. Handler Message Processing Integration

### 12.1 Message Processing Workflow

The Deposit/Withdrawal Logs function requires interactive filtering and pagination for transaction log viewing.

#### 12.1.1 Initial Callback Handler Flow
```go
// handleDepositLogs / handleWithdrawLogs (current implementation)
1. Verify admin permission
2. Display initial logs page with default filters
3. Show filter options and pagination controls
4. Wait for filter input or pagination navigation
```

#### 12.1.2 Filter Selection Flow (TO BE IMPLEMENTED)
```go
// Callback handlers for filter options
case "admin_deposit_logs_filter":
    return showFilterOptions(ctx, callbackQuery, "deposit")
case "admin_withdraw_logs_filter":
    return showFilterOptions(ctx, callbackQuery, "withdraw")
case "admin_logs_filter_user":
    return requestUserFilter(ctx, callbackQuery)
case "admin_logs_filter_date":
    return requestDateFilter(ctx, callbackQuery)
case "admin_logs_filter_amount":
    return requestAmountFilter(ctx, callbackQuery)
case "admin_logs_filter_status":
    return showStatusFilterOptions(ctx, callbackQuery)
```

#### 12.1.3 Filter Input Processing (TO BE IMPLEMENTED)
```go
// Message handler for various filter inputs
1. Detect context: User in logs filter mode
2. Parse filter type and input:
   - User: "@username" or "telegram_id"
   - Date: "YYYY-MM-DD" or "YYYY-MM-DD YYYY-MM-DD"
   - Amount: "min-max" or ">amount" or "<amount"
3. Validate input format
4. Apply filter to current query
5. Re-query with filters and display results
```

### 12.2 Input Parsing Patterns

#### 12.2.1 User Filter Formats
```regex
// Pattern 1: Username with @
^@[a-zA-Z0-9_]+$

// Pattern 2: Telegram ID
^\d+$

// Examples:
// @agoukuaile        → Filter by username
// 5322691835         → Filter by Telegram ID
```

#### 12.2.2 Date Filter Formats
```regex
// Pattern 1: Single date (transactions on this date)
^(\d{4}-\d{2}-\d{2})$

// Pattern 2: Date range
^(\d{4}-\d{2}-\d{2})\s+(\d{4}-\d{2}-\d{2})$

// Examples:
// 2025-01-20               → Transactions on Jan 20
// 2025-01-01 2025-01-31    → Transactions in January
```

#### 12.2.3 Amount Filter Formats
```regex
// Pattern 1: Range
^(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)$

// Pattern 2: Greater than
^>(\d+(?:\.\d+)?)$

// Pattern 3: Less than
^<(\d+(?:\.\d+)?)$

// Examples:
// 100-500         → Amount between 100 and 500
// >1000           → Amount greater than 1000
// <50             → Amount less than 50
```

### 12.3 Service Integration Contract

#### 12.3.1 Service Call Pattern
```go
// Query logs with filters and pagination
logs, err := service.Admin().GetDepositLogs(ctx, &LogQueryParams{
    UserID:      userFilter,
    StartDate:   startDate,
    EndDate:     endDate,
    MinAmount:   minAmount,
    MaxAmount:   maxAmount,
    Status:      statusFilter,
    Page:        currentPage,
    PageSize:    pageSize,
})

// Error handling patterns
switch {
case errors.Is(err, service.ErrNotAdmin):
    // Send permission denied message
case errors.Is(err, service.ErrInvalidFilter):
    // Send filter error popup
case err != nil:
    // Send generic error message
default:
    // Display formatted logs with pagination
}
```

### 12.4 Response Formatting Patterns

#### 12.4.1 Deposit Logs Display Template
```go
// Build deposit logs message
text := fmt.Sprintf(`📥 存款日志

当前过滤器：%s

%s

汇总：
记录数：%d
总金额：%s CNY

页面 %d/%d`,
    formatActiveFilters(filters),
    formatDepositEntries(logs.Items),
    logs.TotalCount,
    totalAmount,
    currentPage,
    totalPages,
)

// Format individual deposit entries
func formatDepositEntries(items []DepositLog) string {
    var entries []string
    for _, item := range items {
        entry := fmt.Sprintf(`#%s | %s
用户：%s (@%s)
金额：%s CNY
状态：%s
时间：%s`,
            item.OrderNo,
            item.PaymentMethod,
            item.FirstName,
            item.Account,
            item.Amount,
            formatDepositStatus(item.State),
            item.CreatedAt.String(),
        )
        entries = append(entries, entry)
    }
    return strings.Join(entries, "\n\n---\n\n")
}
```

#### 12.4.2 Withdrawal Logs Display Template
```go
// Build withdrawal logs message  
text := fmt.Sprintf(`📤 取款日志

当前过滤器：%s

%s

汇总：
记录数：%d
总金额：%s CNY
待审核：%d

页面 %d/%d`,
    formatActiveFilters(filters),
    formatWithdrawEntries(logs.Items),
    logs.TotalCount,
    totalAmount,
    pendingCount,
    currentPage,
    totalPages,
)
```

#### 12.4.3 Log Navigation Keyboard
```go
func BuildLogNavigationKeyboard(ctx context.Context, logType string, page, totalPages int, filters map[string]string) tgbotapi.InlineKeyboardMarkup {
    var rows [][]tgbotapi.InlineKeyboardButton
    
    // Filter buttons row
    rows = append(rows, []tgbotapi.InlineKeyboardButton{
        tgbotapi.NewInlineKeyboardButtonData("🔍 过滤器", fmt.Sprintf("admin_%s_logs_filter", logType)),
        tgbotapi.NewInlineKeyboardButtonData("🔄 刷新", fmt.Sprintf("admin_%s_logs_refresh:%d", logType, page)),
        tgbotapi.NewInlineKeyboardButtonData("❌ 清除过滤", fmt.Sprintf("admin_%s_logs_clear_filter", logType)),
    })
    
    // Pagination row
    if totalPages > 1 {
        var paginationRow []tgbotapi.InlineKeyboardButton
        
        if page > 1 {
            paginationRow = append(paginationRow,
                tgbotapi.NewInlineKeyboardButtonData("⬅️ 上一页", 
                    fmt.Sprintf("admin_%s_logs_page:%d", logType, page-1)),
            )
        }
        
        paginationRow = append(paginationRow,
            tgbotapi.NewInlineKeyboardButtonData(fmt.Sprintf("%d/%d", page, totalPages), "noop"),
        )
        
        if page < totalPages {
            paginationRow = append(paginationRow,
                tgbotapi.NewInlineKeyboardButtonData("下一页 ➡️", 
                    fmt.Sprintf("admin_%s_logs_page:%d", logType, page+1)),
            )
        }
        
        rows = append(rows, paginationRow)
    }
    
    // Export and navigation buttons
    rows = append(rows,
        []tgbotapi.InlineKeyboardButton{
            tgbotapi.NewInlineKeyboardButtonData("📥 导出", fmt.Sprintf("admin_%s_logs_export", logType)),
        },
        []tgbotapi.InlineKeyboardButton{
            tgbotapi.NewInlineKeyboardButtonData("管理员中心", "admin_center"),
        },
    )
    
    return tgbotapi.NewInlineKeyboardMarkup(rows...)
}
```

### 12.5 State Management Requirements

#### 12.5.1 Log Viewing Context
```go
// Store log viewing state with filters
type AdminLogViewState struct {
    AdminUserID int64
    LogType     string              // "deposit" or "withdraw"
    Mode        string              // "viewing", "filtering_user", "filtering_date", etc.
    Filters     map[string]string   // Active filters
    CurrentPage int
    ExpireAt    time.Time
}

// State transitions:
// 1. Click "Deposit/Withdraw Logs" → Initialize viewing state
// 2. Click "Filter" → Show filter options
// 3. Select filter type → Set mode to "filtering_X"
// 4. Input filter value → Apply filter and refresh
// 5. Navigate pages → Update CurrentPage
// 6. Clear filters → Reset to default view
```

### 12.6 Export Functionality

#### 12.6.1 Log Export Handler
```go
func handleExportLogs(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
    // Get current filters from state
    state := getAdminLogState(ctx, callbackQuery.From.ID)
    
    // Generate CSV export
    fileData, fileName, err := service.Admin().ExportLogs(ctx, state.LogType, state.Filters)
    if err != nil {
        return callback.NewAnswerCallback(callbackQuery.ID, "导出失败"), nil
    }
    
    // Send file to admin
    return &callback.SendDocumentResponse{
        CallbackQueryID: callbackQuery.ID,
        ChatID:          callbackQuery.Message.Chat.ID,
        FileData:        fileData,
        FileName:        fileName,
        Caption:         fmt.Sprintf("%s导出\n过滤器：%s\n记录数：%d", 
            getLogTypeTitle(state.LogType),
            formatActiveFilters(state.Filters),
            state.TotalRecords,
        ),
    }, nil
}
```

### 12.7 Integration Testing Requirements

#### 12.7.1 Filter Functionality Tests
```go
func TestLogFilteringFlow(t *testing.T) {
    // 1. Test user filter (username and telegram ID)
    // 2. Test date filter (single date and range)
    // 3. Test amount filter (range, greater than, less than)
    // 4. Test status filter for withdrawals
    // 5. Test combined filters
    // 6. Test filter clearing
    // 7. Test pagination with filters
}
```

#### 12.7.2 Performance Tests
- Large dataset pagination
- Complex filter combinations
- Export performance
- Concurrent admin access

---

## 附录

### A. 错误码定义
```go
const (
    ErrCodeAdminPermissionDenied = "ADMIN_001" // 管理员权限不足
    ErrCodeDatabaseQueryFailed   = "ADMIN_002" // 数据库查询失败
    ErrCodeInvalidPageParams     = "ADMIN_003" // 分页参数无效
    ErrCodeDataNotFound          = "ADMIN_004" // 数据不存在
    ErrCodeSystemError           = "ADMIN_005" // 系统错误
)
```

### B. 配置参数
```go
type AdminLogsConfig struct {
    DefaultPageSize    int           `json:"default_page_size"`    // 默认页大小: 20
    MaxPageSize        int           `json:"max_page_size"`        // 最大页大小: 100
    QueryTimeout       time.Duration `json:"query_timeout"`       // 查询超时: 30s
    CacheExpiration    time.Duration `json:"cache_expiration"`    // 缓存过期: 5m
    EnablePerformanceLog bool        `json:"enable_perf_log"`     // 性能日志开关
}
```

### C. i18n翻译键
```toml
# 新增翻译键建议
AdminCurrentTime = "当前时间: %s"
AdminDepositLogsTitle = "📥 存款日志"
AdminWithdrawLogsTitle = "📤 取款日志"
AdminLogQueryTime = "查询耗时: %dms"
AdminLogTotalAmount = "总金额: %s"
AdminLogExportSuccess = "导出成功"
AdminLogFilterApplied = "已应用过滤器"
```

### D. 性能基准测试结果模板
```
基准测试环境:
- CPU: [待测试]
- Memory: [待测试]  
- Database: [待测试]

性能指标:
- 20条记录查询: < 500ms
- 100条记录查询: < 1s
- 1000条记录查询: < 3s
- 并发10用户: < 2s
- 数据库连接池: 20连接
- 内存使用峰值: < 100MB
```

---

*本PRD文档版本: 1.0*  
*创建日期: 2025-01-20*  
*预计完成时间: 2025-02-05*