# Merchant Information Admin Function - Technical PRD

## 1. Executive Summary

### 1.1 Product Overview
The Merchant Information Admin Function provides comprehensive merchant data management capabilities within the Telegram gaming bot's admin center. This feature enables authorized administrators to view, monitor, and manage merchant configurations, financial summaries, and operational metrics.

### 1.2 Primary Objectives
- **Merchant Visibility**: Provide complete merchant profile and configuration overview
- **Financial Monitoring**: Real-time tracking of merchant balance and transaction fees
- **Administrative Control**: Enable merchant setting management and status monitoring
- **Security Compliance**: Ensure secure access to merchant-sensitive information
- **Operational Efficiency**: Streamline merchant information access for admin operations

### 1.3 Success Metrics
- Admin access time to merchant information < 2 seconds
- 100% accuracy in merchant financial data display
- Zero unauthorized access to merchant information
- Complete audit trail for all merchant data access

## 2. Current Implementation Status

### 2.1 Existing Infrastructure
- **Service Layer**: `internal/service/admin.go` - Basic `GetMerchantInfo()` placeholder
- **Callback Handler**: `internal/admin/handler_callback.go` - `handleMerchantInfo()` implementation
- **Response Builder**: `internal/admin/responses.go` - `BuildMerchantInfoMessage()` function
- **Data Model**: `service.MerchantInfo` struct with basic fields
- **Security Layer**: Admin permission verification via `IsCurrentUserAdmin()`
- **I18n Support**: Chinese localization keys in `zh-CN.toml`

### 2.2 Current Functionality
```go
// Existing MerchantInfo structure
type MerchantInfo struct {
    MerchantID   uint64 `json:"merchant_id"`
    MerchantName string `json:"merchant_name"`
    // Additional fields needed for comprehensive merchant management
}
```

### 2.3 Implementation Gaps
- Placeholder implementation returning static data
- Missing comprehensive merchant data fields
- No database integration for merchant statistics
- Limited merchant configuration display
- No merchant balance and fee tracking

## 3. Technical Specifications

### 3.1 Enhanced Data Model

#### 3.1.1 Comprehensive MerchantInfo Structure
```go
type MerchantInfo struct {
    // Basic Information
    MerchantID       uint64          `json:"merchant_id"`
    MerchantName     string          `json:"merchant_name"`
    TenantID         uint64          `json:"tenant_id"`
    BusinessName     string          `json:"business_name"`
    
    // Financial Data
    TotalBalance     decimal.Decimal `json:"total_balance"`
    Currency         string          `json:"currency"`
    
    // Fee Statistics
    TodayWithdrawFees     decimal.Decimal `json:"today_withdraw_fees"`
    YesterdayWithdrawFees decimal.Decimal `json:"yesterday_withdraw_fees"`
    TotalWithdrawFees     decimal.Decimal `json:"total_withdraw_fees"`
    
    // Operational Status
    Status           int             `json:"status"`
    Level            uint            `json:"level"`
    
    // Configuration
    BettingBonusRate decimal.Decimal `json:"betting_bonus_rate"`
    
    // Contact Information
    TelegramAccount  int64           `json:"telegram_account"`
    TelegramBotName  string          `json:"telegram_bot_name"`
    Customer         string          `json:"customer"`
    Group            string          `json:"group"`
    
    // Timestamps
    CreatedAt        *gtime.Time     `json:"created_at"`
    UpdatedAt        *gtime.Time     `json:"updated_at"`
}
```

#### 3.1.2 Additional Statistics Structures
```go
type MerchantStats struct {
    // User Statistics
    TotalUsers       int64           `json:"total_users"`
    ActiveUsers      int64           `json:"active_users"`
    
    // Financial Summary
    TotalUserBalances decimal.Decimal `json:"total_user_balances"`
    TotalGameBalances decimal.Decimal `json:"total_game_balances"`
    
    // Today's Activity
    TodayDeposits    decimal.Decimal `json:"today_deposits"`
    TodayWithdraws   decimal.Decimal `json:"today_withdraws"`
    
    // Historical Data
    ThisMonthVolume  decimal.Decimal `json:"this_month_volume"`
    LastMonthVolume  decimal.Decimal `json:"last_month_volume"`
}
```

### 3.2 Database Integration

#### 3.2.1 Primary Data Sources
```sql
-- Tenant/Merchant basic information
SELECT 
    tenant_id,
    tenant_name,
    business_name,
    telegram_account,
    telegram_bot_name,
    customer,
    `group`,
    betting_bonus_rate,
    status,
    level,
    created_at,
    updated_at
FROM tenants 
WHERE tenant_id = ? AND status = 1 AND deleted_at IS NULL;

-- User balance aggregation
SELECT 
    SUM(w.balance) as total_user_balances,
    COUNT(DISTINCT u.user_id) as total_users
FROM users u
JOIN wallets w ON u.user_id = w.user_id
WHERE u.tenant_id = ? AND u.deleted_at IS NULL;

-- Withdrawal fee calculations
SELECT 
    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN fee_amount ELSE 0 END) as today_fees,
    SUM(CASE WHEN DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY) THEN fee_amount ELSE 0 END) as yesterday_fees,
    SUM(fee_amount) as total_fees
FROM user_withdraws 
WHERE tenant_id = ? AND status = 'completed';
```

#### 3.2.2 Game Balance Integration
```go
// Integration with game providers for total game balances
func (s *adminService) getGameBalances(ctx context.Context, tenantID uint64) (decimal.Decimal, error) {
    var totalGameBalance decimal.Decimal
    
    // Query game_sessions or game provider APIs
    // Implementation depends on game provider integration
    
    return totalGameBalance, nil
}
```

### 3.3 Service Layer Implementation

#### 3.3.1 Enhanced GetMerchantInfo Method
```go
func (s *adminService) GetMerchantInfo(ctx context.Context) (*MerchantInfo, error) {
    // Verify admin permission
    isAdmin, err := s.IsCurrentUserAdmin(ctx)
    if err != nil {
        return nil, err
    }
    if !isAdmin {
        return nil, ErrNotAdmin
    }
    
    // Get tenant information
    tenant, err := s.getCurrentTenantData(ctx)
    if err != nil {
        return nil, err
    }
    
    // Get merchant statistics
    stats, err := s.getMerchantStats(ctx, tenant.TenantId)
    if err != nil {
        return nil, err
    }
    
    // Get withdrawal fees
    fees, err := s.getWithdrawalFees(ctx, tenant.TenantId)
    if err != nil {
        return nil, err
    }
    
    // Build comprehensive merchant info
    return &MerchantInfo{
        MerchantID:            tenant.TenantId,
        MerchantName:          tenant.TenantName,
        TenantID:              tenant.TenantId,
        BusinessName:          tenant.BusinessName,
        TotalBalance:          stats.TotalUserBalances.Add(stats.TotalGameBalances),
        Currency:              "CNY", // From system config
        TodayWithdrawFees:     fees.TodayFees,
        YesterdayWithdrawFees: fees.YesterdayFees,
        TotalWithdrawFees:     fees.TotalFees,
        Status:                tenant.Status,
        Level:                 tenant.Level,
        BettingBonusRate:      tenant.BettingBonusRate,
        TelegramAccount:       tenant.TelegramAccount,
        TelegramBotName:       tenant.TelegramBotName,
        Customer:              tenant.Customer,
        Group:                 tenant.Group,
        CreatedAt:             tenant.CreatedAt,
        UpdatedAt:             tenant.UpdatedAt,
    }, nil
}
```

## 4. UI/UX Design Specifications

### 4.1 Message Format Requirements

#### 4.1.1 Primary Display Format
Based on the Chinese requirements document, the merchant info should display:

```
🏪 商户信息

商户名：Gaming Bot
总余额：12333.2222CNY

今日取款手续费：4.23CNY
昨日取款手续费：4.44CNY
所有取款手续费：2333.33CNY

【管理员中心】
```

#### 4.1.2 Enhanced Display Format
```go
func BuildMerchantInfoMessage(ctx context.Context, info *MerchantInfo) string {
    if info == nil {
        return service.I18n().T(ctx, "AdminMerchantInfoNoData")
    }
    
    return fmt.Sprintf(`🏪 商户信息

商户名：%s
业务名称：%s
商户ID：%d

💰 财务信息
总余额：%s %s
今日取款手续费：%s %s
昨日取款手续费：%s %s
所有取款手续费：%s %s

⚙️ 配置信息
状态：%s
等级：%d
返水比例：%s%%
客服：%s
官方群组：%s

📅 时间信息
创建时间：%s
最后更新：%s`,
        info.MerchantName,
        info.BusinessName,
        info.MerchantID,
        info.TotalBalance.StringFixed(2),
        info.Currency,
        info.TodayWithdrawFees.StringFixed(2),
        info.Currency,
        info.YesterdayWithdrawFees.StringFixed(2),
        info.Currency,
        info.TotalWithdrawFees.StringFixed(2),
        info.Currency,
        getStatusText(ctx, info.Status),
        info.Level,
        info.BettingBonusRate.Mul(decimal.NewFromInt(100)).StringFixed(2),
        info.Customer,
        info.Group,
        info.CreatedAt.String(),
        info.UpdatedAt.String())
}
```

### 4.2 Interactive Elements

#### 4.2.1 Action Buttons
```go
func BuildMerchantInfoKeyboard(ctx context.Context) tgbotapi.InlineKeyboardMarkup {
    return tgbotapi.NewInlineKeyboardMarkup(
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData(
                service.I18n().T(ctx, "AdminButtonRefresh"), 
                "admin_merchant_info_refresh"),
        ),
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData(
                service.I18n().T(ctx, "AdminButtonBackToAdmin"), 
                "admin_center"),
        ),
    )
}
```

#### 4.2.2 Real-time Updates
- **Refresh Button**: Manual data refresh capability
- **Auto-refresh**: Every 5 minutes for critical financial data
- **Status Indicators**: Visual status representation (🟢 Active, 🔴 Disabled)

## 5. Database Schema Requirements

### 5.1 Existing Schema Enhancement

#### 5.1.1 Tenants Table (Already Exists)
The existing `tenants` table provides the foundation for merchant information:
- `tenant_id` - Unique merchant identifier
- `tenant_name` - Merchant display name
- `business_name` - Official business name
- `telegram_account` - Admin Telegram ID
- `status` - Merchant status (0-disabled, 1-enabled)
- `betting_bonus_rate` - Commission rate configuration

#### 5.1.2 Additional Index Requirements
```sql
-- Optimize merchant data queries
CREATE INDEX idx_tenants_status_deleted ON tenants(status, deleted_at);
CREATE INDEX idx_users_tenant_deleted ON users(tenant_id, deleted_at);
CREATE INDEX idx_withdraws_tenant_date ON user_withdraws(tenant_id, created_at, status);
```

### 5.2 Merchant Statistics View
```sql
CREATE VIEW v_merchant_stats AS
SELECT 
    t.tenant_id,
    t.tenant_name,
    COUNT(DISTINCT u.user_id) as total_users,
    COUNT(DISTINCT CASE WHEN u.last_active_at > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN u.user_id END) as active_users,
    COALESCE(SUM(w.balance), 0) as total_user_balances,
    -- Add game balance aggregation here
    0 as total_game_balances
FROM tenants t
LEFT JOIN users u ON t.tenant_id = u.tenant_id AND u.deleted_at IS NULL
LEFT JOIN wallets w ON u.user_id = w.user_id
WHERE t.status = 1 AND t.deleted_at IS NULL
GROUP BY t.tenant_id, t.tenant_name;
```

## 6. Security Considerations

### 6.1 Access Control

#### 6.1.1 Admin Verification
```go
// Multi-layer security verification
func (s *adminService) verifyMerchantAccess(ctx context.Context) error {
    // 1. Verify admin status
    isAdmin, err := s.IsCurrentUserAdmin(ctx)
    if err != nil {
        return gerror.Wrap(err, "admin verification failed")
    }
    if !isAdmin {
        return ErrNotAdmin
    }
    
    // 2. Verify tenant context
    tenantID, ok := tenant.GetTenantIdFromContext(ctx)
    if !ok || tenantID == 0 {
        return gerror.New("invalid tenant context")
    }
    
    // 3. Log access attempt
    s.logMerchantAccess(ctx, tenantID)
    
    return nil
}
```

#### 6.1.2 Data Sensitivity Classification
- **Public**: Merchant name, creation date
- **Internal**: User counts, basic statistics
- **Confidential**: Financial balances, fee amounts
- **Restricted**: Admin contact information, configuration settings

### 6.2 Audit Trail

#### 6.2.1 Access Logging
```go
type MerchantAccessLog struct {
    LogID           uint64      `json:"log_id"`
    TenantID        uint64      `json:"tenant_id"`
    AdminTelegramID int64       `json:"admin_telegram_id"`
    AccessType      string      `json:"access_type"` // view, refresh, edit
    IPAddress       string      `json:"ip_address"`
    UserAgent       string      `json:"user_agent"`
    AccessedAt      *gtime.Time `json:"accessed_at"`
}
```

#### 6.2.2 Data Privacy Protection
- Sensitive financial data masked in logs
- Admin access rate limiting (max 10 requests/minute)
- Automatic session timeout after 30 minutes of inactivity

## 7. Admin Workflow

### 7.1 Standard Access Flow

#### 7.1.1 Navigation Path
1. **Main Menu** → Click "管理员中心" (Admin Center)
2. **Admin Center** → Click "商户信息" (Merchant Info)
3. **Merchant Info** → View comprehensive merchant data

#### 7.1.2 Workflow States
```mermaid
stateDiagram-v2
    [*] --> AdminLogin
    AdminLogin --> VerifyPermission
    VerifyPermission --> LoadMerchantData: Admin Verified
    VerifyPermission --> AccessDenied: Not Admin
    LoadMerchantData --> DisplayInfo
    DisplayInfo --> RefreshData: Refresh Button
    DisplayInfo --> BackToAdmin: Back Button
    RefreshData --> LoadMerchantData
    BackToAdmin --> AdminCenter
    AccessDenied --> [*]
```

### 7.2 Error Handling

#### 7.2.1 Error Recovery Scenarios
- **Database Connection Failed**: Show cached data with warning
- **Permission Denied**: Clear error message with support contact
- **Data Inconsistency**: Automatic data refresh attempt
- **Timeout Error**: Retry mechanism with exponential backoff

#### 7.2.2 User-Friendly Error Messages
```go
// I18n error messages
AdminMerchantInfoError = "获取商户信息失败，请稍后重试"
AdminMerchantInfoTimeout = "数据加载超时，请检查网络连接"
AdminMerchantInfoNoData = "暂无商户信息"
AdminMerchantInfoPartialData = "⚠️ 部分数据可能不准确，建议刷新"
```

## 8. Implementation Roadmap

### 8.1 Phase 1: Core Infrastructure (Week 1-2)
- [ ] Enhance `MerchantInfo` data structure
- [ ] Implement database integration for basic merchant data
- [ ] Add comprehensive error handling
- [ ] Update i18n localization files

### 8.2 Phase 2: Financial Integration (Week 3-4)
- [ ] Implement withdrawal fee calculation
- [ ] Add user balance aggregation
- [ ] Integrate game balance queries
- [ ] Add real-time balance tracking

### 8.3 Phase 3: Advanced Features (Week 5-6)
- [ ] Implement merchant configuration editing
- [ ] Add historical data trending
- [ ] Create merchant performance analytics
- [ ] Implement auto-refresh mechanisms

### 8.4 Phase 4: Security & Optimization (Week 7-8)
- [ ] Enhance security audit logging
- [ ] Implement data caching strategies
- [ ] Add performance monitoring
- [ ] Complete security penetration testing

## 9. Testing Strategy

### 9.1 Unit Testing

#### 9.1.1 Service Layer Tests
```go
func TestGetMerchantInfo(t *testing.T) {
    tests := []struct {
        name          string
        tenantID      uint64
        isAdmin       bool
        expectedError error
    }{
        {"Valid Admin Access", 1, true, nil},
        {"Non-Admin Access", 1, false, ErrNotAdmin},
        {"Invalid Tenant", 0, true, ErrTenantNotFound},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Test implementation
        })
    }
}
```

#### 9.1.2 Data Integrity Tests
- Financial calculation accuracy
- Currency formatting consistency
- Date/time handling correctness
- Error message localization

### 9.2 Integration Testing

#### 9.2.1 Database Integration
- Multi-tenant data isolation
- Transaction consistency
- Performance under load
- Backup/recovery procedures

#### 9.2.2 Telegram Bot Integration
- Message formatting accuracy
- Keyboard interaction functionality
- Error handling user experience
- Admin permission flow

### 9.3 Security Testing

#### 9.3.1 Access Control Validation
- Unauthorized access prevention
- Session management security
- Data exposure assessment
- Audit trail completeness

#### 9.3.2 Performance Testing
- Load testing with 1000+ concurrent admin sessions
- Database query optimization validation
- Memory usage under heavy load
- Response time consistency

## 10. Monitoring & Maintenance

### 10.1 Key Performance Indicators (KPIs)

#### 10.1.1 Operational Metrics
- Average response time: < 2 seconds
- Error rate: < 0.1%
- Admin satisfaction score: > 95%
- Data accuracy rate: 100%

#### 10.1.2 Security Metrics
- Unauthorized access attempts: 0
- Audit log completeness: 100%
- Security vulnerability count: 0
- Compliance adherence: 100%

### 10.2 Maintenance Procedures

#### 10.2.1 Regular Maintenance
- **Daily**: Monitor error logs and performance metrics
- **Weekly**: Review security audit logs
- **Monthly**: Database optimization and cleanup
- **Quarterly**: Security assessment and penetration testing

#### 10.2.2 Emergency Procedures
- **Data Breach Response**: Immediate access suspension and investigation
- **System Outage**: Fallback to cached data with degraded functionality
- **Performance Degradation**: Automatic scaling and optimization triggers

## 11. Compliance & Documentation

### 11.1 Technical Documentation
- API documentation with OpenAPI specification
- Database schema documentation
- Security implementation guide
- Operational runbook

### 11.2 User Documentation
- Admin user guide with screenshots
- Troubleshooting procedures
- FAQ with common scenarios
- Contact information for technical support

---

## Appendix A: Localization Keys

### Chinese (zh-CN) Required Translations
```toml
# Merchant Info Enhanced
AdminMerchantInfoRefresh = "刷新数据"
AdminMerchantInfoBusinessName = "业务名称"
AdminMerchantInfoFinancialSection = "💰 财务信息"
AdminMerchantInfoConfigSection = "⚙️ 配置信息"
AdminMerchantInfoTimeSection = "📅 时间信息"
AdminMerchantInfoTotalBalance = "总余额"
AdminMerchantInfoStatus = "状态"
AdminMerchantInfoLevel = "等级"
AdminMerchantInfoBonusRate = "返水比例"
AdminMerchantInfoCustomer = "客服"
AdminMerchantInfoGroup = "官方群组"
AdminMerchantInfoCreatedAt = "创建时间"
AdminMerchantInfoUpdatedAt = "最后更新"
AdminMerchantInfoStatusActive = "正常"
AdminMerchantInfoStatusDisabled = "已禁用"
```

## Appendix B: Database Migration Scripts

### B.1 Index Creation
```sql
-- Optimize merchant queries
CREATE INDEX IF NOT EXISTS idx_tenants_admin_lookup 
ON tenants(telegram_account, status, deleted_at);

-- Optimize user balance aggregation
CREATE INDEX IF NOT EXISTS idx_wallets_tenant_balance 
ON wallets(user_id) 
INCLUDE (balance);

-- Optimize withdrawal fee calculations
CREATE INDEX IF NOT EXISTS idx_withdraws_fees_date 
ON user_withdraws(tenant_id, created_at, status, fee_amount);
```

### B.2 View Creation
```sql
-- Create merchant summary view
CREATE OR REPLACE VIEW v_merchant_summary AS
SELECT 
    t.tenant_id,
    t.tenant_name,
    t.business_name,
    t.status,
    COUNT(DISTINCT u.user_id) as user_count,
    COALESCE(SUM(w.balance), 0) as total_balance,
    t.created_at,
    t.updated_at
FROM tenants t
LEFT JOIN users u ON t.tenant_id = u.tenant_id AND u.deleted_at IS NULL
LEFT JOIN wallets w ON u.user_id = w.user_id
WHERE t.deleted_at IS NULL
GROUP BY t.tenant_id, t.tenant_name, t.business_name, t.status, t.created_at, t.updated_at;
```

## 12. Handler Message Processing Integration

### 12.1 Message Processing Workflow

The Merchant Information function is a view-only feature that displays merchant details without requiring user input.

#### 12.1.1 Callback Handler Flow
```go
// handleMerchantInfo (current implementation)
1. Verify admin permission
2. Retrieve current tenant's merchant information
3. Format and display merchant details
4. Show action buttons for related operations
```

#### 12.1.2 Data Retrieval Pattern
```go
// Service call to get merchant information
merchantInfo, err := service.Admin().GetMerchantInfo(ctx)

// Error handling patterns
switch {
case errors.Is(err, service.ErrNotAdmin):
    // Send permission denied message
case errors.Is(err, service.ErrMerchantNotFound):
    // Send merchant not found error
case err != nil:
    // Send generic error message
default:
    // Display formatted merchant information
}
```

### 12.2 Response Formatting Pattern

#### 12.2.1 Merchant Information Display Template
```go
// Build comprehensive merchant information message
text := fmt.Sprintf(`🏪 商户信息

📋 基本信息
商户ID：%d
商户名称：%s
业务名称：%s

💰 财务信息
总余额：%s CNY
总存款费用：%s CNY
总取款费用：%s CNY
总返水支出：%s CNY
净收益：%s CNY

⚙️ 配置信息
状态：%s
等级：%s
返水比例：%s%%
客服：%s
官方群组：%s

📊 运营数据
总用户数：%d
活跃用户数：%d
今日新增用户：%d

📅 时间信息
创建时间：%s
最后更新：%s`,
    merchantInfo.MerchantID,
    merchantInfo.MerchantName,
    merchantInfo.BusinessName,
    merchantInfo.TotalBalance,
    merchantInfo.TotalDepositFees,
    merchantInfo.TotalWithdrawFees,
    merchantInfo.TotalCommissionPaid,
    merchantInfo.NetRevenue,
    formatMerchantStatus(merchantInfo.Status),
    merchantInfo.Level,
    merchantInfo.CommissionRate,
    merchantInfo.CustomerSupport,
    merchantInfo.OfficialGroup,
    merchantInfo.TotalUsers,
    merchantInfo.ActiveUsers,
    merchantInfo.TodayNewUsers,
    merchantInfo.CreatedAt.String(),
    merchantInfo.UpdatedAt.String(),
)
```

#### 12.2.2 Action Buttons Keyboard
```go
func BuildMerchantInfoKeyboard(ctx context.Context) tgbotapi.InlineKeyboardMarkup {
    return tgbotapi.NewInlineKeyboardMarkup(
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData(
                "📊 查看详细报表", 
                "admin_merchant_detailed_report",
            ),
            tgbotapi.NewInlineKeyboardButtonData(
                "⚙️ 商户设置", 
                "admin_merchant_settings",
            ),
        ),
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData(
                "💰 财务明细", 
                "admin_merchant_financial_details",
            ),
            tgbotapi.NewInlineKeyboardButtonData(
                "👥 用户列表", 
                "admin_merchant_user_list",
            ),
        ),
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData(
                "🔄 刷新", 
                "admin_merchant_info",
            ),
        ),
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData(
                "管理员中心", 
                "admin_center",
            ),
        ),
    )
}
```

### 12.3 Sub-Function Handlers

#### 12.3.1 Detailed Report Handler
```go
func handleMerchantDetailedReport(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
    // Generate detailed merchant analytics report
    report, err := service.Admin().GetMerchantDetailedReport(ctx)
    if err != nil {
        return callback.NewAnswerCallback(callbackQuery.ID, "获取报表失败"), nil
    }
    
    // Display detailed report with charts/graphs representation
    return &callback.EditMessageResponse{
        CallbackQueryID: callbackQuery.ID,
        ChatID:          callbackQuery.Message.Chat.ID,
        MessageID:       callbackQuery.Message.MessageID,
        Text:            formatDetailedReport(report),
        ParseMode:       "HTML",
        InlineKeyboard:  BuildReportNavigationKeyboard(ctx),
    }, nil
}
```

#### 12.3.2 Financial Details Handler
```go
func handleMerchantFinancialDetails(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
    // Show date range selection for financial details
    return &callback.EditMessageResponse{
        CallbackQueryID: callbackQuery.ID,
        ChatID:          callbackQuery.Message.Chat.ID,
        MessageID:       callbackQuery.Message.MessageID,
        Text:            "请选择查看财务明细的时间范围：",
        ParseMode:       "HTML",
        InlineKeyboard:  BuildDateRangeKeyboard(ctx, "merchant_financial"),
    }, nil
}
```

### 12.4 State Management

#### 12.4.1 Merchant Context State
```go
// Minimal state for merchant information viewing
type AdminMerchantState struct {
    AdminUserID int64
    ViewMode    string    // "info", "report", "financial", "users"
    ExpireAt    time.Time
}

// State transitions:
// 1. Click "Merchant Info" → Display merchant information
// 2. Click sub-functions → Update ViewMode
// 3. Click "Refresh" → Reload and display updated info
// 4. Click "Admin Center" → Clear state and return
```

### 12.5 Auto-Refresh Capability

#### 12.5.1 Real-time Data Updates
```go
func refreshMerchantInfo(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
    // Get fresh merchant data
    merchantInfo, err := service.Admin().GetMerchantInfo(ctx)
    if err != nil {
        return callback.NewAnswerCallback(callbackQuery.ID, "刷新失败"), nil
    }
    
    // Update the message with fresh data
    return &callback.EditMessageResponse{
        CallbackQueryID: callbackQuery.ID,
        ChatID:          callbackQuery.Message.Chat.ID,
        MessageID:       callbackQuery.Message.MessageID,
        Text:            BuildMerchantInfoMessage(ctx, merchantInfo),
        ParseMode:       "HTML",
        InlineKeyboard:  BuildMerchantInfoKeyboard(ctx),
    }, nil
}
```

### 12.6 Integration Testing Requirements

#### 12.6.1 Display Functionality Tests
```go
func TestMerchantInfoDisplay(t *testing.T) {
    // 1. Test basic merchant info display
    // 2. Test financial calculations accuracy
    // 3. Test status formatting
    // 4. Test refresh functionality
    // 5. Test sub-function navigation
    // 6. Test permission verification
}
```

#### 12.6.2 Data Accuracy Tests
- Verify financial calculations
- Test user count aggregations
- Validate timestamp formatting
- Check multi-tenant data isolation

---

**Document Version**: 1.0  
**Last Updated**: 2025-01-20  
**Author**: Technical Team  
**Review Status**: Draft  
**Approval Required**: Product Manager, Security Team, Development Lead