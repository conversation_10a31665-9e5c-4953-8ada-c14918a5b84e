# Admin Module Technical Documentation

## Overview

This directory contains comprehensive technical documentation for the Admin Center module (`internal/admin`), a multi-tenant Telegram bot administration system with 8 core administrative functions.

## Module Architecture

### Current Implementation Status
- **Service Layer**: ✅ Complete with IAdmin interface (`internal/service/admin.go`)
- **Callback Handlers**: ✅ Complete with proper Service layer integration (`internal/admin/handler_callback.go`) 
- **UI Components**: ✅ Complete keyboards and response builders
- **i18n Support**: ✅ Complete Chinese translations (`manifest/i18n/zh-CN.toml`)
- **Navigation**: ✅ Integration with navigation module for back-to-main functionality
- **Business Logic**: ⚠️ Most functions return placeholder "功能开发中..." responses

### Core Design Patterns
- **Service Layer Pattern**: All data access through `service.Admin()` interface
- **Multi-Tenant Architecture**: Context-based tenant isolation using `tenant.GetTenantIdFromContext()`
- **Telegram Bot API v5**: Callback-driven UI with inline keyboards
- **i18n Internationalization**: `.toml` based translation system
- **Permission-Based Access**: Admin validation through `tenant.telegram_account` comparison

## Admin Functions (8 Core Features)

| Function | Callback Data | Service Method | Implementation Status | Documentation |
|----------|---------------|----------------|----------------------|---------------|
| **Manual Balance** | `admin_manual_balance` | `AdjustUserBalance()` | Placeholder | [PRD](./manual-balance-prd.md) |
| **Flow Requirements** | `admin_flow_requirements` | `AdjustUserFlowRequirement()` | Placeholder | [PRD](./flow-requirements-prd.md) |
| **Platform Statistics** | `admin_platform_stats` | `GetPlatformStats()` | Placeholder | [PRD](./platform-statistics-prd.md) |
| **Personal Statistics** | `admin_personal_stats` | `GetPersonalStats()` | Placeholder | [PRD](./personal-statistics-prd.md) |
| **Daily Reports** | `admin_daily_report` | `GetDailyReport()` | Placeholder | [PRD](./daily-reports-prd.md) |
| **Deposit Logs** | `admin_deposit_logs` | `GetDepositLogs()` | Placeholder | [PRD](./deposit-logs-prd.md) |
| **Withdraw Logs** | `admin_withdraw_logs` | `GetWithdrawLogs()` | Placeholder | [PRD](./withdraw-logs-prd.md) |
| **Merchant Info** | `admin_merchant_info` | `GetMerchantInfo()` | Placeholder | [PRD](./merchant-info-prd.md) |

## Permission System

### Admin Validation Logic
```go
// Current user's Telegram ID must match tenant.telegram_account
isAdmin := tenantData.TelegramAccount == currentUserTelegramID
```

### Security Features
- **Dual Validation**: Both menu display and function execution validate admin permissions
- **Tenant Isolation**: All operations scoped to current tenant context
- **Error Handling**: Proper error logging and user-friendly error messages
- **Audit Trail**: All admin operations logged for compliance

## Technical Implementation

### File Structure
```
internal/admin/
├── handler_callback.go    # Callback handlers for all 8 functions
├── keyboards.go          # Inline keyboard builders
├── responses.go          # Message response builders  
├── init.go              # Handler registration
├── auth.go              # Legacy auth functions (deprecated)
├── test_admin.go        # Test utilities (needs Service layer update)
└── README.md            # Module documentation (outdated)

internal/service/admin.go  # Service layer with IAdmin interface
manifest/i18n/zh-CN.toml  # Admin translations (AdminWelcome, AdminButton*, etc.)
```

### Integration Points
- **Navigation Module**: Uses `navigation.DeleteAndSendMainMenuResponse` for back-to-main
- **Shared Components**: Integrates with shared menu system for admin button display
- **Registry System**: Callback handlers registered via `registry.RegisterExact()`
- **i18n System**: All user-facing text uses `service.I18n().T(ctx, key)`

## Development Workflow

### Adding New Admin Function
1. **Service Interface**: Add method to `IAdmin` interface in `internal/service/admin.go`
2. **Service Implementation**: Implement method with admin permission check
3. **Callback Handler**: Add handler in `handler_callback.go` 
4. **UI Components**: Add keyboard button in `keyboards.go`
5. **Response Builder**: Add message builder in `responses.go`
6. **i18n**: Add translations to `manifest/i18n/zh-CN.toml`
7. **Registration**: Register callback in `init.go`
8. **Documentation**: Create PRD document following template

### Testing Approach
- **Unit Tests**: Service layer methods with mock contexts
- **Integration Tests**: Full callback flow testing
- **Manual Testing**: Use `test_admin.go` utilities (after Service layer update)

## Next Development Priorities

### Immediate Tasks
1. **Fix Test Module**: Update `test_admin.go` to use Service layer instead of deprecated functions
2. **Implement Core Functions**: Replace placeholder implementations with actual business logic
3. **Database Schema**: Verify database schema supports all required admin operations

### Enhancement Opportunities  
1. **Advanced Permissions**: Role-based access control beyond simple admin flag
2. **Audit Dashboard**: Comprehensive admin action audit and reporting
3. **Bulk Operations**: Batch processing for administrative tasks
4. **API Integration**: REST API endpoints for external admin tools

## References
- [Full Requirements Document](./full.md) - Complete business requirements
- [Telegram Bot Media Handling Guide](../telegram-media-handling-guide.md)
- [Service Layer Design Standards](../CLAUDE.md#service-and-dao-layer-interaction-design-standards)
- [i18n Implementation Guide](../i18n-guide.md)