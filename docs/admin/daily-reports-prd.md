# Daily Reports Admin Function - Technical PRD

## Document Information
- **Version**: 1.0
- **Created**: 2025-01-20
- **Last Updated**: 2025-01-20
- **Author**: Technical Team
- **Status**: Development Ready

## 1. Function Overview

### Purpose
The Daily Reports admin function provides comprehensive daily financial reporting capabilities for administrators to monitor platform deposit and withdrawal activities across specified date ranges. This feature enables data-driven decision making through detailed daily transaction summaries with time-based data aggregation.

### Business Objectives
- **Financial Transparency**: Provide detailed daily transaction visibility for administrators
- **Performance Monitoring**: Track daily deposit/withdrawal trends and patterns
- **Compliance Support**: Generate reports for audit and regulatory requirements
- **Data-Driven Insights**: Enable informed business decisions through comprehensive daily metrics

## 2. Current Implementation Status

### Existing Infrastructure
- **Service Layer**: `GetDailyReport()` method defined in `IAdmin` interface (`internal/service/admin.go`)
- **Handler**: `handleDailyReport()` callback handler implemented (`internal/admin/handler_callback.go`)
- **Data Structure**: `DailyReport` struct with basic fields for date, deposits, withdraws
- **UI Integration**: Admin center keyboard with daily report button
- **I18n Support**: Chinese translations for UI elements and messages

### Implementation Status
- ✅ **Interface Definition**: Complete service interface definition
- ✅ **Handler Framework**: Basic callback handler structure implemented
- ✅ **UI Components**: Admin center integration and keyboard layout
- ✅ **I18n Framework**: Translation keys for user-facing messages
- ❌ **Data Aggregation Logic**: Placeholder implementation requires development
- ❌ **Date Range Validation**: Input validation logic needs implementation
- ❌ **Database Queries**: Time-based aggregation queries not implemented
- ❌ **Report Caching**: Performance optimization layer missing
- ❌ **Advanced UI Features**: Pagination and detailed report display pending

## 3. Technical Specifications

### 3.1 Data Sources

#### Primary Tables
- **`user_recharges`**: Deposit transaction records
  - Key fields: `amount`, `converted_amount`, `completed_at`, `state`, `tenant_id`
  - Filtering: `state = 2` (Completed), `tenant_id` match
  - Time field: `completed_at` for deposit completion

- **`user_withdraws`**: Withdrawal transaction records  
  - Key fields: `amount`, `converted_amount`, `completed_at`, `state`, `tenant_id`
  - Filtering: `state = 4` (Completed), `tenant_id` match
  - Time field: `completed_at` for withdrawal completion

- **`transactions`**: Unified transaction log
  - Key fields: `amount`, `type`, `direction`, `created_at`, `status`, `tenant_id`
  - Filtering: `status = 1` (Success), `tenant_id` match
  - Types: `deposit`, `withdrawal` for financial transactions

#### Data Conversion Strategy
- **Currency Standardization**: All amounts reported in tenant's base currency (CNY)
- **Deposit Calculation**: Use `converted_amount` from `user_recharges` (final credited amount)
- **Withdrawal Calculation**: Use `amount` from `user_withdraws` (total deducted amount including fees)
- **Timezone Handling**: Convert all timestamps to GMT+8 for daily boundary calculations

### 3.2 Enhanced Data Structure

```go
type DailyReport struct {
    Date               string                `json:"date"`                // YYYY-MM-DD format
    DailyDeposits      string                `json:"daily_deposits"`      // Total deposits for the day
    DailyWithdraws     string                `json:"daily_withdraws"`     // Total withdrawals for the day
    DepositCount       int64                 `json:"deposit_count"`       // Number of deposit transactions
    WithdrawCount      int64                 `json:"withdraw_count"`      // Number of withdrawal transactions
    NetFlow            string                `json:"net_flow"`            // Deposits - Withdrawals
    Currency           string                `json:"currency"`            // Base currency (e.g., CNY)
    
    // Extended metrics
    AverageDeposit     string                `json:"average_deposit"`     // Average deposit amount
    AverageWithdraw    string                `json:"average_withdraw"`    // Average withdrawal amount
    PeakDepositHour    int                   `json:"peak_deposit_hour"`   // Hour with most deposits (0-23)
    PeakWithdrawHour   int                   `json:"peak_withdraw_hour"`  // Hour with most withdrawals (0-23)
    
    // Metadata
    DataTimestamp      *gtime.Time           `json:"data_timestamp"`      // When data was calculated
    TenantId           int                   `json:"tenant_id"`           // Tenant identifier
}

type DailyReportRange struct {
    StartDate          string                `json:"start_date"`          // YYYY-MM-DD
    EndDate            string                `json:"end_date"`            // YYYY-MM-DD
    Reports            []*DailyReport        `json:"reports"`             // Daily reports array
    Summary            *RangeSummary         `json:"summary"`             // Period summary
    TotalDays          int                   `json:"total_days"`          // Number of days in range
    
    // Pagination
    Page               int                   `json:"page"`                // Current page (1-based)
    PageSize           int                   `json:"page_size"`           // Items per page (default: 20)
    TotalPages         int                   `json:"total_pages"`         // Total pages available
}

type RangeSummary struct {
    TotalDeposits      string                `json:"total_deposits"`      // Sum of all deposits
    TotalWithdraws     string                `json:"total_withdraws"`     // Sum of all withdrawals
    TotalNetFlow       string                `json:"total_net_flow"`      // Total net flow
    TotalTransactions  int64                 `json:"total_transactions"`  // Total transaction count
    AverageDailyDeposit string               `json:"average_daily_deposit"` // Average daily deposits
    AverageDailyWithdraw string              `json:"average_daily_withdraw"` // Average daily withdrawals
    HighestDepositDay  string                `json:"highest_deposit_day"` // Date with highest deposits
    HighestWithdrawDay string                `json:"highest_withdraw_day"` // Date with highest withdrawals
}
```

### 3.3 Database Query Specifications

#### Daily Aggregation Query
```sql
-- Daily deposits aggregation
SELECT 
    DATE(completed_at) as report_date,
    SUM(converted_amount) as total_deposits,
    COUNT(*) as deposit_count,
    AVG(converted_amount) as avg_deposit,
    HOUR(completed_at) as hour_of_day,
    COUNT(*) as hourly_count
FROM user_recharges 
WHERE tenant_id = ? 
    AND state = 2 
    AND DATE(completed_at) BETWEEN ? AND ?
    AND deleted_at IS NULL
GROUP BY DATE(completed_at), HOUR(completed_at)
ORDER BY report_date DESC, hour_of_day;

-- Daily withdrawals aggregation  
SELECT 
    DATE(completed_at) as report_date,
    SUM(amount) as total_withdrawals,
    COUNT(*) as withdraw_count,
    AVG(amount) as avg_withdraw,
    HOUR(completed_at) as hour_of_day,
    COUNT(*) as hourly_count
FROM user_withdraws 
WHERE tenant_id = ? 
    AND state = 4 
    AND DATE(completed_at) BETWEEN ? AND ?
    AND deleted_at IS NULL
GROUP BY DATE(completed_at), HOUR(completed_at)
ORDER BY report_date DESC, hour_of_day;
```

#### Optimization Indexes
```sql
-- Performance optimization indexes
CREATE INDEX idx_user_recharges_tenant_date_state ON user_recharges(tenant_id, completed_at, state);
CREATE INDEX idx_user_withdraws_tenant_date_state ON user_withdraws(tenant_id, completed_at, state);
CREATE INDEX idx_transactions_tenant_date_type ON transactions(tenant_id, created_at, type, status);
```

### 3.4 Time Zone and Date Handling

#### Timezone Strategy
- **System Timezone**: GMT+8 (Asia/Shanghai) as per business requirements
- **Date Boundaries**: Daily reports calculated using GMT+8 timezone boundaries
- **Input Format**: YYYY-MM-DD for user input (e.g., "2025-01-01")
- **Display Format**: Localized date format based on i18n settings

#### Date Processing Logic
```go
// Convert input date to GMT+8 timezone boundaries
func calculateDateBoundaries(dateStr string) (start, end *gtime.Time, error) {
    location, _ := time.LoadLocation("Asia/Shanghai")
    
    // Parse input date
    inputDate, err := time.Parse("2006-01-02", dateStr)
    if err != nil {
        return nil, nil, err
    }
    
    // Set to GMT+8 timezone
    inputDate = inputDate.In(location)
    
    // Day start: 00:00:00 GMT+8
    start = gtime.New(inputDate.Truncate(24 * time.Hour))
    
    // Day end: 23:59:59.999 GMT+8  
    end = gtime.New(inputDate.Add(24*time.Hour - time.Nanosecond))
    
    return start, end, nil
}
```

## 4. UI/UX Design Specifications

### 4.1 Admin Center Integration

#### Navigation Flow
1. **Entry Point**: Admin Center → "查指定日期范围每日存取款" button
2. **Date Input**: User prompted for date range input
3. **Report Display**: Paginated daily report results
4. **Navigation**: Back to admin center, pagination controls

#### User Interaction Pattern
```
Admin Center Menu
     ↓ (Click Daily Report)
Date Range Input
     ↓ (Enter "2025-01-01 2025-01-31")
Validation & Processing
     ↓ (Display results)
Daily Report Table
     ↓ (Navigation)
[← Previous] [Back to Admin] [Next →]
```

### 4.2 Date Range Input Design

#### Input Prompt Message
```
请回复要查询的日期范围，使用空格分隔，例如：2025-01-01 2025-05-30

【返回】
```

#### Input Validation Rules
- **Format Validation**: Two dates separated by single space
- **Date Format**: YYYY-MM-DD format required
- **Range Validation**: Start date ≤ End date
- **Maximum Range**: Limit to 365 days to prevent performance issues
- **Future Date**: Prevent selection of future dates beyond today

#### Error Handling Messages
```toml
# I18n keys for validation errors
AdminDailyReportInvalidFormat = "❌ 日期格式错误，请使用格式：2025-01-01 2025-01-31"
AdminDailyReportInvalidRange = "❌ 开始日期必须小于或等于结束日期"
AdminDailyReportRangeTooLarge = "❌ 日期范围不能超过365天"
AdminDailyReportFutureDate = "❌ 不能查询未来日期的数据"
```

### 4.3 Report Display Format

#### Daily Report Table Layout
```
📅 日期范围：2025-01-01 至 2025-01-31

📊 期间汇总：
💰 总存款：123,456.78 CNY
💸 总取款：98,765.43 CNY  
📈 净流量：+24,691.35 CNY
🔢 总交易：1,250 笔

📋 每日明细：
日期        | 存款      | 取款      | 净流量
2025-01-31 | 5,234.56 | 3,456.78 | +1,777.78
2025-01-30 | 4,123.45 | 2,345.67 | +1,777.78
2025-01-29 | 3,456.78 | 4,567.89 | -1,111.11
...

📄 第1页，共15页 | 显示1-20条，共300条记录

[← 上一页] [返回管理员中心] [下一页 →]
```

#### Enhanced Display Features
- **Currency Formatting**: Comma-separated thousands, 2 decimal places
- **Net Flow Indicators**: + for positive, - for negative flow
- **Summary Statistics**: Period totals and averages
- **Visual Separators**: Unicode symbols for better readability
- **Responsive Layout**: Optimized for Telegram message display

### 4.4 Pagination Design

#### Pagination Parameters
- **Page Size**: 20 records per page (configurable)
- **Navigation**: Previous/Next buttons with page indicators
- **Boundary Handling**: Disable buttons at first/last pages
- **Performance**: Optimize for large date ranges

#### Pagination Button Logic
```go
// Button visibility logic
func buildPaginationButtons(currentPage, totalPages int) []tgbotapi.InlineKeyboardButton {
    var buttons []tgbotapi.InlineKeyboardButton
    
    // Previous button (if not first page)
    if currentPage > 1 {
        buttons = append(buttons, tgbotapi.NewInlineKeyboardButtonData(
            "← 上一页", fmt.Sprintf("admin_daily_report_page_%d", currentPage-1)))
    }
    
    // Back to admin center
    buttons = append(buttons, tgbotapi.NewInlineKeyboardButtonData(
        "返回管理员中心", "admin_center"))
    
    // Next button (if not last page)  
    if currentPage < totalPages {
        buttons = append(buttons, tgbotapi.NewInlineKeyboardButtonData(
            "下一页 →", fmt.Sprintf("admin_daily_report_page_%d", currentPage+1)))
    }
    
    return buttons
}
```

## 5. Database Query Requirements

### 5.1 Core Query Performance

#### Optimized Aggregation Strategy
- **Index Usage**: Leverage tenant_id + date + status composite indexes
- **Query Batching**: Single query for entire date range with GROUP BY
- **Memory Management**: Stream large result sets to prevent memory overflow
- **Connection Pooling**: Reuse database connections for multiple queries

#### Query Execution Plan
```sql
-- Optimized single query for date range
WITH daily_deposits AS (
    SELECT 
        DATE(completed_at) as report_date,
        SUM(converted_amount) as deposits,
        COUNT(*) as deposit_count,
        AVG(converted_amount) as avg_deposit
    FROM user_recharges 
    WHERE tenant_id = ? 
        AND state = 2 
        AND completed_at >= ? AND completed_at < ?
        AND deleted_at IS NULL
    GROUP BY DATE(completed_at)
),
daily_withdraws AS (
    SELECT 
        DATE(completed_at) as report_date,
        SUM(amount) as withdraws,
        COUNT(*) as withdraw_count,
        AVG(amount) as avg_withdraw
    FROM user_withdraws 
    WHERE tenant_id = ? 
        AND state = 4 
        AND completed_at >= ? AND completed_at < ?
        AND deleted_at IS NULL
    GROUP BY DATE(completed_at)
)
SELECT 
    COALESCE(d.report_date, w.report_date) as report_date,
    COALESCE(d.deposits, 0) as daily_deposits,
    COALESCE(w.withdraws, 0) as daily_withdraws,
    COALESCE(d.deposit_count, 0) as deposit_count,
    COALESCE(w.withdraw_count, 0) as withdraw_count,
    COALESCE(d.avg_deposit, 0) as avg_deposit,
    COALESCE(w.avg_withdraw, 0) as avg_withdraw
FROM daily_deposits d
FULL OUTER JOIN daily_withdraws w ON d.report_date = w.report_date
ORDER BY report_date DESC;
```

### 5.2 Error Handling and Edge Cases

#### Query Error Scenarios
- **Database Connection Timeout**: Implement retry logic with exponential backoff
- **Large Dataset Handling**: Stream results and implement query timeout
- **No Data Found**: Return empty result set with appropriate messaging
- **Partial Data**: Handle incomplete transaction records gracefully

#### Data Consistency Checks
- **Transaction Completeness**: Verify all completed transactions are included
- **Amount Accuracy**: Cross-validate against wallet balance changes
- **Timezone Consistency**: Ensure all dates use consistent timezone conversion
- **Tenant Isolation**: Strict tenant_id filtering for data security

## 6. Report Caching and Performance Optimization

### 6.1 Caching Strategy

#### Multi-Level Caching Architecture
```go
type ReportCache struct {
    // Level 1: In-memory cache for recent reports (15 minutes TTL)
    memoryCache map[string]*DailyReportRange
    
    // Level 2: Redis cache for historical reports (24 hours TTL)
    redisCache   redis.Client
    
    // Level 3: Database materialized views for frequently accessed ranges
    materializedViews map[string]string
}

// Cache key format: "daily_report:{tenant_id}:{start_date}:{end_date}:{page}"
func generateCacheKey(tenantId int, startDate, endDate string, page int) string {
    return fmt.Sprintf("daily_report:%d:%s:%s:%d", tenantId, startDate, endDate, page)
}
```

#### Cache Invalidation Strategy
- **Time-based**: Automatic expiration for historical data
- **Event-based**: Invalidate cache when new transactions complete
- **Manual**: Admin-triggered cache refresh for data consistency
- **Selective**: Invalidate only affected date ranges

#### Performance Metrics
- **Target Response Time**: < 2 seconds for cached reports
- **Cache Hit Rate**: > 80% for repeated queries
- **Memory Usage**: < 100MB total cache size
- **Database Load**: < 50% reduction in query frequency

### 6.2 Optimization Techniques

#### Query Optimization
- **Prepared Statements**: Reuse query execution plans
- **Connection Pooling**: Maintain optimal database connections
- **Index Hints**: Guide query planner for optimal execution
- **Partition Pruning**: Use table partitioning for large datasets

#### Data Processing Optimization
```go
// Parallel processing for large date ranges
func processDateRangeParallel(startDate, endDate string, tenantId int) (*DailyReportRange, error) {
    dateRanges := splitDateRange(startDate, endDate, 7) // Split into weekly chunks
    
    var wg sync.WaitGroup
    results := make(chan *DailyReport, len(dateRanges))
    errors := make(chan error, len(dateRanges))
    
    // Process each chunk in parallel
    for _, dateRange := range dateRanges {
        wg.Add(1)
        go func(start, end string) {
            defer wg.Done()
            report, err := generateDailyReportChunk(start, end, tenantId)
            if err != nil {
                errors <- err
                return
            }
            results <- report
        }(dateRange.Start, dateRange.End)
    }
    
    wg.Wait()
    close(results)
    close(errors)
    
    // Aggregate results
    return aggregateReportChunks(results, errors)
}
```

## 7. Admin Workflow

### 7.1 Complete User Journey

#### Step-by-Step Workflow
1. **Access Control**: Verify admin permissions via `IsCurrentUserAdmin()`
2. **Navigation**: Click "查指定日期范围每日存取款" in admin center
3. **Input Collection**: Prompt for date range input via text message
4. **Validation**: Validate date format, range, and business rules
5. **Data Processing**: Execute optimized database queries with caching
6. **Report Generation**: Format data into user-friendly display
7. **Display**: Show paginated results with navigation controls
8. **Navigation**: Allow pagination and return to admin center

#### Permission Validation Flow
```go
func validateAdminAccess(ctx context.Context) error {
    isAdmin, err := service.Admin().IsCurrentUserAdmin(ctx)
    if err != nil {
        g.Log().Errorf(ctx, "Admin permission check failed: %v", err)
        return gerror.Wrap(err, "权限验证失败")
    }
    
    if !isAdmin {
        g.Log().Warningf(ctx, "Non-admin user attempted to access daily reports")
        return gerror.New("您没有管理员权限")
    }
    
    return nil
}
```

### 7.2 Error Handling Workflow

#### User Input Error Handling
```go
func validateDateRangeInput(input string) (startDate, endDate string, err error) {
    parts := strings.Fields(strings.TrimSpace(input))
    if len(parts) != 2 {
        return "", "", gerror.New("日期格式错误，请使用格式：2025-01-01 2025-01-31")
    }
    
    startDate, endDate = parts[0], parts[1]
    
    // Validate date format
    if !isValidDateFormat(startDate) || !isValidDateFormat(endDate) {
        return "", "", gerror.New("日期格式错误，请使用 YYYY-MM-DD 格式")
    }
    
    // Validate date range
    start, _ := time.Parse("2006-01-02", startDate)
    end, _ := time.Parse("2006-01-02", endDate)
    
    if start.After(end) {
        return "", "", gerror.New("开始日期必须小于或等于结束日期")
    }
    
    if end.Sub(start) > 365*24*time.Hour {
        return "", "", gerror.New("日期范围不能超过365天")
    }
    
    if end.After(time.Now()) {
        return "", "", gerror.New("不能查询未来日期的数据")
    }
    
    return startDate, endDate, nil
}
```

#### System Error Recovery
- **Database Timeout**: Retry with exponential backoff, fallback to cached data
- **Memory Overflow**: Implement streaming for large datasets
- **Network Issues**: Graceful degradation with partial data display
- **Cache Failure**: Direct database fallback with performance logging

### 7.3 Audit and Logging

#### Admin Action Logging
```go
type AdminActionLog struct {
    AdminUserID    uint64      `json:"admin_user_id"`
    Action         string      `json:"action"`         // "daily_report_view"
    Parameters     *gjson.Json `json:"parameters"`     // {"start_date": "2025-01-01", "end_date": "2025-01-31"}
    ResultSummary  string      `json:"result_summary"` // "Generated report for 31 days"
    ExecutionTime  int64       `json:"execution_time"` // Milliseconds
    IPAddress      string      `json:"ip_address"`
    UserAgent      string      `json:"user_agent"`
    TenantID       int         `json:"tenant_id"`
    CreatedAt      *gtime.Time `json:"created_at"`
}

func logAdminReportAccess(ctx context.Context, startDate, endDate string, executionTime int64) {
    // Log admin report access for audit purposes
    log := &AdminActionLog{
        AdminUserID:   getCurrentAdminID(ctx),
        Action:        "daily_report_view",
        Parameters:    gjson.New(map[string]string{"start_date": startDate, "end_date": endDate}),
        ResultSummary: fmt.Sprintf("Generated daily report for date range %s to %s", startDate, endDate),
        ExecutionTime: executionTime,
        IPAddress:     getClientIP(ctx),
        UserAgent:     getUserAgent(ctx),
        TenantID:      getTenantID(ctx),
        CreatedAt:     gtime.Now(),
    }
    
    // Store in audit log table
    dao.AdminActionLogs.Ctx(ctx).Data(log).Insert()
}
```

## 8. Implementation Roadmap

### 8.1 Development Phases

#### Phase 1: Core Implementation (Week 1-2)
- **Database Query Engine**: Implement optimized aggregation queries
- **Service Layer**: Complete `GetDailyReport()` method implementation
- **Basic UI**: Date input validation and simple report display
- **Error Handling**: Input validation and basic error responses

**Deliverables:**
- Functional daily report generation
- Date range input handling
- Basic report display format
- Unit tests for core functions

#### Phase 2: Enhanced Features (Week 3-4)
- **Pagination System**: Implement paginated report display
- **Advanced Metrics**: Add summary statistics and extended metrics
- **Performance Optimization**: Implement basic caching layer
- **UI Enhancement**: Improve report formatting and navigation

**Deliverables:**
- Paginated report navigation
- Enhanced report metrics
- Performance optimization
- Integration tests

#### Phase 3: Production Optimization (Week 5-6)
- **Advanced Caching**: Multi-level caching with Redis integration
- **Performance Monitoring**: Add query performance metrics
- **Security Hardening**: Enhanced permission validation and audit logging
- **Error Recovery**: Robust error handling and fallback mechanisms

**Deliverables:**
- Production-ready caching system
- Performance monitoring dashboard
- Security audit compliance
- Load testing results

#### Phase 4: Advanced Analytics (Week 7-8)
- **Trend Analysis**: Weekly and monthly comparison features
- **Export Functionality**: CSV/Excel export capabilities
- **Advanced Filtering**: Filter by transaction types, user segments
- **Real-time Updates**: Live data refresh capabilities

**Deliverables:**
- Advanced analytics features
- Export functionality
- Real-time data updates
- User acceptance testing

### 8.2 Technical Dependencies

#### Required Components
- **Database Optimization**: Create necessary indexes for performance
- **Caching Infrastructure**: Redis setup for production caching
- **Monitoring Tools**: Performance monitoring and alerting setup
- **Testing Framework**: Comprehensive test suite for reliability

#### Integration Points
- **Admin Service**: Extend existing admin service interface
- **I18n System**: Add comprehensive translation support
- **Permission System**: Integrate with existing admin permission validation
- **Audit System**: Connect with existing operation logging framework

### 8.3 Risk Mitigation

#### Technical Risks
- **Performance**: Large date ranges may impact system performance
  - *Mitigation*: Implement pagination and query optimization
- **Data Accuracy**: Timezone conversion errors could affect accuracy
  - *Mitigation*: Comprehensive timezone testing and validation
- **Security**: Unauthorized access to financial data
  - *Mitigation*: Strict permission validation and audit logging

#### Business Risks
- **User Experience**: Complex interface may confuse administrators
  - *Mitigation*: Intuitive UI design and comprehensive documentation
- **Data Privacy**: Financial data exposure risks
  - *Mitigation*: Tenant isolation and access control enforcement

## 9. Testing Strategy

### 9.1 Unit Testing

#### Test Coverage Areas
- **Date Processing**: Timezone conversion and boundary calculations
- **Query Logic**: Database aggregation and filtering
- **Data Validation**: Input validation and error handling
- **Permission Checks**: Admin access control validation

#### Test Cases
```go
func TestDailyReportGeneration(t *testing.T) {
    tests := []struct {
        name      string
        startDate string
        endDate   string
        expected  *DailyReportRange
        expectErr bool
    }{
        {
            name:      "Valid date range",
            startDate: "2025-01-01",
            endDate:   "2025-01-31",
            expected:  &DailyReportRange{TotalDays: 31},
            expectErr: false,
        },
        {
            name:      "Invalid date format",
            startDate: "2025/01/01",
            endDate:   "2025-01-31",
            expected:  nil,
            expectErr: true,
        },
        {
            name:      "End date before start date",
            startDate: "2025-01-31",
            endDate:   "2025-01-01",
            expected:  nil,
            expectErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result, err := service.Admin().GetDailyReport(ctx, tt.startDate, tt.endDate)
            if tt.expectErr {
                assert.Error(t, err)
                assert.Nil(t, result)
            } else {
                assert.NoError(t, err)
                assert.Equal(t, tt.expected.TotalDays, result.TotalDays)
            }
        })
    }
}
```

### 9.2 Integration Testing

#### Database Integration Tests
- **Data Consistency**: Verify aggregated amounts match individual transactions
- **Performance**: Test query performance with large datasets
- **Timezone Handling**: Validate timezone conversion accuracy
- **Tenant Isolation**: Ensure proper tenant data segregation

#### UI Integration Tests
- **Workflow Testing**: Complete admin workflow from start to finish
- **Error Scenarios**: Test error handling and user feedback
- **Pagination**: Verify pagination functionality with various data sizes
- **Permission Validation**: Test access control enforcement

### 9.3 Performance Testing

#### Load Testing Scenarios
- **Concurrent Access**: Multiple admins accessing reports simultaneously
- **Large Date Ranges**: Performance with 1+ year date ranges
- **Peak Traffic**: System behavior during high transaction volumes
- **Memory Usage**: Monitor memory consumption during large report generation

#### Performance Benchmarks
- **Response Time**: < 3 seconds for cached reports, < 10 seconds for new reports
- **Throughput**: Support 50+ concurrent report requests
- **Memory Usage**: < 500MB memory per report generation process
- **Database Load**: < 10% additional load during peak report generation

### 9.4 Security Testing

#### Access Control Testing
- **Permission Bypass**: Attempt to access reports without admin privileges
- **Tenant Isolation**: Verify cross-tenant data access prevention
- **Input Injection**: Test for SQL injection and input validation vulnerabilities
- **Audit Trail**: Verify all admin actions are properly logged

#### Data Privacy Testing
- **Data Exposure**: Ensure no sensitive data leakage in error messages
- **Access Logging**: Verify comprehensive audit logging functionality
- **Cache Security**: Test cache isolation and data protection

## 10. Monitoring and Maintenance

### 10.1 Performance Monitoring

#### Key Performance Indicators (KPIs)
- **Query Execution Time**: Average and P95 query response times
- **Cache Hit Rate**: Percentage of requests served from cache
- **Error Rate**: Frequency of failed report generations
- **User Adoption**: Number of daily report requests per admin

#### Monitoring Implementation
```go
type ReportMetrics struct {
    QueryExecutionTime   time.Duration `json:"query_execution_time"`
    CacheHitRate        float64       `json:"cache_hit_rate"`
    ErrorRate           float64       `json:"error_rate"`
    TotalRequests       int64         `json:"total_requests"`
    AveragePageSize     int           `json:"average_page_size"`
    PeakUsageHour       int           `json:"peak_usage_hour"`
}

func recordReportMetrics(ctx context.Context, startTime time.Time, cacheHit bool, err error) {
    metrics := &ReportMetrics{
        QueryExecutionTime: time.Since(startTime),
        CacheHitRate:      calculateCacheHitRate(),
        ErrorRate:         calculateErrorRate(),
        TotalRequests:     incrementRequestCounter(),
    }
    
    // Send metrics to monitoring system
    monitoring.RecordMetrics("daily_reports", metrics)
}
```

### 10.2 Maintenance Procedures

#### Regular Maintenance Tasks
- **Cache Cleanup**: Remove expired cache entries (weekly)
- **Index Optimization**: Rebuild database indexes for performance (monthly)
- **Audit Log Rotation**: Archive old audit logs (quarterly)
- **Performance Tuning**: Review and optimize slow queries (monthly)

#### Emergency Procedures
- **Cache Failure**: Fallback to direct database queries
- **Database Issues**: Implement read-only mode with cached data
- **Performance Degradation**: Implement query throttling and prioritization
- **Security Incidents**: Immediate audit log review and access restriction

### 10.3 Documentation and Training

#### Administrator Documentation
- **User Guide**: Step-by-step instructions for generating daily reports
- **Troubleshooting Guide**: Common issues and resolution procedures
- **Feature Reference**: Complete feature documentation with examples
- **Best Practices**: Recommendations for optimal report usage

#### Technical Documentation
- **API Reference**: Complete service interface documentation
- **Database Schema**: Table relationships and query optimization
- **Performance Tuning**: Optimization techniques and configuration
- **Security Guidelines**: Access control and audit procedures

## 11. Handler Message Processing Integration

### 11.1 Message Processing Workflow

The Daily Reports function requires a multi-step interaction pattern for date range selection and report generation.

#### 11.1.1 Initial Callback Handler Flow
```go
// handleDailyReport (current implementation)
1. Verify admin permission
2. Display date range selection message
3. Show buttons for common date ranges (Today, Yesterday, Last 7 Days, Last 30 Days, Custom)
4. Wait for date range selection or custom input
```

#### 11.1.2 Date Range Selection Flow (TO BE IMPLEMENTED)
```go
// Callback handler for predefined date ranges
case "admin_daily_report_today":
    return showDailyReport(ctx, callbackQuery, time.Now(), time.Now())
case "admin_daily_report_yesterday":
    yesterday := time.Now().AddDate(0, 0, -1)
    return showDailyReport(ctx, callbackQuery, yesterday, yesterday)
case "admin_daily_report_7days":
    return showDailyReport(ctx, callbackQuery, time.Now().AddDate(0, 0, -7), time.Now())
case "admin_daily_report_30days":
    return showDailyReport(ctx, callbackQuery, time.Now().AddDate(0, 0, -30), time.Now())
case "admin_daily_report_custom":
    return requestCustomDateRange(ctx, callbackQuery)
```

#### 11.1.3 Custom Date Range Input Processing (TO BE IMPLEMENTED)
```go
// Message handler for custom date range input
1. Detect context: User in daily report custom date mode
2. Parse message format: "YYYY-MM-DD YYYY-MM-DD" or "YYYY-MM-DD"
3. Validate date format and range
4. Extract start and end dates
5. Call service layer function
6. Display paginated report or error message
```

### 11.2 Input Parsing Patterns

#### 11.2.1 Date Range Formats
```regex
// Pattern 1: Date range (start and end)
^(\d{4}-\d{2}-\d{2})\s+(\d{4}-\d{2}-\d{2})$

// Pattern 2: Single date (same day report)
^(\d{4}-\d{2}-\d{2})$

// Examples:
// 2025-01-01 2025-01-07    → Report from Jan 1 to Jan 7
// 2025-01-15               → Report for Jan 15 only
```

### 11.3 Service Integration Contract

#### 11.3.1 Service Call Pattern
```go
// Query daily reports with pagination
reports, total, err := service.Admin().GetDailyReport(ctx, startDate, endDate, page, pageSize)

// Error handling patterns
switch {
case errors.Is(err, service.ErrNotAdmin):
    // Send permission denied message
case errors.Is(err, service.ErrInvalidDateRange):
    // Send date range error popup: "日期范围无效"
case errors.Is(err, service.ErrDateRangeTooLarge):
    // Send error: "日期范围过大，最多查询90天"
case err != nil:
    // Send generic error message
default:
    // Display formatted report with pagination
}
```

### 11.4 Response Formatting Patterns

#### 11.4.1 Report Display Template
```go
// Build daily report message
text := fmt.Sprintf(`📊 每日存取款报表
日期范围：%s 至 %s

%s

汇总数据：
总存款金额：%s CNY
总取款金额：%s CNY
净流入：%s CNY

平均每日存款：%s CNY
平均每日取款：%s CNY

页面 %d/%d`,
    startDate.Format("2006-01-02"),
    endDate.Format("2006-01-02"),
    formatDailyEntries(reports),
    totalDeposit,
    totalWithdraw,
    netFlow,
    avgDeposit,
    avgWithdraw,
    currentPage,
    totalPages,
)

// Format individual daily entries
func formatDailyEntries(reports []DailyReport) string {
    var entries []string
    for _, report := range reports {
        entry := fmt.Sprintf("📅 %s\n存款：%s CNY | 取款：%s CNY",
            report.Date,
            report.TotalDeposit,
            report.TotalWithdraw,
        )
        entries = append(entries, entry)
    }
    return strings.Join(entries, "\n\n")
}
```

#### 11.4.2 Pagination Keyboard
```go
func BuildDailyReportKeyboard(ctx context.Context, currentPage, totalPages int, dateRange string) tgbotapi.InlineKeyboardMarkup {
    var rows [][]tgbotapi.InlineKeyboardButton
    
    // Pagination row
    if totalPages > 1 {
        var paginationRow []tgbotapi.InlineKeyboardButton
        
        if currentPage > 1 {
            paginationRow = append(paginationRow,
                tgbotapi.NewInlineKeyboardButtonData(
                    "⬅️ 上一页",
                    fmt.Sprintf("admin_daily_report_page:%d:%s", currentPage-1, dateRange),
                ),
            )
        }
        
        paginationRow = append(paginationRow,
            tgbotapi.NewInlineKeyboardButtonData(
                fmt.Sprintf("%d/%d", currentPage, totalPages),
                "admin_daily_report_current",
            ),
        )
        
        if currentPage < totalPages {
            paginationRow = append(paginationRow,
                tgbotapi.NewInlineKeyboardButtonData(
                    "下一页 ➡️",
                    fmt.Sprintf("admin_daily_report_page:%d:%s", currentPage+1, dateRange),
                ),
            )
        }
        
        rows = append(rows, paginationRow)
    }
    
    // Action buttons
    rows = append(rows,
        []tgbotapi.InlineKeyboardButton{
            tgbotapi.NewInlineKeyboardButtonData("📥 导出报表", fmt.Sprintf("admin_export_report:%s", dateRange)),
            tgbotapi.NewInlineKeyboardButtonData("🔄 刷新", fmt.Sprintf("admin_refresh_report:%s", dateRange)),
        },
        []tgbotapi.InlineKeyboardButton{
            tgbotapi.NewInlineKeyboardButtonData("📅 选择其他日期", "admin_daily_report"),
        },
        []tgbotapi.InlineKeyboardButton{
            tgbotapi.NewInlineKeyboardButtonData("管理员中心", "admin_center"),
        },
    )
    
    return tgbotapi.NewInlineKeyboardMarkup(rows...)
}
```

### 11.5 State Management Requirements

#### 11.5.1 Report Context Tracking
```go
// Store report generation context
type AdminReportState struct {
    AdminUserID int64
    Mode        string    // "awaiting_date_range", "viewing_report"
    StartDate   time.Time
    EndDate     time.Time
    CurrentPage int
    ExpireAt    time.Time
}

// State transitions:
// 1. Click "Daily Report" → Show date range selection
// 2. Select predefined range → Generate and display report
// 3. Select custom → Set mode = "awaiting_date_range"
// 4. Valid date input → Generate and display report
// 5. Navigate pages → Update CurrentPage
// 6. Export/Refresh → Maintain current state
```

### 11.6 Export Functionality

#### 11.6.1 Report Export Handler
```go
func handleExportReport(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
    // Extract date range from callback data
    dateRange := extractDateRangeFromCallback(callbackQuery.Data)
    
    // Generate CSV/Excel file
    fileData, fileName, err := service.Admin().ExportDailyReport(ctx, dateRange.Start, dateRange.End)
    if err != nil {
        return callback.NewAnswerCallback(callbackQuery.ID, "导出失败"), nil
    }
    
    // Send file to admin
    return &callback.SendDocumentResponse{
        CallbackQueryID: callbackQuery.ID,
        ChatID:          callbackQuery.Message.Chat.ID,
        FileData:        fileData,
        FileName:        fileName,
        Caption:         fmt.Sprintf("每日报表导出\n日期：%s 至 %s", 
            dateRange.Start.Format("2006-01-02"),
            dateRange.End.Format("2006-01-02"),
        ),
    }, nil
}
```

### 11.7 Integration Testing Requirements

#### 11.7.1 Date Range Selection Tests
```go
func TestDailyReportDateRangeFlow(t *testing.T) {
    // 1. Test predefined date ranges (today, yesterday, 7 days, 30 days)
    // 2. Test custom date range input:
    //    - Valid date formats
    //    - Invalid date formats
    //    - Future dates
    //    - Date range too large (>90 days)
    // 3. Verify report generation accuracy
    // 4. Test pagination functionality
    // 5. Test export functionality
}
```

#### 11.7.2 Performance Tests
- Large date range queries
- Pagination efficiency
- Cache hit rates
- Concurrent report generation

---

## Conclusion

This comprehensive PRD provides a detailed roadmap for implementing a robust daily reports admin function. The implementation focuses on performance, security, and user experience while maintaining compatibility with the existing system architecture. The phased approach ensures systematic development with clear milestones and deliverables.

The proposed solution addresses all key requirements from the original specification while adding essential enterprise features such as caching, performance optimization, and comprehensive audit capabilities. The technical specifications provide clear guidance for development teams while maintaining flexibility for future enhancements.

**Next Steps:**
1. Review and approve this PRD with stakeholders
2. Begin Phase 1 implementation focusing on core functionality
3. Set up monitoring and testing infrastructure
4. Conduct regular progress reviews and user feedback sessions