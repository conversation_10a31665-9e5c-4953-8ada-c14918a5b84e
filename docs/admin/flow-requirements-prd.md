# Flow Requirements Adjustment Admin Function - Technical PRD

## 1. Function Overview and Business Purpose

### 1.1 Business Context
The Flow Requirements Adjustment function is a critical administrative tool within the Gaming Bot ecosystem that enables authorized administrators to modify user flow requirements (流水要求) for compliance, promotional, and risk management purposes.

### 1.2 Core Business Value
- **Compliance Management**: Ensure users meet minimum wagering requirements before withdrawals
- **Promotional Support**: Adjust flow requirements for deposit bonuses and promotional activities
- **Risk Control**: Manage user flow obligations based on bonus structures and regulatory requirements
- **Operational Flexibility**: Provide admins with tools to handle exceptional cases and user support scenarios

### 1.3 Key Stakeholders
- **Primary**: Gaming platform administrators
- **Secondary**: Customer support agents, compliance officers
- **End Users**: Gaming platform users (recipients of adjustments)

## 2. Current Implementation Status

### 2.1 Infrastructure Analysis
Based on the current codebase analysis:

#### Service Layer (`internal/service/admin.go`)
- **Status**: Interface defined, implementation placeholder
- **Method**: `AdjustUserFlowRequirement(ctx, userID, flowAmount, description)`
- **Permission Check**: `IsCurrentUserAdmin()` implemented
- **Current State**: Returns "功能开发中..." (Under Development)

#### Handler Layer (`internal/admin/handler_callback.go`)
- **Status**: Basic callback handling implemented
- **Callback**: `admin_flow_requirements` → `handleFlowRequirements()`
- **UI Response**: Shows instruction message with back button
- **Current State**: Displays placeholder text, awaits service implementation

#### Internationalization (`manifest/i18n/zh-CN.toml`)
- **Status**: Basic translations available
- **Key Messages**: 
  - `AdminButtonFlowRequirements = "增减流水要求"`
  - `AdminFlowRequirementsInstruction = "增减流水要求功能..."`
- **Coverage**: Basic UI elements translated, detailed messages needed

### 2.2 Missing Components
1. **Database Schema**: Flow requirement tracking tables
2. **Business Logic**: Flow calculation and validation rules
3. **Audit Trail**: Operation logging and history tracking
4. **User Interface**: Input validation and confirmation flows
5. **Notification System**: User alerts for flow adjustments
6. **Integration**: Connection with wallet and gaming systems

## 3. Technical Specifications for Flow Management

### 3.1 Core Business Rules

#### 3.1.1 Flow Requirement Definition
```
Remaining Flow Requirement = Total Flow Requirement - Total Flow Generated
```

#### 3.1.2 Flow Generation Sources
- **Gaming Activity**: User betting amounts in games
- **Manual Admin Additions**: Administrative flow additions via bot/backend

#### 3.1.3 Flow Requirement Sources
- **Deposit Bonuses**: Configurable percentage-based requirements
- **Manual Admin Adjustments**: Administrative requirement modifications
- **Promotional Activities**: Campaign-specific flow obligations

### 3.2 Input Validation Rules

#### 3.2.1 Format Validation
```go
// Input format: @username amount
// Examples: 
//   @agoukuaile 100      (add 100 flow requirement)
//   @agoukuaile -50      (reduce 50 flow requirement)
```

#### 3.2.2 Business Validation
1. **User Existence**: Verify Telegram ID/username exists in platform
2. **Negative Adjustment Validation**: 
   - For negative amounts: `abs(amount) <= current_flow_requirement`
   - Prevent negative flow requirements
3. **Amount Precision**: Support decimal amounts with appropriate precision
4. **Admin Permission**: Verify current user has admin privileges

### 3.3 Data Processing Flow

```mermaid
graph TD
    A[Admin Input] --> B[Format Validation]
    B --> C[User Existence Check]
    C --> D[Permission Verification]
    D --> E[Amount Validation]
    E --> F[Calculate New Flow Requirement]
    F --> G[Update Database]
    G --> H[Create Audit Log]
    H --> I[Send Admin Confirmation]
    I --> J[Send User Notification]
```

## 4. UI/UX Flow Design

### 4.1 Admin Interaction Flow

#### 4.1.1 Entry Point
```
管理员中心 → 【增减流水要求】
```

#### 4.1.2 Instruction Display
```
请回复用户的TelegramID或者用户名，然后加上需要增或者减少的流水要求金额，以空格分隔。

例如：@agoukuaile 100

【○返回】
```

#### 4.1.3 Error Handling UX
```
Format Error → Interactive Popup:
您回复的信息格式错误
[OK]

User Not Found → Interactive Popup:
用户不存在
[OK]

Insufficient Flow → New Message:
操作失败：用户的流水要求不足
Eriq的流水要求：1112.22
```

#### 4.1.4 Success Confirmation
```
Increase Flow Requirement:
Eriq的流水要求增加了100
当前剩余流水要求：1009
【○管理员中心】

Decrease Flow Requirement:
Eriq的流水要求减少了100
当前剩余流水要求：1009
【○管理员中心】
```

### 4.2 User Notification Flow

#### 4.2.1 Flow Requirement Increase
```
管理员已为您成功增加100流水要求
当前剩余流水要求：1009
```

#### 4.2.2 Flow Requirement Decrease
```
管理员已为您成功减少100流水要求
当前剩余流水要求：999
```

## 5. Database Schema Requirements

### 5.1 Core Tables

#### 5.1.1 User Flow Requirements Table
```sql
CREATE TABLE user_flow_requirements (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    tenant_id BIGINT NOT NULL,
    flow_requirement DECIMAL(20,8) NOT NULL DEFAULT 0,
    flow_completed DECIMAL(20,8) NOT NULL DEFAULT 0,
    remaining_requirement DECIMAL(20,8) GENERATED ALWAYS AS (flow_requirement - flow_completed),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_tenant (user_id, tenant_id),
    INDEX idx_remaining (remaining_requirement)
);
```

#### 5.1.2 Flow Requirement Audit Log
```sql
CREATE TABLE flow_requirement_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    tenant_id BIGINT NOT NULL,
    admin_user_id BIGINT NOT NULL,
    operation_type ENUM('INCREASE', 'DECREASE') NOT NULL,
    amount DECIMAL(20,8) NOT NULL,
    previous_requirement DECIMAL(20,8) NOT NULL,
    new_requirement DECIMAL(20,8) NOT NULL,
    description VARCHAR(500),
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_tenant (user_id, tenant_id),
    INDEX idx_admin (admin_user_id),
    INDEX idx_created (created_at)
);
```

#### 5.1.3 Flow Generation Tracking
```sql
CREATE TABLE flow_generation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    tenant_id BIGINT NOT NULL,
    source_type ENUM('GAMING', 'MANUAL_ADMIN') NOT NULL,
    source_reference VARCHAR(100), -- Game session ID, admin operation ID
    flow_amount DECIMAL(20,8) NOT NULL,
    game_provider VARCHAR(50),
    game_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_tenant (user_id, tenant_id),
    INDEX idx_source (source_type, source_reference),
    INDEX idx_created (created_at)
);
```

### 5.2 Data Integrity Constraints

#### 5.2.1 Business Rules
- Flow requirements cannot be negative
- Flow completed cannot exceed flow requirement
- All monetary amounts use DECIMAL(20,8) for precision
- Tenant isolation enforced through tenant_id

#### 5.2.2 Referential Integrity
- Foreign key constraints to users table
- Foreign key constraints to tenants table
- Cascade delete policies for tenant cleanup

## 6. Security Considerations

### 6.1 Access Control

#### 6.1.1 Admin Permission Verification
```go
// Multi-layer permission check
func (s *adminService) AdjustUserFlowRequirement(ctx context.Context, userID uint64, flowAmount string, description string) error {
    // 1. Verify admin permission
    isAdmin, err := s.IsCurrentUserAdmin(ctx)
    if err != nil {
        return err
    }
    if !isAdmin {
        return ErrNotAdmin
    }
    
    // 2. Verify tenant scope
    tenantID, ok := tenant.GetTenantIdFromContext(ctx)
    if !ok {
        return ErrTenantContextMissing
    }
    
    // 3. Verify user belongs to same tenant
    userExists, err := s.verifyUserInTenant(ctx, userID, tenantID)
    if err != nil || !userExists {
        return ErrUserNotInTenant
    }
    
    // Continue with business logic...
}
```

#### 6.1.2 Tenant Isolation
- All operations scoped to current tenant
- Cross-tenant access prevention
- User verification within tenant boundaries

### 6.2 Audit Trail Requirements

#### 6.2.1 Operation Logging
```go
type FlowAdjustmentAudit struct {
    OperationID     string    `json:"operation_id"`
    AdminUserID     uint64    `json:"admin_user_id"`
    TargetUserID    uint64    `json:"target_user_id"`
    TenantID        uint64    `json:"tenant_id"`
    OperationType   string    `json:"operation_type"` // INCREASE/DECREASE
    Amount          string    `json:"amount"`
    PreviousValue   string    `json:"previous_value"`
    NewValue        string    `json:"new_value"`
    Description     string    `json:"description"`
    IPAddress       string    `json:"ip_address"`
    UserAgent       string    `json:"user_agent"`
    Timestamp       time.Time `json:"timestamp"`
}
```

#### 6.2.2 Immutable Audit Records
- All flow adjustments logged with complete context
- No modification of historical audit records
- Cryptographic integrity checks for audit data

### 6.3 Input Sanitization

#### 6.3.1 Data Validation
```go
func validateFlowAdjustmentInput(username, amount string) (*FlowAdjustmentRequest, error) {
    // 1. Sanitize username input
    username = strings.TrimSpace(username)
    if !isValidUsername(username) {
        return nil, ErrInvalidUsername
    }
    
    // 2. Validate amount format and range
    amountDecimal, err := decimal.NewFromString(amount)
    if err != nil {
        return nil, ErrInvalidAmountFormat
    }
    
    // 3. Check amount bounds
    if amountDecimal.Abs().GreaterThan(maxFlowAdjustment) {
        return nil, ErrAmountExceedsLimit
    }
    
    return &FlowAdjustmentRequest{
        Username: username,
        Amount:   amountDecimal,
    }, nil
}
```

#### 6.3.2 SQL Injection Prevention
- Parameterized queries for all database operations
- Input validation before database interaction
- ORM-level protection through GoFrame

## 7. Implementation Roadmap

### 7.1 Phase 1: Foundation (Week 1-2)

#### 7.1.1 Database Schema Implementation
- [ ] Create flow requirement tables
- [ ] Implement audit logging tables
- [ ] Set up indexes and constraints
- [ ] Create migration scripts

#### 7.1.2 Service Layer Core Logic
- [ ] Implement `AdjustUserFlowRequirement` method
- [ ] Add input validation functions
- [ ] Create audit logging mechanisms
- [ ] Implement tenant-scoped queries

### 7.2 Phase 2: Business Logic (Week 3-4)

#### 7.2.1 Flow Calculation Engine
- [ ] Implement flow requirement calculations
- [ ] Add flow generation tracking
- [ ] Create balance validation logic
- [ ] Implement decimal arithmetic safety

#### 7.2.2 User Management Integration
- [ ] User lookup by Telegram ID/username
- [ ] Tenant boundary enforcement
- [ ] User state validation

### 7.3 Phase 3: UI/UX Implementation (Week 5-6)

#### 7.3.1 Message Handling
- [ ] Parse admin input messages
- [ ] Implement validation error responses
- [ ] Create confirmation messages
- [ ] Add interactive popup support

#### 7.3.2 Notification System
- [ ] User notification messages
- [ ] Admin confirmation flows
- [ ] Error handling UX

### 7.4 Phase 4: Testing & Security (Week 7-8)

#### 7.4.1 Security Hardening
- [ ] Permission verification testing
- [ ] Tenant isolation validation
- [ ] Input sanitization verification
- [ ] Audit trail completeness

#### 7.4.2 Integration Testing
- [ ] End-to-end workflow testing
- [ ] Error scenario validation
- [ ] Performance testing
- [ ] User experience validation

## 8. Testing Strategy

### 8.1 Unit Testing

#### 8.1.1 Service Layer Tests
```go
func TestAdjustUserFlowRequirement(t *testing.T) {
    testCases := []struct {
        name           string
        userID         uint64
        amount         string
        expectedError  error
        setupMocks     func()
    }{
        {
            name:   "Valid increase adjustment",
            userID: 12345,
            amount: "100.50",
            expectedError: nil,
        },
        {
            name:   "Valid decrease adjustment",
            userID: 12345,
            amount: "-50.25",
            expectedError: nil,
        },
        {
            name:   "Insufficient flow requirement",
            userID: 12345,
            amount: "-200.00",
            expectedError: ErrInsufficientFlowRequirement,
        },
        {
            name:   "Non-admin user",
            userID: 12345,
            amount: "100.00",
            expectedError: ErrNotAdmin,
        },
    }
    // Test implementation...
}
```

#### 8.1.2 Validation Logic Tests
```go
func TestFlowAdjustmentValidation(t *testing.T) {
    tests := []struct {
        input         string
        expectedValid bool
        expectedError error
    }{
        {"@username 100.50", true, nil},
        {"@username -50.25", true, nil},
        {"invalid format", false, ErrInvalidFormat},
        {"@username abc", false, ErrInvalidAmount},
    }
    // Test implementation...
}
```

### 8.2 Integration Testing

#### 8.2.1 End-to-End Workflows
- Admin login and permission verification
- Complete flow adjustment process
- User notification delivery
- Audit log generation and retrieval

#### 8.2.2 Database Transaction Testing
- Concurrent modification handling
- Transaction rollback scenarios
- Data consistency validation
- Performance under load

### 8.3 Security Testing

#### 8.3.1 Permission Testing
- Non-admin access attempts
- Cross-tenant access prevention
- Session hijacking protection
- Privilege escalation attempts

#### 8.3.2 Input Validation Testing
- SQL injection attempts
- Cross-site scripting (XSS) prevention
- Command injection protection
- Buffer overflow protection

### 8.4 Performance Testing

#### 8.4.1 Load Testing
- Concurrent admin operations
- Database performance under load
- Memory usage optimization
- Response time benchmarks

#### 8.4.2 Scalability Testing
- Multi-tenant performance
- Large dataset handling
- Audit log growth management
- Cache effectiveness

## 9. Monitoring and Observability

### 9.1 Operational Metrics

#### 9.1.1 Business Metrics
- Flow adjustments per day/hour
- Average adjustment amounts
- Admin operation success rates
- User notification delivery rates

#### 9.1.2 Technical Metrics
- Response time percentiles
- Database query performance
- Error rates by operation type
- Memory and CPU utilization

### 9.2 Alerting Strategy

#### 9.2.1 Critical Alerts
- High-value flow adjustments (threshold-based)
- Repeated failed operations
- Unusual admin activity patterns
- Database connectivity issues

#### 9.2.2 Warning Alerts
- Performance degradation
- Audit log growth rate
- User notification failures
- Permission verification failures

### 9.3 Logging Requirements

#### 9.3.1 Structured Logging
```go
log.Info().
    Str("operation", "flow_adjustment").
    Uint64("admin_user_id", adminUserID).
    Uint64("target_user_id", targetUserID).
    Str("amount", amount).
    Str("operation_type", operationType).
    Dur("duration", duration).
    Msg("Flow requirement adjusted successfully")
```

#### 9.3.2 Audit Trail Logging
- Complete operation context
- Before/after state capture
- IP address and user agent tracking
- Correlation ID for request tracing

## 10. Risk Assessment and Mitigation

### 10.1 Technical Risks

#### 10.1.1 Data Consistency Risks
- **Risk**: Concurrent modifications leading to inconsistent state
- **Mitigation**: Database transactions with appropriate isolation levels
- **Detection**: Regular data consistency checks and alerting

#### 10.1.2 Performance Risks
- **Risk**: Slow response times under high admin activity
- **Mitigation**: Database optimization and caching strategies
- **Detection**: Performance monitoring and alerting

### 10.2 Security Risks

#### 10.2.1 Unauthorized Access
- **Risk**: Non-admin users gaining access to flow adjustment features
- **Mitigation**: Multi-layer permission verification and session validation
- **Detection**: Security audit logs and anomaly detection

#### 10.2.2 Data Manipulation
- **Risk**: Malicious modification of flow requirements
- **Mitigation**: Comprehensive audit trails and input validation
- **Detection**: Audit log analysis and fraud detection algorithms

### 10.3 Business Risks

#### 10.3.1 Compliance Violations
- **Risk**: Incorrect flow adjustments affecting regulatory compliance
- **Mitigation**: Validation rules and approval workflows
- **Detection**: Compliance monitoring and reporting

#### 10.3.2 Operational Errors
- **Risk**: Admin mistakes causing user dissatisfaction
- **Mitigation**: Confirmation flows and operation limits
- **Detection**: Error pattern analysis and user feedback monitoring

## 11. Success Criteria and KPIs

### 11.1 Functional Success Criteria
- [ ] 100% admin permission verification accuracy
- [ ] Zero tolerance for cross-tenant data access
- [ ] Complete audit trail for all operations
- [ ] Sub-second response time for typical operations

### 11.2 Business Success Criteria
- [ ] 99.9% operation success rate
- [ ] Zero security incidents related to flow adjustments
- [ ] Positive admin user feedback (>4.5/5)
- [ ] Compliance with all regulatory requirements

### 11.3 Performance KPIs
- [ ] Average response time < 500ms
- [ ] 99th percentile response time < 2000ms
- [ ] Database query efficiency > 95%
- [ ] System availability > 99.9%

## 12. Handler Message Processing Integration

### 12.1 Message Processing Workflow

The Flow Requirements function requires a two-step interaction pattern similar to the Manual Balance function.

#### 12.1.1 Initial Callback Handler Flow
```go
// handleFlowRequirements (current implementation)
1. Verify admin permission
2. Display instruction message with back button
3. Wait for user text message input
```

#### 12.1.2 Message Input Processing Flow (TO BE IMPLEMENTED)
```go
// Message handler for flow requirement adjustments
1. Detect context: User in flow requirements mode
2. Parse message format: "@username amount [description]"
3. Validate input format using regex patterns
4. Extract components:
   - User identifier (username or ID)
   - Flow amount (positive/negative decimal)
   - Description (optional)
5. Call service layer function
6. Handle response and send notifications
```

### 12.2 Input Parsing Patterns

#### 12.2.1 Flow Requirement Adjustment
```regex
// Pattern 1: Username format
^@([a-zA-Z0-9_]+)\s+(-?\d+(?:\.\d+)?)\s*(.*)$

// Pattern 2: User ID format  
^(\d+)\s+(-?\d+(?:\.\d+)?)\s*(.*)$

// Examples:
// @agoukuaile 100              → Add 100 flow requirement
// @agoukuaile -50              → Reduce 50 flow requirement
// 123456789 500.00 活动奖励流水 → Add 500.00 with description
```

### 12.3 Service Integration Contract

#### 12.3.1 Pre-Service Validation
```go
// Before calling service.Admin().AdjustUserFlowRequirement()
1. Parse and validate input format
2. Convert username to user ID if needed (via user service)
3. Validate flow amount using decimal.Decimal:
   - Parse string to decimal
   - Check for valid precision
   - For negative amounts: verify sufficient flow requirement
4. Prepare service call parameters
```

#### 12.3.2 Service Call Pattern
```go
// For flow requirement adjustment
err := service.Admin().AdjustUserFlowRequirement(ctx, userID, flowAmountStr, description)

// Error handling patterns
switch {
case errors.Is(err, service.ErrNotAdmin):
    // Send permission denied message
case errors.Is(err, service.ErrInvalidAmount):
    // Send format error popup
case errors.Is(err, service.ErrUserNotFound):
    // Send user not found popup
case errors.Is(err, service.ErrInsufficientFlowRequirement):
    // Send insufficient flow message with current flow requirement
case err != nil:
    // Send generic error message
default:
    // Send success confirmation
}
```

### 12.4 Response Formatting Patterns

#### 12.4.1 Success Response to Admin
```go
// Template for flow requirement increase
text := fmt.Sprintf("%s的流水要求增加了%s\n当前剩余流水要求：%s\n【管理员中心】", 
    firstName, amount, remainingFlow)

// Template for flow requirement decrease  
text := fmt.Sprintf("%s的流水要求减少了%s\n当前剩余流水要求：%s\n【管理员中心】",
    firstName, amount, remainingFlow)
```

#### 12.4.2 User Notification Messages
```go
// Notification to target user for flow requirement increase
userMsg := fmt.Sprintf("管理员已为您成功增加%s流水要求\n当前剩余流水要求：%s", amount, remainingFlow)

// Notification to target user for flow requirement decrease
userMsg := fmt.Sprintf("管理员已为您成功减少%s流水要求\n当前剩余流水要求：%s", amount, remainingFlow)
```

### 12.5 State Management Requirements

#### 12.5.1 Tracking Admin Context
```go
// Store admin state to track flow requirements mode
type AdminState struct {
    UserID   int64
    Mode     string // "manual_balance", "flow_requirements", etc.
    ExpireAt time.Time
}

// State transitions:
// 1. Click "Flow Requirements" → Set mode = "flow_requirements"
// 2. Send valid adjustment → Clear mode
// 3. Click "Back" → Clear mode
// 4. Timeout after 5 minutes → Clear mode
```

#### 12.5.2 Flow Requirement Validation
- Ensure flow requirement cannot go negative
- Validate against user's current flow requirement
- Check business rules for maximum flow adjustment limits
- Implement transaction-based updates for consistency

### 12.6 Integration Testing Requirements

#### 12.6.1 Handler Integration Tests
```go
// Test complete workflow from callback to service
func TestFlowRequirementsCompleteWorkflow(t *testing.T) {
    // 1. Simulate callback "admin_flow_requirements"
    // 2. Verify instruction message displayed
    // 3. Simulate user message input
    // 4. Verify service function called with correct params
    // 5. Verify success message sent to admin
    // 6. Verify notification sent to target user
}
```

#### 12.6.2 Error Scenario Tests
- Invalid input format variations
- Non-existent user handling
- Insufficient flow requirement scenarios
- Service layer error propagation
- Concurrent adjustment attempts

This comprehensive PRD provides the foundation for implementing a robust, secure, and scalable flow requirements adjustment system that meets both current needs and future growth requirements.