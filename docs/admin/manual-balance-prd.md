# Manual Balance Adjustment Admin Function - Technical PRD

## Document Information
- **Document Type**: Technical Product Requirements Document (PRD)
- **Feature**: Manual Balance Adjustment Admin Function
- **Version**: 1.0
- **Status**: Draft
- **Created**: 2025-01-20
- **Last Updated**: 2025-01-20

## 1. Executive Summary

### 1.1 Function Overview
The Manual Balance Adjustment Admin Function allows authorized administrators to directly modify user wallet balances and flow requirements through the Telegram bot interface. This feature is essential for customer service operations, bonus distributions, compensation payments, and platform maintenance.

### 1.2 Business Purpose
- **Customer Service**: Handle user complaints, deposits that failed to credit properly, or technical issues requiring balance corrections
- **Promotional Activities**: Distribute bonuses, rewards, and promotional funds to users
- **Platform Operations**: Adjust balances for maintenance, testing, or administrative purposes
- **Flow Management**: Manage betting flow requirements for user withdrawals and bonus conditions

### 1.3 Current Implementation Status
- **Admin Permission System**: ✅ Implemented (service layer with tenant-based admin verification)
- **UI Framework**: ✅ Basic structure in place (keyboards, callback handlers)
- **Business Logic**: ❌ Placeholder implementation only (marked as "功能开发中...")
- **Database Schema**: ✅ Complete transaction recording system available
- **Internationalization**: ✅ Basic i18n messages defined

## 2. Technical Specifications

### 2.1 Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Telegram Bot  │────│  Admin Handler  │────│  Admin Service  │
│    Interface    │    │   (Callbacks)   │    │   (Business)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Database    │    │  Transaction    │    │   Validation    │
│     (Wallets,   │◄───│    Service      │◄───│     Layer      │
│  Transactions)  │    │   (Atomicity)   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 Current Code Structure

#### 2.2.1 Service Layer (`internal/service/admin.go`)
```go
type IAdmin interface {
    // Permission management
    IsCurrentUserAdmin(ctx context.Context) (bool, error)
    GetTenantInfo(ctx context.Context) (*entity.Tenants, error)
    
    // Admin operations (PLACEHOLDER IMPLEMENTATION)
    AdjustUserBalance(ctx context.Context, userID uint64, amount string, description string) error
    AdjustUserFlowRequirement(ctx context.Context, userID uint64, flowAmount string, description string) error
}
```

**Current Status**: Permission system fully implemented, core adjustment functions return "功能开发中..." error.

#### 2.2.2 Handler Layer (`internal/admin/handler_callback.go`)
```go
func HandleCallback(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
    switch callbackQuery.Data {
    case "admin_manual_balance":
        return handleManualBalance(ctx, callbackQuery)
    case "admin_flow_requirements":
        return handleFlowRequirements(ctx, callbackQuery)
    }
}
```

**Current Status**: UI navigation implemented, displays instruction messages.

#### 2.2.3 UI Components (`internal/admin/keyboards.go`)
```go
func BuildAdminCenterKeyboard(ctx context.Context) tgbotapi.InlineKeyboardMarkup {
    return tgbotapi.NewInlineKeyboardMarkup(
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData("💰 "+service.I18n().T(ctx, "AdminButtonManualBalance"), "admin_manual_balance"),
        ),
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData("🌊 "+service.I18n().T(ctx, "AdminButtonFlowRequirements"), "admin_flow_requirements"),
        ),
    )
}
```

**Current Status**: Basic keyboard structure implemented.

### 2.3 Database Schema Analysis

#### 2.3.1 Core Tables

**Users Table** (`internal/model/entity/users.go`):
- Primary user information including balance-related fields
- `WithdrawBettingVolume`: Flow requirement field (decimal.Decimal)
- `TenantId`: Multi-tenant support
- Permission fields for various operations

**Wallets Table** (`internal/model/entity/wallets.go`):
- `AvailableBalance`, `FrozenBalance`: Core balance fields (int64)
- `DecimalPlaces`: Precision handling
- `TelegramId`: User identification
- `Symbol`: Currency type

**Transactions Table** (`internal/model/entity/transactions.go`):
- Comprehensive transaction logging system
- `Type`: Transaction type including "system_adjust"
- `Direction`: "in" (increase) / "out" (decrease)
- `Amount`: Transaction amount (decimal.Decimal)
- `BalanceBefore`, `BalanceAfter`: Balance snapshots
- `Memo`: Admin adjustment reason
- `RequestSource`: Source tracking ("admin", "telegram")

#### 2.3.2 Transaction Types for Admin Adjustments
Based on requirements analysis:
- **Balance Adjustment**: `type: "system_adjust"`, `related_entity_type: "admin_balance_adjustment"`
- **Flow Adjustment**: `type: "flow_adjust"`, `related_entity_type: "admin_flow_adjustment"`

### 2.4 Internationalization Support

Current i18n keys in `manifest/i18n/zh-CN.toml`:
```toml
AdminButtonManualBalance = "手动增减余额"
AdminButtonFlowRequirements = "增减流水要求"
AdminManualBalanceInstruction = "手动增减余额功能\n\n请发送格式：\n/balance_adjust <用户ID> <金额> <说明>\n\n例如：\n/balance_adjust 123456 100.50 充值补偿"
AdminFlowRequirementsInstruction = "增减流水要求功能\n\n请发送格式：\n/flow_adjust <用户ID> <流水金额> <说明>\n\n例如：\n/flow_adjust 123456 500.00 活动奖励流水"
AdminWelcome = "你好，管理员"
AdminPermissionDenied = "您没有管理员权限"
AdminPermissionError = "权限验证失败"
```

## 3. UI/UX Flow

### 3.1 Current User Journey

```
Main Menu (with admin permission) 
    ↓ Click "管理员中心"
Admin Center Menu
    ↓ Click "💰 手动增减余额"
Balance Adjustment Instructions
    ↓ User replies with format
[PLACEHOLDER - Not Implemented]
```

### 3.2 Required User Journey (Based on Requirements)

```
Admin Center Menu
    ↓ Click "💰 手动增减余额"
Balance Adjustment Instructions
    ↓ Admin replies: "@username 100" or "@username -50 彩金"
Input Validation
    ↓ [Format OK] [User Exists] [Sufficient Balance for Deduction]
Balance Processing
    ↓ Update wallet + Create transaction record
Success Notification (Admin)
    ↓ Send confirmation message
User Notification
    ↓ Send message to target user
Return to Admin Menu
```

### 3.3 Input Format Requirements

**Balance Adjustment**:
- Format: `@username amount [description]` or `userid amount [description]`
- For "彩金" (bonus flow): `@username amount 彩金`
- Examples:
  - `@agoukuaile 100` (add 100 CNY to balance)
  - `@agoukuaile -50` (deduct 50 CNY from balance)
  - `@agoukuaile 100 彩金` (add 100 to flow/betting volume)

**Flow Requirement Adjustment**:
- Format: `@username flow_amount [description]`
- Example: `@agoukuaile 500` (add 500 to flow requirement)

### 3.4 Validation Rules

1. **Format Validation**: Regex matching for input patterns
2. **User Existence**: Check if username/userid exists in system
3. **Balance Sufficiency**: For deductions, ensure user has sufficient balance
4. **Flow Sufficiency**: For flow deductions, ensure user has sufficient flow
5. **Amount Validation**: Positive/negative numeric validation
6. **Permission Check**: Verify admin permission before each operation

### 3.5 Response Messages

Based on requirements document analysis:

**Success Messages (Balance)**:
- Increase: `"Eriq的CNY增加了100\n当前钱包余额：1009\n【管理员中心】"`
- Decrease: `"Eriq的CNY减少了100\n当前钱包余额：99\n【管理员中心】"`

**Success Messages (Flow)**:
- Increase: `"Eriq的彩金增加了100\n当前流水：1009\n【管理员中心】"`
- Decrease: `"Eriq的彩金减少了100\n当前流水：99\n【管理员中心】"`

**Error Messages**:
- Format Error: `"您回复的信息格式错误"` (Interactive popup)
- User Not Found: `"用户不存在"` (Interactive popup)
- Insufficient Balance: `"操作失败：用户的钱包余额不足\nEriq的钱包余额：1112.22"`
- Insufficient Flow: `"操作失败：用户的流水不足\nEriq的流水：1112.22"`

**User Notifications**:
- Balance Increase: `"恭喜老板，管理员已为您成功上分100CNY\n祝您大吉大利，大放异彩，旗开得胜！"`
- Balance Decrease: `"管理员已为您成功下分100CNY"`
- Flow Increase: `"恭喜老板，管理员已为您成功赠送100彩金\n祝您大吉大利，大放异彩，旗开得胜！"`
- Flow Decrease: `"管理员已为您成功扣除100彩金"`

## 4. Security Considerations

### 4.1 Permission System
**Current Implementation**: ✅ Robust tenant-based admin verification
- Admin verification through `service.Admin().IsCurrentUserAdmin(ctx)`
- Tenant ID validation against `tenants.telegram_account`
- Context-based permission checking

### 4.2 Transaction Security
**Requirements**:
- **Atomicity**: All balance adjustments must use database transactions
- **Audit Trail**: Complete logging of all admin operations
- **Idempotency**: Prevent duplicate adjustments through business_id
- **Balance Validation**: Pre-transaction balance checks

### 4.3 Input Security
**Requirements**:
- Input sanitization for user identifiers and amounts
- SQL injection prevention through parameterized queries
- Amount validation using decimal.Decimal for precision
- Rate limiting for admin operations

### 4.4 Access Control
**Current Status**: ✅ Implemented
- Only users with tenant admin privileges can access functions
- Permission verification on every operation
- Error message differentiation (permission vs. system errors)

## 5. Database Requirements

### 5.1 Required Database Operations

**Balance Adjustment Transaction**:
```sql
BEGIN TRANSACTION;

-- Update wallet balance
UPDATE wallets 
SET available_balance = available_balance + :amount,
    updated_at = NOW()
WHERE user_id = :user_id AND symbol = :currency;

-- Create transaction record
INSERT INTO transactions (
    user_id, type, direction, amount, 
    balance_before, balance_after, memo,
    related_entity_type, request_source, 
    business_id, tenant_id
) VALUES (:params);

COMMIT;
```

**Flow Requirement Adjustment**:
```sql
BEGIN TRANSACTION;

-- Update user flow requirement
UPDATE users 
SET withdraw_betting_volume = withdraw_betting_volume + :flow_amount,
    updated_at = NOW()
WHERE id = :user_id;

-- Create audit log
INSERT INTO admin_balance_adjustment_logs (...) VALUES (...);

COMMIT;
```

### 5.2 New Tables Required

**Admin Balance Adjustment Logs** (Recommended):
```sql
CREATE TABLE admin_balance_adjustment_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    admin_user_id BIGINT NOT NULL,
    target_user_id BIGINT NOT NULL,
    adjustment_type ENUM('balance', 'flow') NOT NULL,
    amount DECIMAL(20,8) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    reason TEXT,
    balance_before DECIMAL(20,8),
    balance_after DECIMAL(20,8),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_tenant_admin (tenant_id, admin_user_id),
    INDEX idx_target_user (target_user_id),
    INDEX idx_created_at (created_at)
);
```

### 5.3 Decimal Precision Requirements
**Important**: All monetary calculations must use `decimal.Decimal` (Shopspring decimal library) as specified in project guidelines.

- **Wallet Balances**: Currently stored as `int64` (requires decimal conversion)
- **Transaction Amounts**: Use `decimal.Decimal` type
- **Flow Requirements**: Use `decimal.Decimal` type

## 6. Implementation Roadmap

### 6.1 Phase 1: Core Balance Adjustment (Priority: High)
**Estimated Time**: 2-3 days

**Tasks**:
1. **Service Layer Implementation**
   - Implement `AdjustUserBalance` method
   - Add user lookup by username/telegram ID
   - Implement balance validation logic
   - Add transaction creation with proper audit trail

2. **Message Handler Implementation**
   - Create message parsing logic for balance adjustment format
   - Add input validation and error handling
   - Implement success/error response generation

3. **Database Integration**
   - Implement atomic transaction processing
   - Add proper decimal handling for balances
   - Create admin operation logging

**Deliverables**:
- Working balance increase/decrease functionality
- Proper error handling and user feedback
- Complete audit trail in database

### 6.2 Phase 2: Flow Requirement Adjustment (Priority: High)
**Estimated Time**: 1-2 days

**Tasks**:
1. **Flow Adjustment Logic**
   - Implement `AdjustUserFlowRequirement` method
   - Add flow balance validation
   - Create flow adjustment transaction records

2. **彩金 (Bonus Flow) Support**
   - Implement bonus flow detection in input parsing
   - Add separate handling for flow vs. balance adjustments
   - Update user notification messages

**Deliverables**:
- Working flow requirement adjustment
- Bonus flow (彩金) functionality
- Proper flow balance validation

### 6.3 Phase 3: User Notifications (Priority: Medium)
**Estimated Time**: 1 day

**Tasks**:
1. **Notification System**
   - Implement user notification sending
   - Add notification templates with proper i18n
   - Handle notification delivery failures

2. **Message Templates**
   - Create all required notification message templates
   - Implement dynamic content generation (amounts, balances)
   - Add proper formatting and branding

**Deliverables**:
- Automatic user notifications for all adjustment types
- Professional notification messages
- Delivery status tracking

### 6.4 Phase 4: Enhanced UI/UX (Priority: Medium)
**Estimated Time**: 1-2 days

**Tasks**:
1. **Interactive Validation**
   - Implement popup confirmations for errors
   - Add real-time input validation feedback
   - Improve error message clarity

2. **Admin Experience Improvements**
   - Add operation confirmation dialogs
   - Implement operation history view
   - Add bulk operation support (future consideration)

**Deliverables**:
- Improved admin user experience
- Interactive error handling
- Enhanced feedback system

### 6.5 Phase 5: Testing & Documentation (Priority: High)
**Estimated Time**: 2 days

**Tasks**:
1. **Comprehensive Testing**
   - Unit tests for all service methods
   - Integration tests for complete workflows
   - Edge case testing (boundary conditions, error scenarios)

2. **Documentation**
   - API documentation for service methods
   - User guide for admin operations
   - Troubleshooting guide

**Deliverables**:
- Complete test coverage
- Admin user documentation
- Technical documentation

## 7. Testing Strategy

### 7.1 Unit Testing
**Coverage Areas**:
- Input parsing and validation logic
- Balance calculation accuracy
- Permission verification
- Error handling scenarios
- Decimal precision calculations

**Test Cases**:
```go
func TestAdjustUserBalance_PositiveAmount(t *testing.T)
func TestAdjustUserBalance_NegativeAmount_SufficientBalance(t *testing.T)
func TestAdjustUserBalance_NegativeAmount_InsufficientBalance(t *testing.T)
func TestAdjustUserBalance_NonExistentUser(t *testing.T)
func TestAdjustUserBalance_NonAdminUser(t *testing.T)
func TestParseBalanceAdjustmentInput_ValidFormats(t *testing.T)
func TestParseBalanceAdjustmentInput_InvalidFormats(t *testing.T)
```

### 7.2 Integration Testing
**Scenarios**:
- Complete balance adjustment workflow (admin input → database update → user notification)
- Flow requirement adjustment workflow
- Permission verification across tenant boundaries
- Transaction atomicity under concurrent operations
- Message delivery and error handling

### 7.3 Security Testing
**Areas**:
- Admin privilege escalation attempts
- Input injection attacks
- Concurrent transaction handling
- Balance manipulation attempts
- Cross-tenant access validation

### 7.4 Performance Testing
**Metrics**:
- Transaction processing latency (<200ms target)
- Database connection handling under load
- Memory usage for decimal calculations
- Notification delivery performance

## 8. Risk Assessment

### 8.1 High Risk Areas

**Financial Accuracy Risk**:
- **Risk**: Decimal precision errors in balance calculations
- **Mitigation**: Use `decimal.Decimal` library exclusively, comprehensive testing
- **Impact**: High (financial loss)

**Permission Bypass Risk**:
- **Risk**: Unauthorized access to admin functions
- **Mitigation**: Multi-layer permission verification, audit logging
- **Impact**: High (security breach)

**Transaction Integrity Risk**:
- **Risk**: Partial transaction completion, balance inconsistencies
- **Mitigation**: Database transactions, rollback procedures, balance validation
- **Impact**: High (data corruption)

### 8.2 Medium Risk Areas

**User Experience Risk**:
- **Risk**: Complex input format causing user errors
- **Mitigation**: Clear instructions, validation feedback, error recovery
- **Impact**: Medium (operational efficiency)

**Notification Delivery Risk**:
- **Risk**: Failed user notifications for balance changes
- **Mitigation**: Retry mechanisms, delivery status tracking, manual notification options
- **Impact**: Medium (user satisfaction)

### 8.3 Monitoring & Alerting

**Required Monitoring**:
- Admin operation frequency and patterns
- Transaction failure rates
- Balance discrepancy detection
- Notification delivery rates
- API response times

**Alerting Thresholds**:
- Failed admin operations >5% in 1 hour
- Large balance adjustments >10,000 CNY
- Multiple failed permission checks from same user
- Database transaction timeouts

## 9. Success Metrics

### 9.1 Functional Metrics
- **Operation Success Rate**: >99.5% for valid admin operations
- **Input Validation Accuracy**: >95% correct error identification
- **Notification Delivery Rate**: >98% successful user notifications
- **Response Time**: <500ms for balance adjustment operations

### 9.2 Security Metrics
- **Zero** unauthorized access incidents
- **100%** audit trail coverage for admin operations
- **Zero** financial discrepancies due to system errors

### 9.3 User Experience Metrics
- **Admin Satisfaction**: Measured through feedback surveys
- **Error Recovery Time**: <30 seconds average for correcting input errors
- **Training Time**: <15 minutes for new admin users

## 10. Dependencies & Prerequisites

### 10.1 Technical Dependencies
- ✅ GoFrame v2 framework
- ✅ Shopspring decimal library
- ✅ Telegram Bot API library
- ✅ Database transaction support
- ✅ Multi-tenant architecture

### 10.2 Database Dependencies
- ✅ Existing wallet and transaction tables
- ✅ User management system
- ✅ Tenant management system
- ❌ Admin operation logging table (needs creation)

### 10.3 Infrastructure Dependencies
- ✅ Database with transaction support
- ✅ Telegram Bot API access
- ✅ Multi-tenant context management
- ✅ I18n system

## 11. Conclusion

The Manual Balance Adjustment Admin Function is a critical component for platform operations with a solid foundation already in place. The current implementation provides excellent security and structural frameworks but requires completion of the core business logic.

**Key Strengths of Current Implementation**:
- Robust permission and security system
- Well-structured service layer architecture
- Complete database schema for transaction recording
- Professional UI framework with i18n support

**Critical Implementation Gaps**:
- Core balance adjustment business logic
- Input parsing and validation
- User notification system
- Error handling and recovery procedures

**Recommended Approach**:
Execute the implementation in the proposed 5-phase roadmap, prioritizing core functionality and security while maintaining the existing architectural patterns. The estimated total implementation time is 7-10 days with proper testing and documentation.

This feature will significantly enhance platform operational capabilities and provide administrators with the tools necessary for effective customer service and platform management.

## 12. Handler Message Processing Integration

### 12.1 Message Processing Workflow

The Manual Balance function requires a two-step interaction pattern: displaying instructions and processing user input messages.

#### 12.1.1 Initial Callback Handler Flow
```go
// handleManualBalance (current implementation)
1. Verify admin permission
2. Display instruction message with back button
3. Wait for user text message input
```

#### 12.1.2 Message Input Processing Flow (TO BE IMPLEMENTED)
```go
// Message handler for manual balance adjustments
1. Detect context: User in manual balance mode
2. Parse message format: "@username amount [description]" or "userid amount [description]"
3. Validate input format using regex patterns
4. Extract components:
   - User identifier (username or ID)
   - Amount (positive/negative decimal)
   - Type (regular balance or 彩金/bonus)
   - Description (optional)
5. Call service layer function
6. Handle response and send notifications
```

### 12.2 Input Parsing Patterns

#### 12.2.1 Regular Balance Adjustment
```regex
// Pattern 1: Username format
^@([a-zA-Z0-9_]+)\s+(-?\d+(?:\.\d+)?)\s*(.*)$

// Pattern 2: User ID format  
^(\d+)\s+(-?\d+(?:\.\d+)?)\s*(.*)$

// Examples:
// @agoukuaile 100              → Add 100 to balance
// @agoukuaile -50              → Deduct 50 from balance
// 123456789 100.50 充值补偿     → Add 100.50 with description
```

#### 12.2.2 Bonus Flow (彩金) Adjustment
```regex
// Pattern: Amount followed by 彩金
^@([a-zA-Z0-9_]+)\s+(-?\d+(?:\.\d+)?)\s*彩金\s*(.*)$

// Examples:
// @agoukuaile 100 彩金         → Add 100 bonus flow
// @agoukuaile -50 彩金         → Deduct 50 bonus flow
```

### 12.3 Service Integration Contract

#### 12.3.1 Pre-Service Validation
```go
// Before calling service.Admin().AdjustUserBalance()
1. Parse and validate input format
2. Convert username to user ID if needed (via user service)
3. Validate amount using decimal.Decimal:
   - Parse string to decimal
   - Check for valid precision
   - For negative amounts: verify sufficient balance
4. Prepare service call parameters
```

#### 12.3.2 Service Call Pattern
```go
// For regular balance adjustment
err := service.Admin().AdjustUserBalance(ctx, userID, amountStr, description)

// Error handling patterns
switch {
case errors.Is(err, service.ErrNotAdmin):
    // Send permission denied message
case errors.Is(err, service.ErrInvalidAmount):
    // Send format error popup
case errors.Is(err, service.ErrUserNotFound):
    // Send user not found popup
case errors.Is(err, service.ErrInsufficientBalance):
    // Send insufficient balance message with current balance
case err != nil:
    // Send generic error message
default:
    // Send success confirmation
}
```

### 12.4 Response Formatting Patterns

#### 12.4.1 Success Response to Admin
```go
// Template for balance increase
text := fmt.Sprintf("%s的CNY增加了%s\n当前钱包余额：%s\n【管理员中心】", 
    firstName, amount, newBalance)

// Template for balance decrease  
text := fmt.Sprintf("%s的CNY减少了%s\n当前钱包余额：%s\n【管理员中心】",
    firstName, amount, newBalance)
```

#### 12.4.2 User Notification Messages
```go
// Notification to target user for balance increase
userMsg := fmt.Sprintf("恭喜老板，管理员已为您成功上分%sCNY\n祝您大吉大利，大放异彩，旗开得胜！", amount)

// Notification to target user for balance decrease
userMsg := fmt.Sprintf("管理员已为您成功下分%sCNY", amount)
```

### 12.5 State Management Requirements

#### 12.5.1 Tracking Admin Context
```go
// Store admin state to track manual balance mode
type AdminState struct {
    UserID   int64
    Mode     string // "manual_balance", "flow_requirements", etc.
    ExpireAt time.Time
}

// State transitions:
// 1. Click "Manual Balance" → Set mode = "manual_balance"
// 2. Send valid adjustment → Clear mode
// 3. Click "Back" → Clear mode
// 4. Timeout after 5 minutes → Clear mode
```

#### 12.5.2 Concurrent Operation Handling
- Prevent multiple admins from adjusting same user simultaneously
- Use database transactions for atomic balance updates
- Implement optimistic locking for balance modifications

### 12.6 Integration Testing Requirements

#### 12.6.1 Handler Integration Tests
```go
// Test complete workflow from callback to service
func TestManualBalanceCompleteWorkflow(t *testing.T) {
    // 1. Simulate callback "admin_manual_balance"
    // 2. Verify instruction message displayed
    // 3. Simulate user message input
    // 4. Verify service function called with correct params
    // 5. Verify success message sent to admin
    // 6. Verify notification sent to target user
}
```

#### 12.6.2 Error Scenario Tests
- Invalid input format variations
- Non-existent user handling
- Insufficient balance scenarios
- Service layer error propagation
- Concurrent adjustment attempts