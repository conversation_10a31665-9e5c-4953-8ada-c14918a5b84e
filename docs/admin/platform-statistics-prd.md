# Platform Statistics Admin Function - Technical PRD

## Document Information
- **Version**: 1.0
- **Date**: 2025-01-20
- **Author**: System Analysis
- **Status**: Technical Requirements Definition

## 1. Function Overview

### 1.1 Business Intelligence Purpose
The Platform Statistics Admin Function provides comprehensive analytics and reporting capabilities for gaming platform administrators to monitor business performance, financial flows, and user activities. This feature serves as the central hub for data-driven decision making in platform operations.

### 1.2 Core Objectives
- **Financial Monitoring**: Real-time tracking of deposits, withdrawals, and commission flows
- **User Analytics**: Comprehensive user behavior and engagement metrics
- **Performance Insights**: Platform health indicators and trend analysis
- **Operational Intelligence**: Admin action tracking and audit trails
- **Business Intelligence**: Revenue analytics and profit/loss calculations

### 1.3 User Personas
- **Primary**: Platform administrators with full access privileges
- **Secondary**: Senior management requiring dashboard views
- **Tertiary**: Financial auditors requiring transaction data

## 2. Current Implementation Status

### 2.1 Service Layer Implementation (`internal/service/admin.go`)

#### 2.1.1 Interface Definition
```go
type IAdmin interface {
    GetPlatformStats(ctx context.Context) (*PlatformStats, error)
    GetPersonalStats(ctx context.Context, userID uint64) (*PersonalStats, error)
    GetDailyReport(ctx context.Context, startDate, endDate string) (*DailyReport, error)
    GetDepositLogs(ctx context.Context, page, pageSize int) (*LogResult, error)
    GetWithdrawLogs(ctx context.Context, page, pageSize int) (*LogResult, error)
    GetMerchantInfo(ctx context.Context) (*MerchantInfo, error)
}
```

#### 2.1.2 Data Structures
```go
type PlatformStats struct {
    TotalUsers      int64   `json:"total_users"`
    TotalDeposits   string  `json:"total_deposits"`
    TotalWithdraws  string  `json:"total_withdraws"`
    TotalCommission string  `json:"total_commission"`
}
```

#### 2.1.3 Current Status
- ✅ **Interface Defined**: Complete method signatures with proper error handling
- ✅ **Permission Validation**: Admin authentication implemented via `IsCurrentUserAdmin()`
- ⚠️ **Placeholder Implementation**: All statistics methods return placeholder data
- ❌ **Database Integration**: No actual data aggregation implemented
- ❌ **Caching Strategy**: No performance optimization implemented

### 2.2 Handler Implementation (`internal/admin/handler_callback.go`)

#### 2.2.1 Current Features
- ✅ **Admin Permission Checks**: Proper validation for all endpoints
- ✅ **Error Handling**: Comprehensive error responses with i18n support
- ✅ **UI Response Structure**: Proper callback response handling
- ⚠️ **Placeholder Messages**: Using development placeholders for most functions

#### 2.2.2 Handler Mapping
```go
case "admin_platform_stats":    // ✅ Implemented with placeholder
case "admin_personal_stats":    // ⚠️ Basic placeholder only
case "admin_daily_report":      // ⚠️ Basic placeholder only
case "admin_deposit_logs":      // ⚠️ Basic placeholder only
case "admin_withdraw_logs":     // ⚠️ Basic placeholder only
case "admin_merchant_info":     // ✅ Implemented with service call
```

### 2.3 Internationalization Support

#### 2.3.1 Implemented i18n Keys (`manifest/i18n/zh-CN.toml`)
```toml
# Platform Statistics
AdminPlatformStatsFormat = "📊 平台统计数据\n\n👥 总用户数: %d\n💰 总存款: %s\n💸 总提现: %s\n💎 总佣金: %s"
AdminPlatformStatsError = "获取平台统计数据失败"
AdminPlatformStatsNoData = "暂无统计数据"

# Admin Functions  
AdminPlatformStatsPlaceholder = "平台统计数据功能开发中..."
AdminPersonalStatsPlaceholder = "个人统计数据功能开发中..."
AdminDailyReportPlaceholder = "查指定日期范围每日存取款功能开发中..."
```

## 3. Technical Specifications

### 3.1 Extended Data Structures

#### 3.1.1 Comprehensive Platform Statistics
```go
type PlatformStats struct {
    // Time-based aggregations
    TodayDeposits     string `json:"today_deposits"`     // 今日总存款
    TodayWithdraws    string `json:"today_withdraws"`    // 今日总取款
    YesterdayDeposits string `json:"yesterday_deposits"` // 昨日总存款
    YesterdayWithdraws string `json:"yesterday_withdraws"` // 昨日总取款
    WeekDeposits      string `json:"week_deposits"`      // 本周总存款
    WeekWithdraws     string `json:"week_withdraws"`     // 本周总取款
    MonthDeposits     string `json:"month_deposits"`     // 本月总存款
    MonthWithdraws    string `json:"month_withdraws"`    // 本月总取款
    LastMonthDeposits string `json:"last_month_deposits"` // 上月总存款
    LastMonthWithdraws string `json:"last_month_withdraws"` // 上月总取款
    
    // Admin operation tracking
    MonthlyAdminDeposits  string `json:"monthly_admin_deposits"`  // 本月管理员手动加额
    MonthlyAdminWithdraws string `json:"monthly_admin_withdraws"` // 本月管理员手动减额
    TotalBonusDeposits    string `json:"total_bonus_deposits"`    // 总加额彩金
    TotalBonusWithdraws   string `json:"total_bonus_withdraws"`   // 总减额彩金
    
    // Lifetime totals
    TotalDeposits   string `json:"total_deposits"`   // 总存款
    TotalWithdraws  string `json:"total_withdraws"`  // 总取款
    
    // Game provider statistics
    Providers []ProviderStats `json:"providers"` // 服务商盈亏数据
    
    // Currency info
    Currency string `json:"currency"` // 币种 (CNY)
}

type ProviderStats struct {
    ProviderName string `json:"provider_name"` // 服务商名称 (PG, PP, Evolution等)
    TodayPnL     string `json:"today_pnl"`     // 今日盈亏
    YesterdayPnL string `json:"yesterday_pnl"` // 昨日盈亏
    WeekPnL      string `json:"week_pnl"`      // 本周盈亏
    MonthPnL     string `json:"month_pnl"`     // 本月盈亏
    LastMonthPnL string `json:"last_month_pnl"` // 上月盈亏
    PrevMonthPnL string `json:"prev_month_pnl"` // 上上月盈亏
}
```

#### 3.1.2 Enhanced Personal Statistics
```go
type PersonalStats struct {
    // User identification
    UserID        uint64 `json:"user_id"`         // 平台ID
    TelegramID    int64  `json:"telegram_id"`     // TelegramID
    FirstName     string `json:"first_name"`      // Eriq
    
    // Financial summary
    TodayDeposits    string `json:"today_deposits"`    // 今日存款
    TodayWithdraws   string `json:"today_withdraws"`   // 今日取款
    YesterdayDeposits string `json:"yesterday_deposits"` // 昨日存款
    YesterdayWithdraws string `json:"yesterday_withdraws"` // 昨日取款
    MonthDeposits    string `json:"month_deposits"`    // 本月存款
    MonthWithdraws   string `json:"month_withdraws"`   // 本月取款
    LastMonthDeposits string `json:"last_month_deposits"` // 上月存款
    LastMonthWithdraws string `json:"last_month_withdraws"` // 上月取款
    
    TotalDeposits  string `json:"total_deposits"`  // 总存款
    TotalWithdraws string `json:"total_withdraws"` // 总取款
    TotalBalance   string `json:"total_balance"`   // 总余额(钱包+游戏)
    TotalPnL       string `json:"total_pnl"`       // 总盈亏
    
    // Game statistics
    GameFlows      []GameFlow `json:"game_flows"`      // 各游戏流水
    TodayPnL       string     `json:"today_pnl"`       // 今日输赢
    FlowRequirement string    `json:"flow_requirement"` // 剩余流水要求
    
    // Account status
    Status   string `json:"status"`   // 用户状态: 正常/已封号
    Currency string `json:"currency"` // 所有单位为CNY
}

type GameFlow struct {
    GameName  string `json:"game_name"`  // 游戏名称
    TodayFlow string `json:"today_flow"` // 今日流水
    YesterdayFlow string `json:"yesterday_flow"` // 昨日流水
}
```

#### 3.1.3 Daily Report Structure
```go
type DailyReport struct {
    DateRange   string      `json:"date_range"`   // 日期范围: 2025-01-01 2025-03-03
    DailyData   []DailyData `json:"daily_data"`   // 每日数据
    TotalCount  int64       `json:"total_count"`  // 总记录数
    Page        int         `json:"page"`         // 当前页
    PageSize    int         `json:"page_size"`    // 每页大小(20条)
}

type DailyData struct {
    Date      string `json:"date"`      // 日期: 2025-01-01
    Deposits  string `json:"deposits"`  // 存款金额
    Withdraws string `json:"withdraws"` // 取款金额
}
```

### 3.2 Database Query Requirements

#### 3.2.1 Platform Statistics Queries
```sql
-- Time-based deposit/withdraw aggregations
SELECT 
    DATE(created_at) as date,
    SUM(CASE WHEN type = 'deposit' THEN amount ELSE 0 END) as daily_deposits,
    SUM(CASE WHEN type = 'withdraw' THEN amount ELSE 0 END) as daily_withdraws
FROM transactions 
WHERE tenant_id = ? 
    AND created_at >= ? 
    AND created_at < ?
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Admin operation aggregations  
SELECT 
    SUM(CASE WHEN type = 'admin_deposit' AND created_at >= ? THEN amount ELSE 0 END) as monthly_admin_deposits,
    SUM(CASE WHEN type = 'admin_withdraw' AND created_at >= ? THEN amount ELSE 0 END) as monthly_admin_withdraws,
    SUM(CASE WHEN type = 'bonus_deposit' THEN amount ELSE 0 END) as total_bonus_deposits,
    SUM(CASE WHEN type = 'bonus_withdraw' THEN amount ELSE 0 END) as total_bonus_withdraws
FROM transactions 
WHERE tenant_id = ?;

-- Game provider statistics (requires game service integration)
SELECT 
    provider_name,
    DATE(created_at) as date,
    SUM(bet_amount) as total_bets,
    SUM(win_amount) as total_wins,
    SUM(bet_amount - win_amount) as pnl
FROM game_transactions 
WHERE tenant_id = ?
GROUP BY provider_name, DATE(created_at)
ORDER BY date DESC, provider_name;
```

#### 3.2.2 Personal Statistics Queries
```sql
-- User transaction summary
SELECT 
    u.user_id,
    u.telegram_id,
    u.first_name,
    COALESCE(SUM(CASE WHEN t.type = 'deposit' THEN t.amount ELSE 0 END), 0) as total_deposits,
    COALESCE(SUM(CASE WHEN t.type = 'withdraw' THEN t.amount ELSE 0 END), 0) as total_withdraws,
    u.wallet_balance + COALESCE(SUM(g.balance), 0) as total_balance
FROM users u
LEFT JOIN transactions t ON u.user_id = t.user_id AND u.tenant_id = t.tenant_id
LEFT JOIN game_balances g ON u.user_id = g.user_id AND u.tenant_id = g.tenant_id
WHERE u.user_id = ? AND u.tenant_id = ?
GROUP BY u.user_id;

-- User game flows
SELECT 
    gt.game_name,
    DATE(gt.created_at) as date,
    SUM(gt.bet_amount) as flow_amount
FROM game_transactions gt
JOIN users u ON gt.user_id = u.user_id AND gt.tenant_id = u.tenant_id
WHERE u.user_id = ? AND u.tenant_id = ?
GROUP BY gt.game_name, DATE(gt.created_at)
ORDER BY date DESC, gt.game_name;
```

#### 3.2.3 Performance Optimization Indexes
```sql
-- Transaction table indexes for fast aggregation
CREATE INDEX idx_transactions_tenant_type_date ON transactions(tenant_id, type, created_at);
CREATE INDEX idx_transactions_user_type_date ON transactions(user_id, tenant_id, type, created_at);

-- Game transaction indexes
CREATE INDEX idx_game_transactions_tenant_provider_date ON game_transactions(tenant_id, provider_name, created_at);
CREATE INDEX idx_game_transactions_user_game_date ON game_transactions(user_id, tenant_id, game_name, created_at);

-- User status index
CREATE INDEX idx_users_tenant_status ON users(tenant_id, status, created_at);
```

### 3.3 Data Aggregation and Calculations

#### 3.3.1 Time Zone Handling
```go
// Use China timezone (GMT+8) for all calculations
func GetChinaTimeRange(date string) (start, end time.Time) {
    loc, _ := time.LoadLocation("Asia/Shanghai")
    t, _ := time.ParseInLocation("2006-01-02", date, loc)
    start = t
    end = t.AddDate(0, 0, 1)
    return
}

// Today's range calculation
func GetTodayRange() (start, end time.Time) {
    now := time.Now().In(time.FixedZone("CST", 8*3600))
    start = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
    end = start.AddDate(0, 0, 1)
    return
}
```

#### 3.3.2 Currency Conversion Logic
```go
// All amounts stored in base currency (CNY)
// Display currency from tenant configuration
func FormatAmount(amount decimal.Decimal, currency string) string {
    return fmt.Sprintf("%.2f %s", amount, currency)
}

// Deposit calculation: Final amount credited to user wallet in CNY
// Withdraw calculation: Amount debited from user wallet in CNY (including fees)
```

#### 3.3.3 Profit/Loss Calculations
```go
type PnLCalculation struct {
    TotalBets decimal.Decimal // 总投注额
    TotalWins decimal.Decimal // 总返奖额  
    HouseEdge decimal.Decimal // 平台抽成
    NetPnL    decimal.Decimal // 净盈亏 = TotalBets - TotalWins
}

// Requires integration with game provider APIs
func CalculateProviderPnL(ctx context.Context, providerName string, startDate, endDate time.Time) (*PnLCalculation, error) {
    // This will depend on provider API capabilities
    // Some providers may not support real-time PnL data
}
```

## 4. UI/UX Design Specifications

### 4.1 Platform Statistics Display

#### 4.1.1 Message Format (Based on PRD Requirements)
```
📊 平台统计数据

今日总存款：1234.56
今日总取款：123456.123
昨日总存款：123456.123  
昨日总取款：123456.123
本周总存款：123456.123
本周总取款：123456.123
本月总存款：123456.123
本月总取款：123456.123
上月总存款：123456.123
上月总取款：123456.123

本月管理员手动加额：117
本月管理员手动减额：123
总加额彩金：1222
总减额彩金：122

总存款：123456.123
总取款：123456.123

服务商：PG
今日盈亏：222.33
昨日盈亏：2233.333
本周盈亏：2333.44
本月盈亏：-2323.344
上月盈亏：12223.889
上上月盈亏：-38484444

所有单位为CNY

【管理员中心】
```

#### 4.1.2 Loading States
```
正在查询，请稍等...
```

#### 4.1.3 Error Handling
```go
// Service error responses
AdminPlatformStatsError = "获取平台统计数据失败"
AdminPlatformStatsNoData = "暂无统计数据"

// Network timeout handling
if err := service.Admin().GetPlatformStats(ctx); err != nil {
    return callback.NewAnswerCallback(callbackID, "数据加载失败，请稍后重试")
}
```

### 4.2 Interactive Elements

#### 4.2.1 Keyboard Layout
```go
// Admin center main keyboard
📊 平台统计数据    👤 个人统计数据
📅 查指定日期范围每日存取款
📥 平台存款日志    📤 平台取款日志  
🏪 商户信息
🔙 返回

// Stats page keyboard
🔄 刷新
🔙 返回管理员中心
```

#### 4.2.2 Responsive Design Considerations
- **Message Length**: Platform stats can be long; consider pagination for mobile
- **Number Formatting**: Use consistent decimal places (2 for amounts)
- **Loading Indicators**: Show progress for slow queries (>2 seconds)

### 4.3 Personal Statistics Interface

#### 4.3.1 User Input Flow
```
1. Click "个人统计数据"
2. Show input prompt: "请回复要查询用户的TelegramID或用户名"
3. User types: "@username" or "123456789"
4. Validate user exists
5. Display comprehensive stats
```

#### 4.3.2 User Stats Display Format
```
Eriq的总数据

平台ID：12345
TelegramID：5322691835

今日存款：1234.23    今日取款：1234.23
昨日存款：1234.23    昨日取款：1234.23
本月存款：1234.23    本月取款：0
上月存款：0         上月取款：0

总存款：10
总取款：5
总余额：20
总盈亏：15

◯ 今日游戏1流水：0    ◯ 今日游戏2流水：1234    ◯ 今日游戏3流水：123
昨日游戏1流水：0     昨日游戏2流水：2        昨日游戏3流水：3

今日输赢：2
剩余流水要求：0

所有单位为CNY
用户状态：正常

【查看Ta指定日期范围流水】
【查看Ta的注单】【查看Ta的账变】
【改变用户状态】
【管理员中心】
```

## 5. Performance Considerations

### 5.1 Real-time vs Cached Data Strategy

#### 5.1.1 Real-time Data Requirements
- **Current Balance Information**: Must be real-time
- **Today's Transactions**: Real-time for accurate monitoring
- **User Status Changes**: Real-time for security

#### 5.1.2 Cached Data Opportunities
- **Historical Statistics**: Daily/monthly aggregations (cache for 1 hour)
- **Provider PnL Data**: May have API rate limits (cache for 15 minutes)
- **User Game Flows**: Historical data (cache for 30 minutes)

#### 5.1.3 Caching Implementation Strategy
```go
type StatsCacheService struct {
    redis  *redis.Client
    ttl    time.Duration
}

func (s *StatsCacheService) GetPlatformStats(ctx context.Context, tenantID uint64) (*PlatformStats, error) {
    cacheKey := fmt.Sprintf("platform_stats:%d", tenantID)
    
    // Try cache first
    if cached, err := s.redis.Get(ctx, cacheKey).Result(); err == nil {
        var stats PlatformStats
        if json.Unmarshal([]byte(cached), &stats) == nil {
            return &stats, nil
        }
    }
    
    // Compute and cache
    stats, err := s.computePlatformStats(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    
    if data, err := json.Marshal(stats); err == nil {
        s.redis.Set(ctx, cacheKey, data, s.ttl)
    }
    
    return stats, nil
}
```

### 5.2 Database Query Optimization

#### 5.2.1 Pagination Strategy
```go
// Large dataset pagination
type PaginationConfig struct {
    DefaultPageSize int  // 20 for logs, 1 for detailed reports
    MaxPageSize     int  // 100 maximum
    Offset         int  // Current offset
}

// Memory-efficient cursor pagination for large datasets
func GetDepositLogs(ctx context.Context, cursor string, limit int) (*LogResult, error) {
    query := `
        SELECT id, user_id, amount, created_at, memo
        FROM transactions 
        WHERE tenant_id = ? AND type = 'deposit' AND id > ?
        ORDER BY id DESC 
        LIMIT ?`
    
    // Use cursor-based pagination for better performance
}
```

#### 5.2.2 Query Result Caching
```go
// Cache expensive aggregation queries
func GetMonthlyAggregations(ctx context.Context, tenantID uint64, month string) (*MonthlyStats, error) {
    cacheKey := fmt.Sprintf("monthly_stats:%d:%s", tenantID, month)
    
    // Historical months never change, cache permanently
    if isHistoricalMonth(month) {
        return getCachedResult(cacheKey, computeMonthlyStats)
    }
    
    // Current month: short cache (5 minutes)
    return getCachedResultWithTTL(cacheKey, computeMonthlyStats, 5*time.Minute)
}
```

### 5.3 Large Dataset Handling

#### 5.3.1 Streaming for Large Reports
```go
func GenerateLargeReport(ctx context.Context, params ReportParams) (<-chan ReportItem, error) {
    resultChan := make(chan ReportItem, 1000)
    
    go func() {
        defer close(resultChan)
        
        // Process in batches to avoid memory issues
        offset := 0
        batchSize := 1000
        
        for {
            batch, err := getReportBatch(ctx, params, offset, batchSize)
            if err != nil || len(batch) == 0 {
                break
            }
            
            for _, item := range batch {
                select {
                case resultChan <- item:
                case <-ctx.Done():
                    return
                }
            }
            
            offset += batchSize
        }
    }()
    
    return resultChan, nil
}
```

#### 5.3.2 Background Aggregation Jobs
```go
// Pre-compute daily statistics at midnight
func ScheduleDailyAggregation() {
    c := cron.New()
    c.AddFunc("0 0 * * *", func() {
        ctx := context.Background()
        yesterday := time.Now().AddDate(0, 0, -1)
        
        // Pre-compute yesterday's statistics for all tenants
        tenants := getAllActiveTenants(ctx)
        for _, tenant := range tenants {
            computeAndCacheDailyStats(ctx, tenant.ID, yesterday)
        }
    })
    c.Start()
}
```

## 6. Security and Data Privacy

### 6.1 Access Control

#### 6.1.1 Admin Permission Validation
```go
func (s *adminService) IsCurrentUserAdmin(ctx context.Context) (bool, error) {
    // Get tenant data and current user
    tenantData, err := s.getCurrentTenantData(ctx)
    if err != nil {
        return false, err
    }
    
    tgUser, err := Update().GetTgUser(ctx)
    if err != nil {
        return false, err
    }
    
    // Compare Telegram IDs
    return tenantData.TelegramAccount == tgUser.ID, nil
}
```

#### 6.1.2 Tenant Data Isolation
```go
// All queries must include tenant_id filter
func GetPlatformStats(ctx context.Context) (*PlatformStats, error) {
    tenantID, ok := tenant.GetTenantIdFromContext(ctx)
    if !ok {
        return nil, gerror.New("tenant ID required")
    }
    
    // All statistics scoped to tenant
    query := `SELECT ... FROM transactions WHERE tenant_id = ?`
    // ...
}
```

### 6.2 Sensitive Data Handling

#### 6.2.1 Data Masking for Non-Admin Users
```go
func MaskSensitiveData(data *PlatformStats, userRole string) *PlatformStats {
    if userRole != "admin" {
        // Mask specific financial data for lower privilege users
        data.TotalDeposits = "***"
        data.TotalWithdraws = "***"
    }
    return data
}
```

#### 6.2.2 Audit Logging
```go
func LogAdminAccess(ctx context.Context, action string, targetUserID uint64) {
    adminUser, _ := Update().GetTgUser(ctx)
    tenantID, _ := tenant.GetTenantIdFromContext(ctx)
    
    auditLog := AuditLog{
        TenantID:     tenantID,
        AdminUserID:  adminUser.ID,
        Action:       action,
        TargetUserID: targetUserID,
        Timestamp:    time.Now(),
        IPAddress:    getClientIP(ctx),
    }
    
    dao.AuditLogs.Insert(ctx, auditLog)
}
```

### 6.3 Data Privacy Compliance

#### 6.3.1 Personal Data Protection
```go
// Personal stats require explicit user consent or admin override
func GetPersonalStats(ctx context.Context, userID uint64) (*PersonalStats, error) {
    isAdmin, err := s.IsCurrentUserAdmin(ctx)
    if err != nil {
        return nil, err
    }
    
    if !isAdmin {
        return nil, ErrNotAdmin
    }
    
    // Log access to personal data
    LogAdminAccess(ctx, "view_personal_stats", userID)
    
    return s.computePersonalStats(ctx, userID)
}
```

#### 6.3.2 Data Retention Policies
```go
// Implement data retention based on regulatory requirements
func CleanupOldAuditLogs(ctx context.Context) error {
    // Keep audit logs for 7 years
    cutoffDate := time.Now().AddDate(-7, 0, 0)
    
    _, err := dao.AuditLogs.Ctx(ctx).
        Where("created_at < ?", cutoffDate).
        Delete()
    
    return err
}
```

## 7. Implementation Roadmap

### 7.1 Phase 1: Core Statistics Implementation (Week 1-2)

#### 7.1.1 Database Schema Updates
- [ ] Create performance indexes for transaction aggregation
- [ ] Add audit logging tables
- [ ] Implement data retention policies

#### 7.1.2 Service Layer Implementation
- [ ] Implement `GetPlatformStats()` with real database queries
- [ ] Add caching layer with Redis integration
- [ ] Implement time zone handling for China (GMT+8)

#### 7.1.3 Basic UI Implementation
- [ ] Complete platform statistics display formatting
- [ ] Add loading states and error handling
- [ ] Implement refresh functionality

### 7.2 Phase 2: Personal Statistics & Logs (Week 3-4)

#### 7.2.1 Personal Statistics
- [ ] Implement user search functionality
- [ ] Complete personal stats data aggregation
- [ ] Add game flow integration
- [ ] Implement user status management

#### 7.2.2 Transaction Logs
- [ ] Implement deposit/withdraw log retrieval
- [ ] Add pagination for large datasets
- [ ] Create log filtering capabilities

### 7.3 Phase 3: Advanced Features (Week 5-6)

#### 7.3.1 Daily Reports
- [ ] Implement date range queries
- [ ] Add export functionality
- [ ] Create summary views

#### 7.3.2 Game Provider Integration
- [ ] Integrate with game provider APIs for PnL data
- [ ] Implement fallback for providers without real-time data
- [ ] Add provider-specific statistics

### 7.4 Phase 4: Performance & Security (Week 7-8)

#### 7.4.1 Performance Optimization
- [ ] Implement background aggregation jobs
- [ ] Add database query optimization
- [ ] Implement streaming for large reports

#### 7.4.2 Security Enhancements
- [ ] Complete audit logging system
- [ ] Add data masking for sensitive information
- [ ] Implement rate limiting for expensive queries

## 8. Testing Strategy

### 8.1 Data Accuracy Testing

#### 8.1.1 Statistical Accuracy Verification
```go
func TestPlatformStatsAccuracy(t *testing.T) {
    // Create known test data
    testData := []Transaction{
        {Type: "deposit", Amount: decimal.NewFromFloat(100.00), Date: today},
        {Type: "withdraw", Amount: decimal.NewFromFloat(50.00), Date: today},
        {Type: "deposit", Amount: decimal.NewFromFloat(200.00), Date: yesterday},
    }
    
    // Verify calculations match expected values
    stats, err := service.Admin().GetPlatformStats(ctx)
    assert.NoError(t, err)
    assert.Equal(t, "100.00", stats.TodayDeposits)
    assert.Equal(t, "50.00", stats.TodayWithdraws)
    assert.Equal(t, "200.00", stats.YesterdayDeposits)
}
```

#### 8.1.2 Time Zone Testing
```go
func TestTimeZoneHandling(t *testing.T) {
    // Test GMT+8 boundary conditions
    chinaTime := time.FixedZone("CST", 8*3600)
    
    // Test day boundary at midnight Beijing time
    testCases := []struct {
        name string
        time time.Time
        expectedDay string
    }{
        {"Before midnight", time.Date(2025, 1, 1, 23, 59, 0, 0, chinaTime), "2025-01-01"},
        {"After midnight", time.Date(2025, 1, 2, 0, 1, 0, 0, chinaTime), "2025-01-02"},
    }
    
    for _, tc := range testCases {
        day := GetDayString(tc.time)
        assert.Equal(t, tc.expectedDay, day, tc.name)
    }
}
```

### 8.2 Performance Testing

#### 8.2.1 Load Testing for Large Datasets
```go
func BenchmarkPlatformStatsQuery(b *testing.B) {
    // Test with various data volumes
    dataSizes := []int{1000, 10000, 100000}
    
    for _, size := range dataSizes {
        b.Run(fmt.Sprintf("DataSize_%d", size), func(b *testing.B) {
            setupTestData(size)
            
            b.ResetTimer()
            for i := 0; i < b.N; i++ {
                _, err := service.Admin().GetPlatformStats(ctx)
                assert.NoError(b, err)
            }
        })
    }
}
```

#### 8.2.2 Cache Performance Testing
```go
func TestCachePerformance(t *testing.T) {
    // First call should hit database
    start := time.Now()
    stats1, err := service.Admin().GetPlatformStats(ctx)
    firstCallDuration := time.Since(start)
    assert.NoError(t, err)
    
    // Second call should hit cache (much faster)
    start = time.Now()
    stats2, err := service.Admin().GetPlatformStats(ctx)
    secondCallDuration := time.Since(start)
    assert.NoError(t, err)
    
    // Cache should be significantly faster
    assert.True(t, secondCallDuration < firstCallDuration/2)
    assert.Equal(t, stats1, stats2)
}
```

### 8.3 Security Testing

#### 8.3.1 Permission Testing
```go
func TestAdminPermissionRequired(t *testing.T) {
    // Test with non-admin user
    ctx := setNonAdminUser(context.Background())
    
    _, err := service.Admin().GetPlatformStats(ctx)
    assert.Error(t, err)
    assert.Equal(t, service.ErrNotAdmin, err)
}

func TestTenantDataIsolation(t *testing.T) {
    // Create data for multiple tenants
    setupMultiTenantTestData()
    
    // Verify each tenant only sees their own data
    tenant1Stats := getPlatformStatsForTenant(1)
    tenant2Stats := getPlatformStatsForTenant(2)
    
    assert.NotEqual(t, tenant1Stats.TotalDeposits, tenant2Stats.TotalDeposits)
}
```

#### 8.3.2 Data Privacy Testing
```go
func TestPersonalDataAccess(t *testing.T) {
    // Test audit logging for personal data access
    userID := uint64(12345)
    _, err := service.Admin().GetPersonalStats(ctx, userID)
    assert.NoError(t, err)
    
    // Verify audit log created
    auditLogs := getAuditLogsForUser(userID)
    assert.Len(t, auditLogs, 1)
    assert.Equal(t, "view_personal_stats", auditLogs[0].Action)
}
```

## 9. Dependencies and Integration Points

### 9.1 External Service Dependencies

#### 9.1.1 Game Provider APIs
```go
// Integration with game providers for PnL data
type GameProviderClient interface {
    GetPlayerStats(ctx context.Context, playerID string, dateRange DateRange) (*PlayerStats, error)
    GetProviderPnL(ctx context.Context, dateRange DateRange) (*ProviderPnL, error)
}

// Handle providers that don't support real-time data
func GetProviderStats(ctx context.Context, provider string) (*ProviderStats, error) {
    client, exists := gameProviders[provider]
    if !exists {
        return &ProviderStats{
            ProviderName: provider,
            Note: "Real-time data not available",
        }, nil
    }
    
    return client.GetProviderPnL(ctx, GetTodayRange())
}
```

#### 9.1.2 Currency Exchange APIs
```go
// For multi-currency support (future enhancement)
type ExchangeRateService interface {
    GetExchangeRate(from, to string) (decimal.Decimal, error)
    ConvertAmount(amount decimal.Decimal, from, to string) (decimal.Decimal, error)
}
```

### 9.2 Internal Service Dependencies

#### 9.2.1 User Service Integration
```go
// Dependency on user service for user data
func GetUserDetails(ctx context.Context, userID uint64) (*UserDetails, error) {
    return service.User().GetUserByID(ctx, userID)
}
```

#### 9.2.2 Transaction Service Integration
```go
// Dependency on transaction service for financial data
func GetTransactionHistory(ctx context.Context, params TransactionParams) (*TransactionResult, error) {
    return service.Transaction().GetTransactionHistory(ctx, params)
}
```

### 9.3 Configuration Dependencies

#### 9.3.1 Tenant Configuration
```go
// Currency and timezone settings from tenant config
type TenantConfig struct {
    Currency string    `json:"currency"`         // CNY, USD, etc.
    Timezone string    `json:"timezone"`         // Asia/Shanghai
    Locale   string    `json:"locale"`           // zh-CN, en-US
}
```

#### 9.3.2 Cache Configuration
```go
// Redis cache settings
type CacheConfig struct {
    RedisURL            string        `json:"redis_url"`
    DefaultTTL          time.Duration `json:"default_ttl"`
    StatsRefreshInterval time.Duration `json:"stats_refresh_interval"`
}
```

## 10. Monitoring and Observability

### 10.1 Performance Metrics

#### 10.1.1 Key Performance Indicators
```go
// Metrics to track
var (
    StatsQueryDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "admin_stats_query_duration_seconds",
            Help: "Duration of admin statistics queries",
        },
        []string{"query_type", "tenant_id"},
    )
    
    CacheHitRate = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "admin_stats_cache_hits_total",
            Help: "Number of cache hits for admin statistics",
        },
        []string{"cache_type"},
    )
)
```

#### 10.1.2 Business Metrics
```go
// Track business-relevant metrics
var (
    AdminAccessCount = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "admin_access_total",
            Help: "Number of admin function accesses",
        },
        []string{"function", "tenant_id"},
    )
    
    DataVolumeProcessed = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "admin_data_volume_processed",
            Help: "Volume of data processed in admin queries",
        },
        []string{"data_type"},
    )
)
```

### 10.2 Error Tracking

#### 10.2.1 Error Classification
```go
// Categorize errors for better monitoring
type AdminError struct {
    Type      string `json:"type"`       // permission, data, service
    Severity  string `json:"severity"`   // low, medium, high, critical
    Context   string `json:"context"`    // Additional context
    UserID    uint64 `json:"user_id"`    // User who triggered error
    TenantID  uint64 `json:"tenant_id"`  // Tenant context
}
```

#### 10.2.2 Alerting Rules
```yaml
# Prometheus alerting rules
groups:
  - name: admin_stats_alerts
    rules:
      - alert: AdminStatsQuerySlow
        expr: admin_stats_query_duration_seconds > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Admin statistics query is slow"
          description: "Query duration {{ $value }}s exceeds threshold"
      
      - alert: AdminStatsDataInconsistency
        expr: increase(admin_stats_data_errors_total[5m]) > 10
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Admin statistics data inconsistency detected"
```

---

**Document Status**: This PRD provides a comprehensive technical foundation for implementing the platform statistics admin function. The implementation should follow the phased approach outlined, with emphasis on data accuracy, performance optimization, and security compliance.

**Next Steps**: 
1. Review and approve technical specifications
2. Begin Phase 1 implementation with database schema updates
3. Set up monitoring and testing infrastructure
4. Implement core statistics functionality with proper caching