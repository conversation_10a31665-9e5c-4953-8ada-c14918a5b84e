# Admin Module Implementation Plan

## Summary of Changes

The Task Master tasks have been successfully updated to reflect the actual state of the codebase. The original tasks assumed starting from scratch, but the reality is:

### What Already Exists ✓
1. **Admin Module Structure**: Complete at `internal/admin/`
2. **Navigation & Menus**: Fully implemented in `keyboards.go` and `handler_callback.go`
3. **Handler Stubs**: All 7 admin functions have handlers that show instructions
4. **Service Interface**: `IAdmin` interface defined with all methods
5. **Authentication**: `IsCurrentUserAdmin()` already checks admin permissions

### What Actually Needs Implementation 🚧
1. **Service Layer**: All methods return "功能开发中..." placeholder
2. **Message Processing**: Handlers only show instructions, no input parsing
3. **Database Tables**: Admin-specific tables for audit logging not created
4. **State Management**: No multi-step workflow handling

## Updated Task List (Priority Order)

### Phase 1: Foundation (Tasks 1-3) - Week 1-2
**Task 1: Create Admin Database Models and Audit Tables**
- Design admin_audit_log, admin_balance_adjustments, admin_flow_adjustments tables
- Create GORM models and migrations
- Foundation for all other features

**Task 2: Implement Admin Service Layer Methods**
- Replace placeholder returns with actual business logic
- Connect to existing DAO layer
- Add proper error handling

**Task 3: Add Message Processing to Existing Admin Handlers**
- Parse user input (usernames, amounts, dates)
- Add state management for multi-step workflows
- Integrate with service layer

### Phase 2: Core Features (Tasks 4-8) - Week 3-4
**Task 4: Implement Manual Balance Adjustment Feature** (High Priority)
- Parse `@username amount [彩金]` format
- Validate and execute balance changes
- Send notifications

**Task 5: Implement Flow Requirements Management** (Medium Priority)
- Calculate and adjust wagering requirements
- Track completed vs remaining

**Task 6: Implement Platform Statistics Dashboard** (Medium Priority)
- Aggregate financial data
- Cache for performance
- Real-time metrics

**Task 7: Implement Personal Statistics and User Management** (Medium Priority)
- User lookup and stats
- Ban/unban functionality
- Transaction history

**Task 8: Implement Withdrawal Processing System** (High Priority)
- Approve/reject withdrawals
- Handle concurrent admins
- Payment gateway integration

### Phase 3: Completion (Tasks 9-10) - Week 5-6
**Task 9: Implement Platform Logs and Reports** (Low Priority)
- Deposit/withdrawal logs
- Daily reports
- Merchant information

**Task 10: Implement Admin Audit System and Notifications** (Medium Priority)
- Comprehensive audit logging
- User notifications
- Rate limiting

## Key Implementation Notes

### Service Layer Pattern
```go
// Current (placeholder)
func (s *adminService) AdjustUserBalance(ctx context.Context, userID uint64, amount string, description string) error {
    return gerror.New("功能开发中...")
}

// Target implementation
func (s *adminService) AdjustUserBalance(ctx context.Context, userID uint64, amount string, description string) error {
    // 1. Validate admin permission
    // 2. Parse and validate amount
    // 3. Begin transaction
    // 4. Update balance
    // 5. Create audit log
    // 6. Send notifications
    // 7. Commit transaction
    return nil
}
```

### Message Processing Pattern
```go
// Current (instructions only)
func handleManualBalance(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) {
    // Shows instructions only
}

// Target implementation
func handleManualBalance(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) {
    // 1. Show instructions
    // 2. Set state to expect user input
    // 3. Process incoming messages
    // 4. Parse @username amount format
    // 5. Call service.Admin().AdjustUserBalance()
    // 6. Show results
}
```

## Testing Strategy

1. **Unit Tests**: Service layer with mocked DAO
2. **Integration Tests**: Database operations and transactions
3. **E2E Tests**: Complete admin workflows via Telegram
4. **Concurrency Tests**: Multi-admin scenarios
5. **Security Tests**: Permission and audit trail verification

## Success Criteria

✅ All service methods return actual data instead of placeholders
✅ Admin can adjust balances and flow requirements via chat
✅ Platform statistics show real aggregated data
✅ All admin actions are logged in audit table
✅ Withdrawal processing handles concurrent admins
✅ User notifications delivered for admin actions
✅ 100% test coverage for critical paths

## Next Steps

1. Start with Task 1: Create database schemas
2. Use `task-master set-status --id=1 --status=in-progress` to begin
3. Follow dependency chain through tasks
4. Test each feature thoroughly before marking complete

---

The implementation is now properly aligned with the existing codebase, focusing on filling the gaps rather than recreating what already exists.