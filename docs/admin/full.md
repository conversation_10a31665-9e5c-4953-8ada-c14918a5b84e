# telegram电子游戏机器人

# 2.13 管理员中心

![](images/75a4aa212710d84c262b2317156884b7862a4fbdfa12772512f72e2146fbeed7.jpg)

![](images/3b98815e195d07bd927f22fa0f762b33553eed1025198e439c9c641599d0445e.jpg)

# 管理员中心菜单

点击Menu中的【管理员中心】，替换消息为如下消息。需要注意，可在后台对某些用户设置其为管理员，只有管理员用户的主菜单中才有【管理员中心】的入口

你好，管理员

【手动增减余额】

【增减流水要求】

【平台统计数据】【个人统计数据】

【查指定日期范围每日存取款】

【平台存款日志】【平台取款日志】

【商户信息】

【○返回】

如果用户之前是管理员，后面取消了其管理员身份，此用户点击历史消息中的【管理员中心】或其他仅限管理员可操作的功能时，需要发送一条新消息，禁止客户操作：

仅限管理员用户可操作

![](images/9cd0c7cf23fcf9b90d061280af76439a10f3992be6ccbdfb15b71ebb048b0c28.jpg)

# 手动增减余额

点击手动增减余额，替换当前消息为如下消息：

请回复用户的TelegramID或者用户名，然后加上需要增或者减少的金额，以空格分隔。例如：  $<$  agoukuaile100

如果是增或者减少彩金，请使用如下格式：@agoukuaile100彩金

# 【返回】

用户回复信息之后，要对用户回复的信息做以下检测：

用户回复信息的格式是否正确，如果不正确，用交互式弹框提示：

# 您回复的信息格式错误

[OK]

点击OK关闭弹框

用户名或TelegramID是否已在平台注册，如果不存在，用交互式弹框提示：

用户不存在

[OK]

点击OK关闭弹框

如果回复的是非彩金(彩金是流水的意思)，而且金额是负数，需要检测（金额\*- 1）是否<=此用户的钱包余额，如果回复的金额>此用户的钱包余额，给用户新发一条消息，内容如下：

操作失败：用户的钱包余额不足

Eriq的钱包余额：1112.22

如果回复的是彩金(即流水)，而且金额是负数，如果（金额\*- 1）是否>此用户当前的流水总金额，给用户新发一条消息，内容如下：

# 操作失败：用户的流水不足

Eriq的流水：1112.22

其中Eriq是要增减额的用户的firstname

用户回复的信息通过了以上检测后，非彩金：

如果是增加余额，在用户现有的钱包余额基础上增加上对应的金额，并回复一条新信息，内容如下：

Eriq的CNY增加了100

当前钱包余额：1009

【管理员中心】

同时要给对应用户的游戏BOT发送一条新消息，内容如下：

恭喜老板，管理员已为您成功上分100CNY

祝您大吉大利，大放异彩，旗开得胜！

如果是负数减额，在用户现有的钱包余额基础上减少对应的金额，并回复一条新信息，内容如下：

Eriq的CNY减少了100

当前钱包余额：99

【管理员中心】

同时要给对应用户的游戏BOT发送一条新消息，内容如下：

管理员已为您成功下分100CNY

Eriq：调额用户的firstnameCNY：后台配置的此机器人商户的账户币种。例如：CNY、PHP、VDN。

100：增或减额的金额

当前钱包余额：用户增或减之后的钱包余额管理员中心：点击新发一条管理员中心消息

手动增减余额要生成账变记录，账变类目详见管理员中心- 个人统计数据- 查看Ta的账变

用户回复的信息通过了以上检测后，如果是彩金：

如果是增加彩金，彩金即流水，在用户现有的流水金额增加上对应的金额，并回复一条新信息，内容如下：

Eriq的彩金增加了100

当前流水：1009

【管理员中心】

同时要给对应用户的游戏BOT发送一条新消息，内容如下：

恭喜老板，管理员已为您成功赠送100彩金

祝您大吉大利，大放异彩，旗开得胜！

如果是负数减额，在用户现有的流水金额减少对应的金额，并回复一条新信息，内容如下：

Eriq的彩金减少了100

当前流水：99

【管理员中心】

同时要给对应用户的游戏BOT发送一条新消息，内容如下：

管理员已为您成功扣除100彩金

Eriq：调额用户的first name100：要增或减的彩金金额当前流水：用户增或减之后的彩金金额管理员中心：点击新发一条管理员中心消息

![](images/6eae47c33dd1d8f88ed3d3905f535ee95ba423f3c3c280b65cf818f1d933ea82.jpg)

注意：上面的手动增减钱包余额和彩金，每操作一次都需要生成对应的记录，以防后面要查看这块的操作记录

# 增减流水要求

点击增加流水要求，替换当前消息为如下消息：

请回复用户的TelegramID或者用户名，然后加上需要增或者减少的流水要求金额，以空格分隔。

例如：@agoukuaile100

# 【○返回】

用户回复信息之后，要对用户回复的信息做以下检测：

用户回复信息的格式是否正确，如果不正确，用交互式弹框提示：

您回复的信息格式错误

[OK]

用户名或TelegramID是否已在平台注册，如果不存在，用交互式弹框提示：

用户不存在

[OK]

点击OK，关闭弹框

如果回复的金额是负数，如果（金额\*- 1）>此用户的当前流水要求金额，给用户新发一条消息，内容如下：

操作失败：用户的流水要求不足

Eriq的流水要求：1112.22

用户回复的信息通过了以上检测后，如果是增加流水要求，在用户现有的流水要求基础上增加上对应的流水要求金额，并回复一条新信息，内容如下：

Eriq的流水要求增加了100

当前剩余流水要求：1009

【O管理员中心】

同时要给对应用户的游戏BOT发送一条新消息，内容如下：

管理员已为您成功增加100流水要求

当前剩余流水要求：1009

用户回复的信息通过了以上检测后，如果是减少流水要求，在用户现有的流水要求基础上减少对应的流水要求，并回复一条新信息，内容如下：

Eriq的流水要求减少了100

当前剩余流水要求：1009

【O管理员中心】

同时要给对应用户的游戏BOT发送一条新消息，内容如下：

管理员已为您成功减少100流水要求

当前剩余流水要求：999

Eriq：用户的firstname

100：增加的流水要求额度

当前剩余流水要求：剩余流水要求  $\equiv$  流水要求- 流水

当前剩余流水要求：剩余流水要求  $\equiv$  流水要求总额- 流水总额

产生流水的地方包括：

用户在游戏中投注，投注额多少，那么用户就会产生多少的流水

管理员在BOT的管理员中心和游戏商户后台，手动给客户增加流水

产生流水要求的地方包括：

- 用户在存款奖励设置的流水要求比例，这块需求详见电子游戏机器人PRO文档- 管理员在BOT的管理中心和游戏商户后台，手动给客户增加流水要求

![](images/ee74bf5b3dc7688fa95680ed3a7988e37f17fd62922b8f3cff3ead21bf7767aa.jpg)

管理员中心：点击新发一条管理员中心消息

注意：上面的增减流水要求，每操作一次都需要生成对应的记录，以防后面要查看这块的操作记录

# 平台统计数据

点击平台统计数据，交互式弹框提示：正在查询，请稍等查询完毕后，给用户回复如下信息，如左侧图2：

今日总存款：1234.56

今日总取款：123456.123

昨日总存款：123456.123

昨日总取款：123456.123

本周总存款：123456.123

本周总取款：123456.123

本月总存款：123456.123

本月总取款：123456.123

上月总存款：123456.123

上月总取款：123456.123

本月管理员手动加额：117

本月管理员手动减额：123

总加额彩金：1222

总减额彩金：122

总存款：123456.123

总取款：123456.123

服务商：PG

今日盈亏：222.33

昨日盈亏：2233.333

本周盈亏：2333.44

本月盈亏：- 2323.344

上月盈亏：12223.889上上月盈亏：- 38484444

所有单位为CNY

【管理员中心】

本月管理员手动加额：通过手动增减余额功能，管理员进行的所有增加钱包余额的金额总和

本月管理员手动减额：通过手动增减余额功能，管理员进行的所有减少钱包余额的金额总和

总加额彩金：通过手动增减余额功能，管理员进行的所有增加彩金额总和

总减额彩金：通过手动增减余额功能，管理员进行的所有减少彩金额总和

上述消息中的总存款和总取款计算范围是此商户下所有用户的存款和取款总和，注意取款数据是包含手续费的取款全额，需要注意：

- 存款数据：无论是通过链上的USDT存款，还是通过DXPay钱包存款，这里的存款数据，都是以最终进入到用户电子游戏机器人钱包余额的CNY为统计依据- 取款数据：无论是通过链上的USDT取款，还是直接取款到DXPay钱包中，这里的取款数据，都是以用户电子游戏人钱包余额出账减少的CNY为统计依据

管理员手动加额是此商户下给所有用户的手动加额总和

管理员手动减额是此商户下给所有用户的手动减额总和

服务商的盈亏数据：服务商在此电子游戏机器人下的盈亏数据。有几个游戏服务商这里就要显示几个服务商的盈亏数据（此数据可能要依赖于服务商的接口能力）

所有单位为CNY：此出CNY是取的后台配置的商户的账户币种，上述所有的数据也是按照此币种进行展示

管理员中心：点击新发一条管理员中心消息

# 个人统计数据

点击个人统计数据，替换消息为如下信息：

请回复要查询用户的TelegramID或用户名

【返回】

用户回复信息之后，要对用户回复的信息做以下检测：

![](images/0dd853d22670c437b176b15ec6ecd15e377c83e4cfdefe3c4f05673ac85b3025.jpg)

用户名或TelegramID是否已在平台注册，如果不存在，用交互式弹框提示：

用户不存在

[OK]

点击OK关闭弹框

如果用户回复的信息通过了检测后，替换当前消息为如下消息，如左侧图2：

Eriq的总数据

平台ID：12345TelegramID：5322691835

今日存款：1234.23今日取款：1234.23

昨日存款：1234.23昨日取款：1234.23

本月存款：1234.23本月取款：0

上月存款：0

上月取款：0

总存款：10

总取款：5

总余额：20

总盈亏：15

$\circledcirc$  今日游戏1流水：0 $\circledcirc$  今日游戏2流水：1234 $\circledcirc$  今日游戏3流水：123

昨日游戏1流水：0  昨日游戏2流水：2  昨日游戏3流水：3  今日输赢：2  剩余流水要求：0

所有单位为CNY  用户状态：正常

【查看Ta指定日期范围流水】

【查看Ta的注单】【查看Ta的账变】

【改变用户状态】

【管理员中心】

平台ID：此用户在我们平台的ID

Eriq：用户的firstname

总存款：用户从注册到目前为止的存款总额。需要注意，无论是通过链上的USDT存款，还是通过DXPay钱包存款，这里的存款数据，都是以最终进入到用户电子游戏机器人钱包余额的CNY为统计依据

总取款：用户从注册到目前为止的取款总额(包含手续费)。需要注意：无论是通过链上的USDT取款，还是直接取款到DXPay钱包中，这里的取款数据，都是以用户电子游戏人钱包余额出账减少的CNY为统计依据

总余额：用户当前的钱包余额+所有游戏的余额

总盈亏：此用户在所有游戏中的盈亏总和（此数据可能要依赖于服务商的接口能力）

游戏流水：用户在游戏中的投注额（此数据可能要依赖于服务商的接口能力）

今日输赢：此用户今日在所有游戏中的盈亏总和（此数据可能要依赖于服务商的接口能力）

剩余流水要求：剩余流水要求  $\equiv$  流水要求- 流水

所有单位为CNY：账户币种可以暂时写死账户币种是CNY，后续要取后台配置。上述所有的数据也是按照此币种进行展示，可以暂时写死账户币种是CNY，后续要取后台配置

用户状态：正常/已封号

# 改变用户状态：

- 如果此用户的状态为正常，点击此按钮，修改用户的状态为已封号，禁止用户在Gaming Bot的一切操作，封号完成用交互式弹框提示： $\bigcirc$  已封号，并且需要把当前消息中的用户状态字段值更新一下

已封号

[OK]

点击OK关闭弹框

- 如果用户此时的状态为已封号，点击此按钮，修改用户的状态为正常，解封完成用交互式弹框提示： $\bigcirc$  已解封，并且需要把当前消息中的用户状态字段值更新一下

已解封

[OK]

点击OK关闭弹框

- 如果某用户的账号被封了，禁止用户在电子游戏机器人下的所有操作（不包括游戏服务商游戏中的操作）。如果用户点击了任何按钮或发送了任何命令，给用户发送一条新消息，内容如下：

$\bigcirc$  很抱歉，您的账号已被封禁。

【联系客服】

联系客服：跳转至客服聊天，客服聊天的username当前写在配置文件或者数据库中。在第二阶段，可以在后台配置。

# 查看Ta指定日期范围的流水

点击个人统计数据中的查看Ta指定日期范围的流水，替换消息为如下信息：

请回复要查询的日期范围，使用空格分

隔，例如：2025- 01- 01 2025- 05- 30

【返回】

![](images/79b99fb2c8ba27de82afc7634c937b9052733cb534768e4982db192128856fca.jpg)

![](images/93d722306266dd29e44aebe3f01c18b2ab0730531d194bf9e30bf05624377b2c.jpg)

# Gaming Bot

# Eriq的注单记录：

游戏1的名称下注0.4返奖0.1获取注单时间：2025- 06- 2622:22:22

游戏2的名称下注0.5返奖0.5获取注单时间：2025- 06- 2522:22:22

游戏3的名称下注0.4返奖0.1获取注单时间：2025- 06- 2422:22:22

游戏2的名称下注0.4返奖0.1获取注单时间：2025- 06- 2322:22:22

游戏1的名称下注0.4返奖0.1获取注单时间：2025- 06- 2222:22:22

游戏3的名称下注0.4返奖0.1获取注单时间：2025- 06- 2422:22:22

#

返回

获取注单时间：2025- 06- 250:00:01

# 游戏3的名称

下注0.4返奖0.1

获取注单时间：2025- 06- 240:00:01

# 游戏2的名称

下注0.4返奖0.1

获取注单时间：2025- 06- 210:00:01

# [→]

# 【O返回】

每天GMT+8的0:00系统自动统计用户昨天的总投注额，并计算用户的投注返水给用户发放，这块需求详见2.11投注返水

Eriq：本次查询用户Tg账户的的Firstname

下注：即此客户的投注额

返奖：即此投注额用户获得的返水

数据记录：需要把此用户在商户下所有游戏的注单记录都显示出来，按照日期由近及远的顺序进行排列：例如：2025- 06- 220:00:01、2025- 06- 210:00:01、2025- 06- 200:00:01

每页显示10条数据，如果只有1页数据，则无需显示下面的翻页按钮。注意，翻页按钮需要按实际情况添加向左和向右翻页按钮。例如：一共5页数据，当用户翻到第5页，只需显示向左翻页按钮，当用户翻到第2- 4页，需要同时显示向左和向右按钮。

返回：点击返回上到一个信息

# 查看Ta的账变

点击查看Ta的账变，替换消息为如下信息：

EriqID:2827377483

账变日志

类型：一

名字：发送红包

金额：11

货币：CNY

Frame 1

# Gaming Bot

# Ericq ID:2827377483

账变日志

类型：- 名字：发送红包金额：11货币：CNY变动后余额：4日期：2025- 06- 24 22:22:23

类型：+名字：游戏1转回钱包金额：11货币：CNY变动后余额：15日期：2025- 06- 24 22:22:23

类型：- 名字：钱包转入游戏2金额：11货币：CNY变动后余额：4日期：2025- 06- 24 22:22:23

类型：+名字：Deposit(存款)金额：11货币：CNY变动后余额：15日期：2025- 06- 24 22:22:23

#

返回

变动后余额：4

日期：2025- 06- 24 22:22:23

类型：+

名字：某游戏转回钱包

金额：11

货币：CNY

变动后余额：15

日期：2025- 06- 24 22:22:23

类型：一

名字：钱包转入某游戏

金额：11

货币：CNY

变动后余额：4

日期：2025- 06- 24 22:22:23

类型：+

名字：Deposit(存款)

金额：11

货币：CNY

变动后余额：15

日期：2025- 06- 24 22:22:23

【→】

【○返回】

账变记录是电子游戏机器人中此用户钱包的变动记录

Eriq：本次查询用户Tg账户的的Firstname，可点击查看用户

ID：本次查询用户Tg账户的ID

类型：如果是用户电子机器人的钱包入账，此处显示十，如果是用户电子机器人的钱包出账，此处是一

名字：对应的账目名称

金额：此次变动的金额

货币：此次变动的币种

变动后的余额：此次变动后用户钱包的余额

日期：变动日期

数据列表按照日期由近及远的顺序进行排列：例如：2025- 06- 2222:22:22、2025- 06- 2122:22:22、2025- 06- 2022:22:22。每页显示10条数据，如果只有1页数据，则无需显示下面的翻页按钮。注意，翻页按钮需要按实际情况添加向左和向右翻页按钮。例如：一共5页数据，当用户翻到第5页，只需显示向左翻页按钮，当用户翻到第2- 4页，需要同时显示向左和向右按钮。

返回：点击返回上到一个信息

下面是电子游戏机器人中用户钱包账户的所有账变类目，以及其类型和名字：

- 场景：用户通过TXPay人民币、TXpay-USDT和USDT(TRC20)真实存款

类型：+

名字：Deposit(存款)

- 场景：用户取款

类型：一

名字：Withdrawal(取款)

- 场景：取款失败退款

类型：+

名字：WithdrawalFail(取款失败退款)

- 场景：发送红包

类型：一

名字：发送红包

- 场景：领取红包

类型：+

名字：领取红包，来自：Eriq

Eriq是发送红包用户的Telegram账户的firstname，可点击查看用户

- 场景：红包过期退款

类型：+

名字：红包过期退款

- 场景：钱包转入某游戏

类型：一

名字：钱包转入某游戏

某游戏需要替换成游戏名称

<table><tr><td colspan="2">类型：+</td></tr><tr><td colspan="2">名字：某游戏转回钱包</td></tr><tr><td colspan="2">某游戏需要替换成游戏名称</td></tr><tr><td colspan="2">·场景：管理员手动加额(非彩金)</td></tr><tr><td colspan="2">类型：+</td></tr><tr><td colspan="2">名字：管理员手动上分</td></tr><tr><td colspan="2">·场景：管理员手动减额(非彩金)</td></tr><tr><td colspan="2">类型：—</td></tr><tr><td colspan="2">名字：管理员手动下分</td></tr><tr><td colspan="2">·场景：用户从直接邀请的用户投注获得的佣金</td></tr><tr><td colspan="2">类型：+</td></tr><tr><td colspan="2">名字：直接邀请返水0.3%，来自：eriq2</td></tr><tr><td colspan="2">eriq2是被邀请用户的Telegram账户的first name，可点击查看用户</td></tr><tr><td colspan="2">·场景：用户从间接邀请的用户投注获得的佣金</td></tr><tr><td colspan="2">类型：+</td></tr><tr><td colspan="2">名字：间接邀请返水0.2%，来自：eriq4</td></tr><tr><td colspan="2">eriq4是被邀请用户的Telegram账户的first name，可点击查看用户</td></tr><tr><td colspan="2">·场景：用户设置了存款奖励为3.0%时存款获得的奖励</td></tr><tr><td colspan="2">类型：+</td></tr><tr><td colspan="2">名字：存款奖励3.0%</td></tr><tr><td colspan="2">·场景：用户设置了存款奖励为5.0%时存款获得的奖励</td></tr><tr><td colspan="2">类型：+</td></tr><tr><td colspan="2">名字：存款奖励5.0%</td></tr><tr><td colspan="2">·场景：用户每天的投注返水</td></tr><tr><td colspan="2">类型：+</td></tr><tr><td colspan="2">名字：投注额：6.64，返水比例0.8%</td></tr><tr><td colspan="2">商户信息</td></tr><tr><td colspan="2">点击商户信息，替换消息为如下信息：</td></tr><tr><td colspan="2">商户信息</td></tr></table>

# Gaming Bot

# 商户信息

商户名：Gaming Bot

总余额：1222.222CNY今日取款手续费：4.23CNY昨日取款手续费：4.44CNY所有取款手续费：2333.33CNY

管理员中心

![](images/beb2ee0be2363ec778456118064ccfb0139ad66362dba88e85aa1f251eeb914d.jpg)

![](images/7e6e57463f2ac315684bb29116a89f089946c33640f44a4fbcdf7a20618b1d90.jpg)

商户名：Gaming Bot

总余额：12333.2222CNY

今日取款手续费：4.23CNY

昨日取款手续费：4.44CNY

所有取款手续费：2333.33CNY

管理员中心】

商户名：此商户机器人的名称

总余额：此商户下所有用户的钱包余额+游戏余额

今日取款手续费：截止目前，今日此商户下所有用户的取款手续费总额

昨日取款手续费：昨日24小时内此商户下所有用户的取款手续费总额

所有取款手续费：此商户下所有用户的取款手续费总额

- 取款手续费定义：用户把电子游戏机器人中的钱包余额通过链上USDT或DXPay钱包的方式取走时，扣除的手续费。详见取款功能的需求说明

管理员中心：点击新发一条管理员中心消息

# 查指定范围日期每日存取款

点击查指定范围日期每日存取款，替换消息为如下信息：

请回复要查询的日期范围，使用空格分隔，例如：205- 01- 012025- 05- 30

用户回复信息之后，要对用户回复的信息做以下检测：

- 日期格式是否正确，如果不正确，用交互式弹框提示用户：格式错误- 开始日期是否<=结束日期，如果不正确，用交互式弹框提示用户：开始日期需小于结束日期

如果用户回复的数据通过了检测，数据查询出来之后回复如下信息，如左侧图3：

日期范围：2025- 01- 012025- 03- 03

日期|存款|取款

![](images/adca7b4d55d83836ea06ea4b58cb4b1b8405679af96381eba43cc4e1b18ec3c8.jpg)

2025- 01- 01|0|0 2025- 01- 02|100|200 2025- 01- 03|100|200 2025- 01- 04|0|0 2025- 01- 05|100|200 2025- 01- 06|100|200 2025- 01- 07|0|0 2025- 01- 08|100|200 2025- 01- 09|100|200 2025- 01- 10|100|200 [→] 【管理员中心】

日期范围：用户要查询的日期

数据列表需要以天为维度，统计出此商户下每天所有用户的存款和取款总额，数据列表按照日期由远及近的顺序进行排列：例如：2025- 01- 01|0|0、2025- 01- 02|100|200、2025- 01- 03|100|200 需要注意：

- 存款数据：无论是通过链上的USDT存款，还是通过DXPay钱包存款，这里的存款数据，都是以最终进入到用户电子游戏机器人钱包余额的CNY为统计依据- 取款数据：无论是通过链上的USDT取款，还是直接取款到DXPay钱包中，这里的取款数据，都是以用户电子游戏人钱包余额出账减少的CNY为统计依据

每页显示20条数据，如果只有1页数据，则无需显示下面的翻页按钮。注意，翻页按钮需要按实际情况添加向左和向右翻页按钮。例如：一共5页数据，当用户翻到第5页，只需显示向左翻页按钮，当用户翻到第2- 4页，需要同时显示向左和向右按钮。

管理员中心：点击新发一条管理员中心消息

# 平台存款日志

点击平台存款日志，替换消息为如下信息：

# 存款日志

当前时间2025- 06- 1815:23:34

存款人：Eriq ID：5322691835

![](images/7404564ff7f35b62bed7bd3bc5978b57c1b4b4c0456d8679d40e09d2f2d4c9b3.jpg)

存款金额：123.233存款时间：2025- 06- 19 11:11:12存款人：Eriq ID：5322691835存款金额：123.233存款时间：2025- 06- 18 11:11:12存款人：Eriq ID：5322691835存款金额：123.233存款时间：2025- 06- 17 11:11:12存款人：Eriq ID：5322691835存款金额：123.233存款时间：2025- 06- 16 11:11:12存款人：Eriq ID：5322691835存款金额：123.233存款时间：2025- 06- 15 11:11:12存款人：Eriq ID：5322691835存款金额：123.233存款时间：2025- 06- 14 11:11:12【→】【○】管理员中心】

当前时间：数据的查询时间

存款人：用户TG账户的firstname，点击可查看用户

ID：用户的TG账户ID

存款金额：无论是通过链上的USDT存款，还是通过DXPay钱包存款，这里的存款数据，都是以最终进入到用户电子游戏机器人钱包余额的CNY为统计依据

数据列表是此商户下所有用户的存款记录，按照存款时间由近及远显示，例如：2025- 06- 16 11:11:12、2025- 06- 15 11:11:12、2025- 06- 14 11:11:12

每页显示20条数据，如果只有1页数据，则无需显示下面的翻页按钮。注意，翻页按钮需要按实际情况添加向左和向右翻页按钮。例如：一共5页数据，当用户翻到第5页，只需显示向左翻页按钮，当用户翻到第2- 4页，需要同时显示向左和向右按钮。

管理员中心：点击新发一条管理员中心消息

# Gaming Bot

# 平台取款日志

点击平台取款日志，替换消息为如下信息：

# 取款日志

当前时间2025- 06- 1815:23:34

取款人：Eriq ID：5322691835

取款金额：123.233

取款时间：2025- 06- 1911:11:12

管理员反馈：Success（取款成功）

取款人：Eriq ID：5322691835

取款金额：123.233

取款时间：2025- 06- 1811:11:12

管理员反馈：Fail（取款失败）

备注：这里是失败原因

取款人：Eriq ID：5322691835

取款金额：123.233

取款时间：2025- 06- 1711:11:12

管理员反馈：Success（取款成功）

备注：https://tronscan.org/?/t

ransaction/bffa858e230ec4392

cc8b31b7a290e38bd0619711785

29236592672edd842e56

取款人：Eriq ID：5322691835

取款金额：123.233

取款时间：2025- 06- 1611:11:12

管理员反馈：Success（取款成功）

# [→]

【管理员中心】

数据列表是此商户下所有用户的取款记录，按照取款时间由近及远显示，例如：2025- 06- 16 11:11:12、2025- 06- 15 11:11:12、2025- 06- 14 11:11:12

当前时间：数据的查询时间

取款人：用户TG账户的firstname，点击可查看用户

ID：用户的TG账户ID

取款金额：无论是通过链上的USDT取款，还是直接取款到DXPay钱包中，这里的取款数据，都是以用户电子游戏人钱包余额出账减少的CNY为统计依据

管理员反馈：如果取款成功，此字段的值为Success（取款成功）。取款失败，此字段的值为Fail（取款失败）

备注：取款失败时，该字段的值为失败原因。取款成功时，如果是人民币取款，此字段无需显示，如果是加密货币取款，此字段需要显示可查询hash信息的地址

https://tronscan.org/#/transaction/+hash，此链接可点击

每页显示20条数据，如果只有1页数据，则无需显示下面的翻页按钮。注意，翻页按钮需要按实际情况添加向左和向右翻页按钮。例如：一共5页数据，当用户翻到第5页，只需显示向左翻页按钮，当用户翻到第2- 4页，需要同时显示向左和向右按钮。

管理员中心：点击新发一条管理员中心消息

# 提现审核

用户点击管理员中心，如果有等待审核的提现申请，需要在管理员中心菜单增加待审核的提现按钮，如果没有等待审核的提现申请，则不需要增加此按钮。具体如下：

你好，管理员

【！处理取款（共1）笔】

【手动增减余额】

【增减流水要求】

【平台统计数据】【个人统计数据】

【查指定日期范围每日存取款】

【平台存款日志】【平台取款日志】

【商户信息】

![](images/2630581744768c6cdc806968fcd44fa8c5d2a7a56928adc150729349d0ef18e9.jpg)

![](images/aacf514147904423e45c596994425942be4cff47dff0bbd4359ba8015dd5d44e.jpg)

![](images/fa0b2a46dcfcc09c722a321e134adae1a8773d831d33a35e4fb498971d775fe8.jpg)

# 【返回】

其中数字1是等待审核的提现申请数量。

点击【！处理取款（共1）笔】按钮，替换当前消息为待审核提现申请列表消息

# 待审核提现申请列表

点击【！处理取款（共2）笔】按钮，要获取最新的等待审核的提现记录（因为有可能已经被其他的管理员处理过了）

如果没有等待审核的提现申请，给客户发送一条新消息，内容如下：

# 暂无需要处理的提现申请

如果有等待审核的提现申请，替换当前消息为审核列表消息：

Eriq申请取款

订单号：22334444

扣除金额：234.92CNY

到账金额：34USDT

手续费：3USDT

提现地址：0X122222suhus433242dssafsa f

[→]

【拒绝】【同意】

【拒绝并封号】

【查看Ta的注单】【查看Ta的账变记录】

# 【查看用户详情】

【查看用户详情】Eriq：提现申请人的Telegram frist name，可点击查看用户订单号：这笔提现的订单号扣除金额：用户的提现金额，是包含手续费的提现金额到账金额：最终到账金额手续费：本次提现的手续费提现地址：如果是链上提现，需要有提现地址的显示，非链上提现无需显示此字段分页：每页显示1条数据，如果只有1页数据，则无需显示下面的翻页按钮。注意，翻页按钮需要按实际情况添加向左和向右翻页按钮。例如：一共5页数据，当用户翻到第5页，只需显示向左翻页按钮，当用户翻到第2- 4页，需要同时显示向左和向右按钮。如果有多条等待申请的提现申请，按照提现申请时间由远到近显示，例如：第一条显示2025- 01- 12 11:22:22，第二条显示：2025- 03- 23 11:22:22

# 拒绝审核

点击拒绝按钮

如果此提现申请已经被其他运营处理了，用交互式弹框给用户提示，内容如下：：

处理失败，该笔提现申请已被处理

[OK]

同时需要更新待审核提现列表的消息

如果此提现申请还没被处理，则需要把该笔提现申请审核拒绝，同时需要更新待审核提现列表的消息。审核拒绝之后用户的BOT会进行提现申请审核拒绝的相应流程，详见Rechange and withdraw cash- 取款- 提现结果

# 同意审核

点击同意按钮

如果此提现申请已经被其他运营处理了，用交互式弹框给用户提示：

处理失败，该笔提现申请已被处理

[OK]

# 同时需要更新待审核提现列表的消息

- 如果此提现申请还没被处理，则需要把该笔提现申请审核通过。同时需要更新待审核提现列表的消息。审核通过之后，即走后续的打款流程。后续的打款流程详见Rechange and withdraw cash的提现模块

# 拒绝并封号

点击拒绝并封号按钮

- 如果此提现申请已经被其他运营处理了，用交互式弹框给用户提示：

处理失败，该笔提现申请已被处理

[OK]

同时需要更新待审核提现列表的消息

- 如果此提现申请还没被处理，则需要把该笔提现申请审核拒绝，并且把此用户的账号状态调整为已封号。同时需要更新待审核提现列表的消息。审核拒绝之后用户的BOT会进行提现申请审核拒绝的相应流程，详见Rechange and withdraw cash-取款-提现结果

# 查看Ta的注单

这块功能和2.1.3管理员中心- 个人统计数据- 查看Ta的注单一样

# 查看Ta的账变记录

这块功能和2.1.3管理员中心- 个人统计数据- 查看Ta的账变一样

# 查看用户详情

点击查看用户详情，新发一条消息，内容如下：

用户名：Eriq平台ID：12345TelegramID：5322691835

总存款：10总取款：5总余额：20总盈亏：15

直接邀请：122人

间接邀请：2233人

今日游戏1流水：0

今日游戏2流水：1234

今日游戏3流水：123

昨日游戏1流水：0

昨日游戏2流水：2

昨日游戏3流水：3

今日输赢：2

剩余流水要求：0

用户状态：正常

用户名：用户的firstname

平台ID：此用户在我们平台的ID

TelegramID：用户的TelegramID

总存款：用户从注册到目前为止的存款总额。需要注意，无论是通过链上的USDT存款，还是通过DXPay钱包存款，这里的存款数据，都是以最终进入到用户电子游戏机器人钱包余额的CNY为统计依据

总取款：用户从注册到目前为止的取款总额(包含手续费)。需要注意：无论是通过链上的USDT取款，还是直接取款到DXPay钱包中，这里的取款数据，都是以用户电子游戏人钱包余额出账减少的CNY为统计依据

总余额：用户当前的钱包余额+所有游戏的余额

总盈亏：此用户在所有游戏中的盈亏总和（此数据可能要依赖于服务商的接口能力）

游戏流水：用户在游戏中的投注额（此数据可能要依赖于服务商的接口能力）

今日输赢：此用户今日在所有游戏中的盈亏总和（此数据可能要依赖于服务商的接口能力）

剩余流水要求：剩余流水要求=流水要求总额- 流水总额

用户状态：正常/已封号