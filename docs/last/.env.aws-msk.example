# AWS MSK (Managed Streaming for Apache Kafka) Configuration Example
# Copy this file to .env and update with your actual AWS MSK settings

# Kafka Broker Configuration
# Replace with your actual AWS MSK broker endpoints
# For SASL/SCRAM authentication, use port 9096
# For TLS authentication, use port 9094
BOT_API_KAFKA_BROKERS_0=b-1.walletmsk01.ojh58v.c3.kafka.ap-southeast-1.amazonaws.com:9096

# General Ka<PERSON>ka Settings
BOT_API_KAFKA_DEV=false

# Producer Configuration
BOT_API_KAFKA_PRODUCERCONFIG_DIALTIMEOUT=3s
BOT_API_KAFKA_PRODUCERCONFIG_WRITETIMEOUT=5s
BOT_API_KAFKA_PRODUCERCONFIG_READTIMEOUT=5s
BOT_API_KAFKA_PRODUCERCONFIG_FLUSHMESSAGES=100
BOT_API_KAFKA_PRODUCERCONFIG_FLUSHFREQUENCY=10ms
BOT_API_KAFKA_PRODUCERCONFIG_RETRYMAX=3

# AWS MSK SASL/SCRAM Authentication
BOT_API_KAFKA_PRODUCERCONFIG_SASLENABLE=true
BOT_API_KAFKA_PRODUCERCONFIG_SASLMECHANISM=SCRAM-SHA-512
BOT_API_KAFKA_PRODUCERCONFIG_SASLUSER=user1
BOT_API_KAFKA_PRODUCERCONFIG_SASLPASSWORD=uQzm2YDzPT

# TLS Configuration (Recommended for AWS MSK)
BOT_API_KAFKA_PRODUCERCONFIG_TLSENABLE=true
BOT_API_KAFKA_PRODUCERCONFIG_TLSINSECURESKIPVERIFY=false

# Consumer Configuration
BOT_API_KAFKA_CONSUMERCONFIG_GROUPID=telegram-bot-consumer-group
BOT_API_KAFKA_CONSUMERCONFIG_REDPACKETCLAIMGROUPID=red-packet-claim-group
BOT_API_KAFKA_CONSUMERCONFIG_REDPACKETMESSAGEUPDATERGROUPID=red-packet-updater-group
BOT_API_KAFKA_CONSUMERCONFIG_UNIFIEDNOTIFIERGROUPID=unified-notifier-group
BOT_API_KAFKA_CONSUMERCONFIG_DIALTIMEOUT=3s
BOT_API_KAFKA_CONSUMERCONFIG_FETCHMIN=10000
BOT_API_KAFKA_CONSUMERCONFIG_FETCHDEFAULT=10000000
BOT_API_KAFKA_CONSUMERCONFIG_MAXWAITTIME=500ms
BOT_API_KAFKA_CONSUMERCONFIG_AUTOCOMMITINTERVAL=1s
BOT_API_KAFKA_CONSUMERCONFIG_RETRYBACKOFF=500ms
BOT_API_KAFKA_CONSUMERCONFIG_SESSIONTIMEOUT=20s
BOT_API_KAFKA_CONSUMERCONFIG_HEARTBEATINTERVAL=3s

# Consumer SASL/SCRAM Authentication (same as producer)
BOT_API_KAFKA_CONSUMERCONFIG_SASLENABLE=true
BOT_API_KAFKA_CONSUMERCONFIG_SASLMECHANISM=SCRAM-SHA-512
BOT_API_KAFKA_CONSUMERCONFIG_SASLUSER=user1
BOT_API_KAFKA_CONSUMERCONFIG_SASLPASSWORD=uQzm2YDzPT

# Consumer TLS Configuration (same as producer)
BOT_API_KAFKA_CONSUMERCONFIG_TLSENABLE=true
BOT_API_KAFKA_CONSUMERCONFIG_TLSINSECURESKIPVERIFY=false

# Kafka Topics
BOT_API_KAFKA_TOPICS_INCOMING=telegram-incoming
BOT_API_KAFKA_TOPICS_OUTGOING=telegram-outgoing
BOT_API_KAFKA_TOPICS_REDPACKETCLAIMREQUESTS=red-packet-claim-requests
BOT_API_KAFKA_TOPICS_REDPACKETMESSAGEUPDATEREQUESTS=red-packet-message-updates
BOT_API_KAFKA_TOPICS_UNIFIEDNOTIFICATIONS=unified-notifications

# Notes:
# 1. Replace broker endpoints with your actual AWS MSK cluster endpoints
# 2. Replace SASL credentials with your actual MSK SCRAM credentials
# 3. For production, consider using AWS IAM authentication instead of SCRAM
# 4. Ensure your security groups allow access to the MSK ports (9094, 9096)
# 5. For enhanced security, use AWS Secrets Manager to store credentials