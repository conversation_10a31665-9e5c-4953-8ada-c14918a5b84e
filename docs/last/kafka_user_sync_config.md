# Kafka 用户同步配置更新

## config.yaml.template 新增配置

```yaml
kafka:
  topics:
    # 现有配置...
    userSyncUpdates: "${BOT_API_KAFKA_TOPICS_USER_SYNC_UPDATES}" # 用户同步更新主题
    
  consumerConfig:
    # 现有配置...
    userSyncGroupId: "${BOT_API_KAFKA_CONSUMER_USER_SYNC_GROUP_ID}" # 用户同步消费者组

# 用户同步特定配置
userSync:
  # 批量更新配置
  batchUpdate:
    loginTimeBatchSize: ${BOT_API_USER_SYNC_LOGIN_TIME_BATCH_SIZE} # 登录时间批量大小，默认100
    batchFlushInterval: "${BOT_API_USER_SYNC_BATCH_FLUSH_INTERVAL}" # 批量刷新间隔，默认30s
    maxBatchWaitTime: "${BOT_API_USER_SYNC_MAX_BATCH_WAIT_TIME}" # 最大等待时间，默认5分钟
  
  # 缓存配置
  cache:
    userDataTTL: "${BOT_API_USER_SYNC_CACHE_USER_DATA_TTL}" # 用户数据缓存TTL，默认10分钟
    compareFields: # 需要比较的字段列表
      - "nickname"
      - "name" 
      - "language"
      - "telegram_username"
      - "first_name"
      - "last_login_time"
    
  # 性能配置
  performance:
    enableAsyncProcessing: ${BOT_API_USER_SYNC_ENABLE_ASYNC_PROCESSING} # 启用异步处理
    maxConcurrentUpdates: ${BOT_API_USER_SYNC_MAX_CONCURRENT_UPDATES} # 最大并发更新数
```

## 环境变量定义

```bash
# Kafka Topics
BOT_API_KAFKA_TOPICS_USER_SYNC_UPDATES=user_sync_updates

# Consumer Groups  
BOT_API_KAFKA_CONSUMER_USER_SYNC_GROUP_ID=user_sync_processor_group

# User Sync Configuration
BOT_API_USER_SYNC_LOGIN_TIME_BATCH_SIZE=100
BOT_API_USER_SYNC_BATCH_FLUSH_INTERVAL=30s
BOT_API_USER_SYNC_MAX_BATCH_WAIT_TIME=5m
BOT_API_USER_SYNC_CACHE_USER_DATA_TTL=10m
BOT_API_USER_SYNC_ENABLE_ASYNC_PROCESSING=true
BOT_API_USER_SYNC_MAX_CONCURRENT_UPDATES=50