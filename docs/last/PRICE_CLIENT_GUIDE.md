# 价格客户端使用指南

## 概述

价格客户端 (`service.IPriceClient`) 为项目中的其他服务提供统一的价格数据访问接口，支持获取加密货币价格和法币兑换价格。

## 快速开始

### 1. 基础用法

```go
package main

import (
    "context"
    "fmt"
    "telegram-bot-api/internal/boot"
    "telegram-bot-api/internal/service"
)

func main() {
    // 1. 初始化应用程序（通常在服务启动时调用一次）
    boot.Initialize(context.Background())
    
    // 2. 获取价格客户端实例
    priceClient := service.PriceClientInstance()
    ctx := context.Background()
    
    // 3. 获取ETH价格
    ethPrice, err := priceClient.GetRealTimePrice(ctx, "ETHUSDT")
    if err != nil {
        fmt.Printf("获取ETH价格失败: %v\n", err)
        return
    }
    
    fmt.Printf("ETH当前价格: $%s\n", ethPrice.Price.String())
    fmt.Printf("24小时涨跌: %s%%\n", ethPrice.Change24h.Mul(decimal.NewFromInt(100)).String())
}
```

### 2. 获取法币价格

```go
// 获取USDT/CNY价格
usdtCnyPrice, err := priceClient.GetFiatPrice(ctx, "USDT", "CNY")
if err != nil {
    fmt.Printf("获取USDT/CNY价格失败: %v\n", err)
    return
}

fmt.Printf("USDT/CNY价格:\n")
fmt.Printf("  买入价: %s %s\n", usdtCnyPrice.BuyPrice.String(), usdtCnyPrice.CurrencySymbol)
fmt.Printf("  卖出价: %s %s\n", usdtCnyPrice.SellPrice.String(), usdtCnyPrice.CurrencySymbol)
fmt.Printf("  中间价: %s %s\n", usdtCnyPrice.MidPrice.String(), usdtCnyPrice.CurrencySymbol)
fmt.Printf("  价差: %.2f%%\n", usdtCnyPrice.SpreadPercent.InexactFloat64())
```

## 接口说明

### IPriceClient 接口

```go
type IPriceClient interface {
    // 获取加密货币实时价格
    GetRealTimePrice(ctx context.Context, symbol string) (*PriceData, error)
    
    // 批量获取加密货币价格
    GetMultiplePrices(ctx context.Context, symbols []string) (map[string]*PriceData, error)
    
    // 获取法币价格
    GetFiatPrice(ctx context.Context, asset, currency string) (*FiatPriceData, error)
    
    // 批量获取法币价格
    GetMultipleFiatPrices(ctx context.Context, pairs []FiatPair) (map[string]*FiatPriceData, error)
    
    // 获取加密货币价格历史
    GetPriceHistory(ctx context.Context, symbol string, from, to time.Time) ([]*PriceData, error)
    
    // 获取法币价格历史
    GetFiatPriceHistory(ctx context.Context, asset, currency string, from, to time.Time) ([]*FiatPriceData, error)
}
```

### 数据结构

#### PriceData (加密货币价格)

```go
type PriceData struct {
    Symbol      string          `json:"symbol"`        // 交易对符号 (ETHUSDT)
    Price       decimal.Decimal `json:"price"`         // 当前价格
    Volume24h   decimal.Decimal `json:"volume_24h"`    // 24小时交易量
    Change24h   decimal.Decimal `json:"change_24h"`    // 24小时涨跌幅 (小数形式)
    High24h     decimal.Decimal `json:"high_24h"`      // 24小时最高价
    Low24h      decimal.Decimal `json:"low_24h"`       // 24小时最低价
    Provider    string          `json:"provider"`      // 数据提供商
    Timestamp   time.Time       `json:"timestamp"`     // 更新时间
    LastUpdated int64           `json:"last_updated"`  // Unix时间戳
}
```

#### FiatPriceData (法币价格)

```go
type FiatPriceData struct {
    Asset          string          `json:"asset"`           // 资产符号 (USDT)
    Currency       string          `json:"currency"`        // 法币符号 (CNY)
    CurrencySymbol string          `json:"currency_symbol"` // 法币符号 (￥)
    BuyPrice       decimal.Decimal `json:"buy_price"`       // 买入价格
    SellPrice      decimal.Decimal `json:"sell_price"`      // 卖出价格
    MidPrice       decimal.Decimal `json:"mid_price"`       // 中间价格 (自动计算)
    Spread         decimal.Decimal `json:"spread"`          // 价差 (自动计算)
    SpreadPercent  decimal.Decimal `json:"spread_percent"`  // 价差百分比 (自动计算)
    Provider       string          `json:"provider"`        // 数据提供商
    Timestamp      time.Time       `json:"timestamp"`       // 更新时间
    LastUpdated    int64           `json:"last_updated"`    // Unix时间戳
}
```

## 实际使用场景

### 1. 在转账服务中使用

```go
type TransferService struct {
    priceClient service.IPriceClient
}

func NewTransferService() *TransferService {
    return &TransferService{
        priceClient: service.PriceClientInstance(),
    }
}

func (s *TransferService) CalculateTransferAmount(ctx context.Context, fromToken, toToken string, amount decimal.Decimal) (decimal.Decimal, error) {
    // 获取代币价格
    fromPrice, err := s.priceClient.GetRealTimePrice(ctx, fromToken+"USDT")
    if err != nil {
        return decimal.Zero, fmt.Errorf("获取%s价格失败: %w", fromToken, err)
    }
    
    toPrice, err := s.priceClient.GetRealTimePrice(ctx, toToken+"USDT")
    if err != nil {
        return decimal.Zero, fmt.Errorf("获取%s价格失败: %w", toToken, err)
    }
    
    // 计算转换金额
    usdValue := amount.Mul(fromPrice.Price)
    toAmount := usdValue.Div(toPrice.Price)
    
    return toAmount, nil
}
```

### 2. 在红包服务中使用

```go
type RedPacketService struct {
    priceClient service.IPriceClient
}

func (s *RedPacketService) GetRedPacketValueInFiat(ctx context.Context, tokenAmount decimal.Decimal, token, fiatCurrency string) (decimal.Decimal, string, error) {
    // 获取代币USDT价格
    tokenPrice, err := s.priceClient.GetRealTimePrice(ctx, token+"USDT")
    if err != nil {
        return decimal.Zero, "", err
    }
    
    // 获取USDT法币价格
    fiatPrice, err := s.priceClient.GetFiatPrice(ctx, "USDT", fiatCurrency)
    if err != nil {
        return decimal.Zero, "", err
    }
    
    // 计算法币价值：代币数量 × 代币单价(USDT) × USDT单价(法币)
    usdValue := tokenAmount.Mul(tokenPrice.Price)
    fiatValue := usdValue.Mul(fiatPrice.MidPrice)
    
    return fiatValue, fiatPrice.CurrencySymbol, nil
}
```

### 3. 在支付服务中使用

```go
type PaymentService struct {
    priceClient service.IPriceClient
}

func (s *PaymentService) ConvertToLocalCurrency(ctx context.Context, usdtAmount decimal.Decimal, localCurrency string) (*PaymentQuote, error) {
    // 获取USDT兑换本地货币的价格
    fiatPrice, err := s.priceClient.GetFiatPrice(ctx, "USDT", localCurrency)
    if err != nil {
        return nil, fmt.Errorf("获取汇率失败: %w", err)
    }
    
    return &PaymentQuote{
        USDTAmount:       usdtAmount,
        LocalAmount:      usdtAmount.Mul(fiatPrice.MidPrice),
        LocalCurrency:    localCurrency,
        CurrencySymbol:   fiatPrice.CurrencySymbol,
        ExchangeRate:     fiatPrice.MidPrice,
        BuyRate:          fiatPrice.BuyPrice,
        SellRate:         fiatPrice.SellPrice,
        Timestamp:        fiatPrice.Timestamp,
    }, nil
}
```

## 批量操作

### 批量获取加密货币价格

```go
symbols := []string{"ETHUSDT", "BTCUSDT", "TRXUSDT"}
prices, err := priceClient.GetMultiplePrices(ctx, symbols)
if err != nil {
    return err
}

for symbol, price := range prices {
    fmt.Printf("%s: $%s\n", symbol, price.Price.String())
}
```

### 批量获取法币价格

```go
pairs := []service.FiatPair{
    {Asset: "USDT", Currency: "CNY"},
    {Asset: "USDT", Currency: "USD"},
    {Asset: "USDT", Currency: "EUR"},
}

fiatPrices, err := priceClient.GetMultipleFiatPrices(ctx, pairs)
if err != nil {
    return err
}

for pair, price := range fiatPrices {
    fmt.Printf("%s: %s %s\n", pair, price.MidPrice.String(), price.CurrencySymbol)
}
```

## 错误处理

```go
ethPrice, err := priceClient.GetRealTimePrice(ctx, "ETHUSDT")
if err != nil {
    // 处理不同类型的错误
    switch {
    case strings.Contains(err.Error(), "stale"):
        // 数据过期，可以选择使用缓存或者等待
        log.Printf("价格数据过期: %v", err)
    case strings.Contains(err.Error(), "NO_DATA"):
        // 没有数据，可能是新币种或者服务问题
        log.Printf("暂无价格数据: %v", err)
    default:
        // 其他错误（网络问题、Redis连接等）
        log.Printf("获取价格失败: %v", err)
    }
    return err
}
```

## 性能优化建议

### 1. 缓存常用价格

```go
type CachedPriceService struct {
    priceClient service.IPriceClient
    cache       map[string]*CachedPrice
    mutex       sync.RWMutex
}

type CachedPrice struct {
    Data      *service.PriceData
    CachedAt  time.Time
    TTL       time.Duration
}

func (s *CachedPriceService) GetPriceWithCache(ctx context.Context, symbol string) (*service.PriceData, error) {
    s.mutex.RLock()
    cached, exists := s.cache[symbol]
    s.mutex.RUnlock()
    
    if exists && time.Since(cached.CachedAt) < cached.TTL {
        return cached.Data, nil
    }
    
    // 缓存过期或不存在，重新获取
    price, err := s.priceClient.GetRealTimePrice(ctx, symbol)
    if err != nil {
        return nil, err
    }
    
    s.mutex.Lock()
    s.cache[symbol] = &CachedPrice{
        Data:     price,
        CachedAt: time.Now(),
        TTL:      30 * time.Second,
    }
    s.mutex.Unlock()
    
    return price, nil
}
```

### 2. 并发获取

```go
func GetMultiplePricesConcurrently(ctx context.Context, priceClient service.IPriceClient, symbols []string) map[string]*service.PriceData {
    results := make(map[string]*service.PriceData)
    var mutex sync.Mutex
    var wg sync.WaitGroup
    
    for _, symbol := range symbols {
        wg.Add(1)
        go func(sym string) {
            defer wg.Done()
            
            price, err := priceClient.GetRealTimePrice(ctx, sym)
            if err != nil {
                log.Printf("获取%s价格失败: %v", sym, err)
                return
            }
            
            mutex.Lock()
            results[sym] = price
            mutex.Unlock()
        }(symbol)
    }
    
    wg.Wait()
    return results
}
```

## 注意事项

1. **数据新鲜度**: 加密货币价格更新频率较高，法币价格更新频率较低
2. **错误处理**: 始终检查错误，价格数据可能暂时不可用
3. **精度处理**: 使用 `decimal.Decimal` 进行价格计算，避免浮点数精度问题
4. **并发安全**: 客户端实例是并发安全的，可以在多个goroutine中使用
5. **Redis依赖**: 客户端依赖Redis连接，确保Redis服务可用

## 完整示例

参考 `examples/price_client_usage.go` 文件查看完整的使用示例。