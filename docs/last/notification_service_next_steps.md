# 统一通知服务下一步行动计划

## 📋 概述

统一通知服务的核心迁移已完成，本文档详细规划了后续的验证、优化和扩展工作。

## 🎯 第一阶段：验证和测试（1-2周）

### 1.1 功能验证测试

#### 支付通知验证
- [ ] **创建支付请求测试**
  - 创建一个支付请求并完成支付
  - 验证付款方收到"支付已发送"通知
  - 验证收款方收到"支付已接收"通知
  - 检查通知内容的准确性（金额、用户名等）

- [ ] **支付通知格式验证**
  - 验证通知消息的多语言支持
  - 检查TraceID是否正确生成和传递
  - 确认通知时间戳准确性

#### 红包通知验证
- [ ] **红包过期通知测试**
  - 创建一个短期过期的红包（如1分钟后过期）
  - 等待红包过期，验证创建者收到过期通知
  - 检查过期通知的内容准确性

- [ ] **红包被领完通知测试**
  - 创建一个小额红包（如1个红包）
  - 领取红包后验证创建者收到"被领完"通知
  - 检查通知中的金额和红包信息

#### 提现通知验证
- [ ] **提现处理流程测试**
  - 创建提现请求
  - 等待定时任务处理（最多5分钟）
  - 验证收到提现成功或失败通知
  - 检查提现失败时的退款是否正确

### 1.2 性能和稳定性测试

#### 并发测试
- [ ] **高并发通知发送测试**
  - 同时创建多个支付请求
  - 验证所有通知都能正确发送
  - 检查Kafka消息队列是否有积压

#### 错误处理测试
- [ ] **Kafka故障测试**
  - 模拟Kafka服务不可用
  - 验证错误日志记录正确
  - 确认业务流程不受影响

- [ ] **通知服务异常测试**
  - 测试无效的通知参数
  - 验证错误处理和日志记录
  - 确认系统稳定性

### 1.3 兼容性验证

#### 消费者兼容性
- [ ] **unified_notifier兼容性测试**
  - 验证新通知消息能被正确消费
  - 检查消息格式兼容性
  - 确认用户能正常收到Telegram通知

#### 向后兼容性
- [ ] **现有功能不受影响验证**
  - 验证其他业务功能正常工作
  - 确认没有引入新的bug
  - 检查系统整体稳定性

## 🔧 第二阶段：优化和清理（1周）

### 2.1 旧代码清理

#### payment_notifier清理
- [ ] **确认依赖关系**
  ```bash
  # 搜索payment_notifier的使用
  grep -r "payment_notifier" internal/
  grep -r "PaymentNotification" internal/
  ```

- [ ] **移除旧的支付通知处理器**
  - [ ] 移除 `internal/bot/payment_notifier/` 目录
  - [ ] 更新 `internal/cmd/processor/main.go` 移除相关消费者
  - [ ] 清理相关的Kafka topic配置

#### 配置清理
- [ ] **更新Kafka配置**
  - 移除 `kafka.topics.paymentNotifications` 配置
  - 移除 `kafka.consumerConfig.paymentNotifierGroupId` 配置
  - 保留 `kafka.topics.unifiedNotifications` 配置

### 2.2 代码优化

#### 错误处理优化
- [ ] **统一错误码定义**
  ```go
  // 在 internal/consts/ 中定义通知相关错误码
  const (
      NotificationErrorInvalidRequest = "NOTIFICATION_INVALID_REQUEST"
      NotificationErrorKafkaFailure   = "NOTIFICATION_KAFKA_FAILURE"
      // ...
  )
  ```

#### 日志优化
- [ ] **结构化日志**
  - 统一通知相关的日志格式
  - 添加更多上下文信息
  - 优化日志级别设置

## 📈 第三阶段：监控和告警（1-2周）

### 3.1 指标收集

#### 通知发送指标
- [ ] **实现指标收集**
  ```go
  // 在notification service中添加指标
  type NotificationMetrics struct {
      TotalSent     int64
      TotalFailed   int64
      SendDuration  time.Duration
      ByType        map[string]int64
  }
  ```

- [ ] **关键指标定义**
  - 通知发送总数
  - 通知发送失败率
  - 通知发送延迟
  - 各类型通知的发送量

#### 业务指标
- [ ] **红包过期处理指标**
  - 每次处理的过期红包数量
  - 处理耗时
  - 处理失败率

- [ ] **提现处理指标**
  - 提现处理成功率
  - 处理延迟
  - 退款成功率

### 3.2 告警配置

#### 关键告警
- [ ] **通知发送失败率告警**
  - 阈值：5分钟内失败率 > 10%
  - 通知方式：邮件 + 钉钉/企微

- [ ] **Kafka连接异常告警**
  - 阈值：连续3次连接失败
  - 通知方式：立即告警

- [ ] **定时任务异常告警**
  - 红包过期处理失败
  - 提现处理失败

#### 业务告警
- [ ] **提现退款失败告警**
  - 提现失败但退款也失败的情况
  - 需要人工介入处理

## 🚀 第四阶段：功能扩展（2-4周）

### 4.1 充值通知集成

#### 充值检测点识别
- [ ] **查找充值到账逻辑**
  ```bash
  # 搜索充值相关代码
  grep -r "deposit" internal/
  grep -r "充值" internal/
  ```

- [ ] **集成充值成功通知**
  ```go
  // 在充值到账处添加通知
  notificationReq := &service.DepositNotificationRequest{
      UserID:  userID,
      Amount:  amount.String(),
      Symbol:  tokenSymbol,
      TxHash:  transactionHash,
      Wallet:  walletAddress,
  }
  service.Notification().SendDepositNotification(ctx, notificationReq)
  ```

### 4.2 通知模板系统

#### 动态模板支持
- [ ] **模板配置化**
  - 将通知文本模板移到配置文件
  - 支持运行时更新模板
  - 支持A/B测试不同模板

#### 个性化通知
- [ ] **用户偏好设置**
  - 用户可选择接收哪些类型的通知
  - 支持通知时间段设置
  - 支持通知方式选择

### 4.3 多渠道通知支持

#### 邮件通知
- [ ] **邮件服务集成**
  ```go
  type EmailNotificationRequest struct {
      UserEmail string
      Subject   string
      Content   string
      Template  string
  }
  ```

#### 短信通知
- [ ] **短信服务集成**
  - 重要通知支持短信发送
  - 支持国际短信
  - 成本控制和频率限制

## 📊 第五阶段：数据分析和优化（持续进行）

### 5.1 通知效果分析

#### 用户行为分析
- [ ] **通知点击率统计**
  - 统计用户对不同类型通知的响应
  - 分析最佳通知时间
  - 优化通知内容

#### 业务影响分析
- [ ] **通知对业务的影响**
  - 支付完成率与通知的关系
  - 红包参与度与通知的关系
  - 用户留存与通知的关系

### 5.2 性能优化

#### 批量处理优化
- [ ] **批量通知发送**
  - 对于大量通知，支持批量发送
  - 减少Kafka连接开销
  - 提高吞吐量

#### 缓存优化
- [ ] **通知模板缓存**
  - 缓存常用的通知模板
  - 减少数据库查询
  - 提高响应速度

## 🔍 验证检查清单

### 每个阶段完成后的检查项

#### 阶段一检查清单
- [ ] 所有通知类型都能正常发送
- [ ] 通知内容准确无误
- [ ] 系统性能无明显下降
- [ ] 无新增的错误日志

#### 阶段二检查清单
- [ ] 旧代码已完全移除
- [ ] 配置文件已更新
- [ ] 代码质量检查通过
- [ ] 文档已更新

#### 阶段三检查清单
- [ ] 监控指标正常收集
- [ ] 告警规则已配置并测试
- [ ] 告警通知渠道正常
- [ ] 监控面板已创建

#### 阶段四检查清单
- [ ] 新功能正常工作
- [ ] 向后兼容性保持
- [ ] 性能测试通过
- [ ] 用户反馈良好

## 📝 执行建议

### 优先级排序
1. **高优先级**：阶段一的功能验证（必须完成）
2. **中优先级**：阶段二的代码清理和阶段三的监控
3. **低优先级**：阶段四和五的功能扩展

### 资源分配
- **开发时间**：每个阶段预留足够的测试时间
- **测试环境**：确保有完整的测试环境
- **监控工具**：准备好监控和告警工具

### 风险控制
- **灰度发布**：新功能先在小范围测试
- **回滚方案**：准备快速回滚方案
- **监控告警**：及时发现和处理问题

## 📞 联系和支持

如果在执行过程中遇到问题，可以参考：
- 设计文档：`docs/unified_notification_service_design.md`
- 迁移指南：`docs/notification_service_migration_guide.md`
- 使用示例：`examples/notification_service_usage.go`
- 单元测试：`internal/service/notification_test.go`

---

**注意**：本计划应根据实际情况调整时间安排和优先级，确保每个阶段的质量和稳定性。
