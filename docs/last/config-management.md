# 增强配置管理系统

本文档介绍如何使用增强的配置管理系统，该系统支持多配置文件、分层配置和优先级管理。

## 功能特性

1. **多配置文件支持** - 可以同时使用多个配置文件
2. **分层配置** - 配置文件按优先级分层，高优先级覆盖低优先级
3. **Consul 集成** - 支持从 Consul 读取配置（最高优先级）
4. **回退机制** - 如果高优先级配置不可用，自动回退到低优先级配置
5. **动态加载** - 可以在运行时添加新的配置文件

## 配置优先级

配置读取的优先级顺序（从高到低）：

1. **Consul 配置** - 动态配置，最高优先级
2. **分层配置文件** - 按添加时指定的优先级排序
3. **默认 GoFrame 配置** - `manifest/config/config.yaml`

## 使用方法

### 1. 基本用法

```go
import "telegram-bot-api/internal/config"

ctx := context.Background()

// 从分层配置读取
value, err := config.GetFromLayers(ctx, "database.default.debug", false)
if err != nil {
    log.Printf("读取配置失败: %v", err)
}

// 必须获取配置（失败时 panic）
value := config.MustGetFromLayers(ctx, "app.devMode", true)
```

### 2. 添加配置文件

```go
// 添加开发环境配置（优先级 2）
err := config.AddConfigFile(ctx, "dev", "manifest/config/config.dev.yaml", 2)
if err != nil {
    log.Printf("添加开发配置失败: %v", err)
}

// 添加生产环境配置（优先级 3，更高优先级）
err = config.AddConfigFile(ctx, "prod", "manifest/config/config.prod.yaml", 3)
if err != nil {
    log.Printf("添加生产配置失败: %v", err)
}
```

### 3. 从特定文件读取

```go
// 直接从特定文件读取配置
value, err := config.GetFromFile(ctx, "manifest/config/config.dev.yaml", "logger.level", "info")
if err != nil {
    log.Printf("从文件读取配置失败: %v", err)
}
```

### 4. 加载配置文件

```go
// 加载配置文件并获取配置实例
cfg, err := config.LoadConfigFile(ctx, "manifest/config/config.dev.yaml")
if err != nil {
    log.Printf("加载配置文件失败: %v", err)
    return
}

// 使用配置实例读取值
value, err := cfg.Get(ctx, "database.default.debug")
```

### 5. 扫描配置文件

```go
// 列出目录中的所有配置文件
configFiles, err := config.ListConfigFiles("manifest/config")
if err != nil {
    log.Printf("扫描配置文件失败: %v", err)
    return
}

for _, file := range configFiles {
    fmt.Printf("找到配置文件: %s\n", file)
}
```

## 配置文件示例

### 主配置文件 (config.yaml)
```yaml
app:
  devMode: true
database:
  default:
    debug: false
logger:
  level: "info"
```

### 开发环境配置 (config.dev.yaml)
```yaml
# 只覆盖需要修改的配置
database:
  default:
    debug: true  # 开发环境开启调试
logger:
  level: "debug"  # 开发环境使用调试级别
```

### 生产环境配置 (config.prod.yaml)
```yaml
app:
  devMode: false  # 生产环境关闭开发模式
logger:
  level: "warn"   # 生产环境只记录警告
  stdout: false   # 不输出到控制台
```

## 环境变量支持

可以通过环境变量控制使用哪个配置文件：

```bash
# 设置环境变量
export CONFIG_ENV=dev

# 在代码中根据环境变量加载配置
configEnv := os.Getenv("CONFIG_ENV")
if configEnv != "" {
    configFile := fmt.Sprintf("manifest/config/config.%s.yaml", configEnv)
    err := config.AddConfigFile(ctx, configEnv, configFile, 10)
    if err != nil {
        log.Printf("加载环境配置失败: %v", err)
    }
}
```

## 最佳实践

1. **保持主配置文件完整** - 主配置文件应包含所有必要的配置项
2. **环境配置只覆盖差异** - 环境特定的配置文件只包含需要覆盖的配置
3. **使用合理的优先级** - 为不同环境的配置文件设置合理的优先级
4. **敏感信息使用 Consul** - 密码、密钥等敏感信息应存储在 Consul 中
5. **配置验证** - 在应用启动时验证关键配置项是否存在

## 测试

运行测试脚本来验证配置管理功能：

```bash
go run scripts/test_layered_config.go
```

这个脚本会：
- 扫描所有配置文件
- 测试从不同源读取配置
- 验证配置优先级
- 测试 Consul 集成（如果可用）

## 故障排除

### 配置文件不存在
```
错误: configuration file does not exist: manifest/config/config.dev.yaml
解决: 确保配置文件路径正确，或创建缺失的配置文件
```

### 配置适配器错误
```
错误: configuration adapter is not a file adapter
解决: 确保使用的是文件配置适配器，而不是其他类型的适配器
```

### Consul 连接失败
```
错误: Consul 初始化失败
解决: 检查 Consul 服务器是否运行，配置是否正确
```
