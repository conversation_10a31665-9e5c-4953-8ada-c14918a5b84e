# Swap Error Analysis Report

## Summary
Analysis of fmt.Errorf usage in swap-related files to identify which errors should be converted to structured SwapError for better user experience and i18n support.

## Error Categories

### 1. User-Facing Errors (Should be converted to SwapError)

#### swap_service.go
- **Line 68**: `fmt.<PERSON><PERSON><PERSON>("swap service unavailable: %s", message)` → Should use `ErrCodeServiceUnavailable`
- **Line 113**: `fmt.<PERSON><PERSON><PERSON>("product not found: %s", symbol)` → Need new error code `ErrCodeProductNotFound`
- **Line 143**: `fmt.E<PERSON>rf("product not found for token pair: %d/%d", baseTokenID, quoteTokenID)` → Need new error code `ErrCodeTradingPairNotFound`
- **Line 157**: `fmt.Errorf("token not found for ID: %d", tokenID)` → Need new error code `ErrCodeTokenNotFound`
- **Line 170**: `fmt.<PERSON><PERSON><PERSON>("token not found for symbol: %s", symbol)` → Need new error code `ErrCodeTokenNotFound`
- **Line 184**: `fmt.Errorf("swap service unavailable: %s", message)` → Should use `ErrCodeServiceUnavailable`
- **Line 191**: `fmt.Errorf("invalid amount")` → Should use `ErrCodeInvalidAmount`
- **Line 228**: `fmt.Errorf("no trading pair available for this token combination")` → Need new error code `ErrCodeTradingPairNotFound`
- **Line 342**: `fmt.Errorf("invalid trade configuration")` → Need new error code `ErrCodeInvalidTradeConfiguration`
- **Line 358**: `fmt.Errorf("invalid trade configuration")` → Need new error code `ErrCodeInvalidTradeConfiguration`
- **Line 568**: `fmt.Errorf("quote not found or expired")` → Should use `ErrCodeQuoteExpired` or `ErrCodeQuoteNotFound`
- **Line 601**: `fmt.Errorf("quote has expired")` → Should use `ErrCodeQuoteExpired`
- **Line 625**: `fmt.Errorf("quote has expired")` → Should use `ErrCodeQuoteExpired`
- **Line 630**: `fmt.Errorf("quote does not belong to user")` → Need new error code `ErrCodeUnauthorizedQuote`
- **Line 1088**: `fmt.Errorf("price slippage too high: %s%%", ...)` → Should use `ErrCodePriceSlippage`
- **Line 1207**: `fmt.Errorf("insufficient balance for swap")` → Should use `ErrCodeInsufficientBalance`
- **Line 1379**: `fmt.Errorf("order not found")` → Should use `ErrCodeOrderNotFound`

#### swap_config.go
- **Line 155**: `fmt.Errorf("amount below minimum: $%s", ...)` → Should use `ErrCodeAmountBelowMinimum`
- **Line 160**: `fmt.Errorf("amount exceeds maximum: $%s", ...)` → Should use `ErrCodeAmountExceedsMaximum`
- **Line 183**: `fmt.Errorf("too many orders, please wait a moment")` → Should use `ErrCodeRateLimitExceeded`

### 2. Technical/System Errors (Keep as fmt.Errorf for logging)

#### swap_service.go
- **Line 64**: `fmt.Errorf("failed to check maintenance mode: %w", err)` - Internal error
- **Line 82**: `fmt.Errorf("failed to get active products: %w", err)` - Database error
- **Line 109**: `fmt.Errorf("failed to get product by symbol: %w", err)` - Database error
- **Line 124**: `fmt.Errorf("failed to get base token symbol: %w", err)` - Database error
- **Line 275**: `fmt.Errorf("failed to get fiat price: %w", err)` - External API error
- **Line 297**: `fmt.Errorf("failed to get price: %w", err)` - External API error
- **Line 395**: `fmt.Errorf("product limit check failed: %w", err)` - Internal check error
- **Line 423**: `fmt.Errorf("failed to calculate fee: %w", err)` - Calculation error
- **Line 529**: `fmt.Errorf("failed to marshal quote: %w", err)` - Serialization error
- **Line 540**: `fmt.Errorf("failed to cache quote: %w", err)` - Redis error
- **Line 563**: `fmt.Errorf("failed to get quote from cache: %w", err)` - Redis error
- **Line 576**: `fmt.Errorf("failed to parse quote: %w", err)` - Deserialization error
- **Line 651**: `fmt.Errorf("failed to get product: %w", err)` - Database error
- **Line 730**: `fmt.Errorf("failed to create order: %w", err)` - Database error
- **Line 1100**: `fmt.Errorf("failed to update order status: %w", err)` - Database error
- **Line 1146**: `fmt.Errorf("failed to process swap debit: %w", err)` - Wallet operation error
- **Line 1231**: `fmt.Errorf("failed to process swap credit: %w", err)` - Wallet operation error
- **Line 1239**: `fmt.Errorf("critical error: failed to credit user wallet after successful debit")` - Critical system error

### 3. Already Converted to SwapError
- **Lines 233-236**: Buying not active → Using `ErrCodeTradingPairNotActive`
- **Lines 238-243**: Buying not allowed → Using `ErrCodeBuyingNotAllowed`
- **Lines 245-250**: Selling not allowed → Using `ErrCodeSellingNotAllowed`
- **Lines 367-376**: Amount below minimum → Using `ErrCodeAmountBelowMinimum`
- **Lines 378-391**: Amount exceeds maximum → Using `ErrCodeAmountExceedsMaximum`
- **Lines 895-902**: Maintenance mode check → Using `ErrCodeServiceUnavailable` and `ErrCodeInternalError`
- **Lines 906-909**: Database transaction → Using `ErrCodeInternalError`
- **Lines 926-928**: Order not found → Using `ErrCodeOrderNotFound`
- **Lines 932-935**: Invalid order status → Using `ErrCodeInvalidOrderStatus`
- **Lines 875-877**: Failed to execute order → Using `ErrCodeInternalError`

## New Error Codes Needed

The following new error codes should be added to swap_errors.go:

1. `ErrCodeProductNotFound` - "PRODUCT_NOT_FOUND"
2. `ErrCodeTradingPairNotFound` - "TRADING_PAIR_NOT_FOUND"
3. `ErrCodeTokenNotFound` - "TOKEN_NOT_FOUND"
4. `ErrCodeInvalidTradeConfiguration` - "INVALID_TRADE_CONFIGURATION"
5. `ErrCodeUnauthorizedQuote` - "UNAUTHORIZED_QUOTE"

## Priority Conversions

### High Priority (Direct user impact)
1. Insufficient balance errors
2. Quote expired errors
3. Amount validation errors (min/max)
4. Rate limit errors
5. Trading pair not found errors

### Medium Priority (Configuration/availability)
1. Service unavailable errors
2. Product not found errors
3. Token not found errors
4. Unauthorized quote errors

### Low Priority (Edge cases)
1. Invalid trade configuration errors
2. Price slippage errors (already have good error messages)

## Implementation Recommendations

1. **Add new error codes** to `model/swap_errors.go`
2. **Update i18n files** with user-friendly messages for each error code
3. **Convert high-priority errors first** as they have the most user impact
4. **Keep technical errors as fmt.Errorf** for internal logging and debugging
5. **Ensure error details** include relevant context (amounts, symbols, limits)

## Example Conversions

```go
// Before:
return nil, fmt.Errorf("insufficient balance for swap")

// After:
return nil, model.NewSwapError(model.ErrCodeInsufficientBalance, "insufficient balance for swap").
    WithDetail("required_amount", debitAmount.String()).
    WithDetail("token", debitTokenSymbol)
```

```go
// Before:
return nil, fmt.Errorf("quote not found or expired")

// After:
return nil, model.NewSwapError(model.ErrCodeQuoteExpired, "quote not found or expired").
    WithDetail("quote_id", quoteID)
```