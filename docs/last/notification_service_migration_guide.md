# 统一通知服务迁移指南

## 概述

本指南说明如何将现有的分散通知逻辑迁移到新的统一通知服务。新服务提供了简洁的API，隐藏了Kafka和消息构造的复杂性。

## 迁移前后对比

### 迁移前的问题
1. **代码重复**: 每个业务模块都需要手动构造复杂的Kafka消息
2. **维护困难**: 通知逻辑分散在各个文件中
3. **错误处理不一致**: 每个地方都有不同的错误处理逻辑
4. **测试困难**: 需要模拟Kafka环境

### 迁移后的优势
1. **代码简洁**: 一行代码发送通知
2. **统一管理**: 所有通知逻辑集中在通知服务中
3. **类型安全**: 强类型的请求参数
4. **易于测试**: 可以轻松mock通知服务

## 具体迁移步骤

### 1. 支付通知迁移

**迁移文件**: `internal/logic/payment_request/execute_payment.go`

**原代码** (第78-179行):
```go
// 4. Send Kafka notifications using the new UnifiedNotificationMessage structure
unifiedNotificationTopic := g.Cfg().MustGet(ctx, "kafka.topics.unifiedNotifications", "unified_notifications").String()
currentTime := gtime.Now().Unix()
traceID := fmt.Sprintf("paymentreq_%d_%d", request.RequestId, currentTime)

// 构造付款方通知
payerPayload := model.PaymentSentPayload{
    RequesterUsername: requesterUsername,
    Amount:            request.Amount.String(),
    Symbol:            tokenSymbol,
}
payerPayloadBytes, _ := json.Marshal(payerPayload)

payerUnifiedMsg := model.UnifiedNotificationMessage{
    NotificationKey: consts.NotificationKeyPaymentSent,
    UserID:          request.PayerUserId,
    UserLanguage:    "",
    Timestamp:       currentTime,
    TraceID:         traceID,
    Payload:         json.RawMessage(payerPayloadBytes),
}

msgBytesPayer, marshalErr := json.Marshal(payerUnifiedMsg)
if marshalErr != nil {
    g.Log().Errorf(ctx, "Failed to marshal UnifiedNotificationMessage for payer: %v", marshalErr)
} else {
    kafkaErr := service.Kafka().SendMessage(ctx, unifiedNotificationTopic, fmt.Sprintf("payment_%d_payer", request.RequestId), msgBytesPayer)
    if kafkaErr != nil {
        g.Log().Errorf(ctx, "Failed to send Kafka PaymentSent notification for payer: %v", kafkaErr)
    }
}

// 类似的代码还要为收款方重复一遍...
```

**新代码**:
```go
// 4. Send payment notifications
notificationReq := &service.PaymentNotificationRequest{
    PayerUserID:       request.PayerUserId,
    RequesterUserID:   request.RequesterUserId,
    Amount:            request.Amount.String(),
    Symbol:            tokenSymbol,
    RequesterUsername: requesterUsername,
    PayerUsername:     payerUsername,
    TraceID:           fmt.Sprintf("paymentreq_%d_%d", request.RequestId, gtime.Now().Unix()),
}

err := service.Notification().SendPaymentNotification(ctx, notificationReq)
if err != nil {
    g.Log().Errorf(ctx, "Failed to send payment notification (RequestID: %d): %v", request.RequestId, err)
}
```

### 2. 红包通知迁移

**迁移场景**: 红包过期和被领完的通知

**新代码示例**:
```go
// 红包过期通知
expiredReq := &service.RedPacketNotificationRequest{
    NotificationType: "expired",
    CreatorUserID:    redPacket.CreatorUserId,
    RedPacketUUID:    redPacket.Uuid,
    Amount:           redPacket.RemainingAmount.String(),
    Symbol:           redPacket.Symbol,
}
err := service.Notification().SendRedPacketNotification(ctx, expiredReq)

// 红包被领完通知
claimedReq := &service.RedPacketNotificationRequest{
    NotificationType: "claimed_fully",
    CreatorUserID:    redPacket.CreatorUserId,
    RedPacketUUID:    redPacket.Uuid,
    Amount:           redPacket.TotalAmount.String(),
    Symbol:           redPacket.Symbol,
}
err := service.Notification().SendRedPacketNotification(ctx, claimedReq)
```

### 3. 充值/提现通知迁移

**充值成功通知**:
```go
depositReq := &service.DepositNotificationRequest{
    UserID:  userID,
    Amount:  amount.String(),
    Symbol:  tokenSymbol,
    TxHash:  transactionHash,
    Wallet:  walletAddress,
}
err := service.Notification().SendDepositNotification(ctx, depositReq)
```

**提现通知**:
```go
// 提现成功
successReq := &service.WithdrawNotificationRequest{
    NotificationType: "success",
    UserID:           userID,
    Amount:           amount.String(),
    Symbol:           tokenSymbol,
    Address:          withdrawAddress,
    TxHash:           transactionHash,
}
err := service.Notification().SendWithdrawNotification(ctx, successReq)

// 提现失败
failedReq := &service.WithdrawNotificationRequest{
    NotificationType: "failed",
    UserID:           userID,
    Amount:           amount.String(),
    Symbol:           tokenSymbol,
    Address:          withdrawAddress,
    FailureReason:    "余额不足",
}
err := service.Notification().SendWithdrawNotification(ctx, failedReq)
```

## 迁移检查清单

### 需要迁移的文件
- [ ] `internal/logic/payment_request/execute_payment.go` - 支付通知
- [ ] `internal/logic/red_packet/` - 红包相关通知
- [ ] `internal/logic/withdraw/` - 提现通知
- [ ] `internal/logic/deposit/` - 充值通知 (如果有)
- [ ] 其他直接调用Kafka发送通知的业务代码

### 迁移步骤
1. [ ] 识别现有的通知发送代码
2. [ ] 确定通知类型和所需参数
3. [ ] 使用对应的通知服务方法替换原代码
4. [ ] 更新错误处理逻辑
5. [ ] 测试新的通知功能
6. [ ] 删除不再需要的旧代码

### 测试验证
1. [ ] 支付完成后能正确发送通知给付款方和收款方
2. [ ] 红包过期和被领完能正确通知创建者
3. [ ] 充值成功能正确通知用户
4. [ ] 提现成功/失败能正确通知用户
5. [ ] 所有通知的TraceID能正确生成和传递
6. [ ] 错误情况下的日志记录正确

## 注意事项

1. **向后兼容**: 新的通知服务与现有的`unified_notifier`消费者完全兼容
2. **TraceID**: 如果不提供TraceID，系统会自动生成
3. **错误处理**: 通知发送失败不应该影响主业务流程
4. **性能**: 新服务使用相同的Kafka基础设施，性能无影响
5. **监控**: 保持现有的日志记录和监控

## 未来扩展

新的通知服务设计为易于扩展:
1. 添加新的通知类型只需要扩展接口和实现
2. 可以轻松添加新的通知渠道 (如邮件、短信)
3. 支持通知模板的动态配置
4. 可以添加通知发送的重试机制

## 技术支持

如果在迁移过程中遇到问题，请参考:
1. `examples/notification_service_usage.go` - 使用示例
2. `internal/service/notification.go` - 服务实现
3. 现有的`unified_notifier`消费者代码
