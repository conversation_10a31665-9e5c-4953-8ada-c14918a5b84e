# Database Connection Optimization

This document describes the changes made to fix the "Error 1040: Too many connections" issue in the Telegram Bot API.

## Problem Analysis

The application was experiencing database connection exhaustion due to:

1. **Multiple concurrent tasks** running simultaneously (every 10-60 seconds)
2. **Short connection lifetime** (30 seconds) causing frequent connection recycling
3. **No connection coordination** between tasks
4. **Lack of monitoring** for connection usage

## Solutions Implemented

### 1. Database Connection Pool Optimization

**Changes made to `manifest/config/config.yaml` and `config_variables.json`:**

```yaml
database:
  default:
    maxIdle: 20          # Increased from 10
    maxOpen: 200         # Increased from 100  
    maxLifetime: "300s"  # Increased from "30s" to 5 minutes
```

**Benefits:**
- More idle connections available for reuse
- Higher maximum connection limit
- Longer connection lifetime reduces recycling overhead

### 2. Database Connection Monitoring

**New file: `internal/service/database_monitor.go`**

Features:
- Real-time connection pool statistics
- Health checks every 30 seconds
- Connection usage alerts when > 80%
- Error tracking and recovery monitoring

**Usage:**
```go
monitor := service.GetDatabaseMonitor()
stats := monitor.GetConnectionStats()
health := monitor.GetHealthStatus()
```

### 3. Task Coordination System

**New file: `internal/task/coordinator.go`**

Features:
- Prevents overlapping task executions
- Limits concurrent tasks (max 3 simultaneously)
- Task execution statistics and monitoring
- Automatic retry and error handling

**Usage:**
```go
ExecuteWithCoordination(ctx, "TaskName", func(taskCtx context.Context) error {
    return taskFunction(taskCtx)
})
```

### 4. Improved Task Scheduling

**Changes to `internal/task/init.go`:**

- **Reduced frequency**: Changed from 10s to 30s for notification tasks
- **Random delays**: Added jitter (0-15s) to prevent simultaneous execution
- **Database monitoring**: Wrapped all database operations with monitoring
- **Task coordination**: All tasks now use the coordination system

**New schedule:**
- Red packet expiry: Every 1 minute (with 0-10s delay)
- Transfer expiry: Every 1 minute (with 5-15s delay)  
- Recharge notifications: Every 30 seconds (with 0-5s delay)
- Withdraw notifications: Every 30 seconds (with 10-15s delay)

### 5. Query Optimization

**Added to task functions:**
- 30-second query timeouts to prevent connection hanging
- Better error handling and logging
- Connection usage debugging

## Monitoring and Debugging

### 1. Connection Statistics Logging

The system now logs connection statistics every 5 minutes:

```
[DatabaseMonitor] Current stats: Open=15, Idle=8, InUse=7, Max=200
[TaskCoordinator] Running tasks: [HandleCompletedRecharges]
[TaskCoordinator] Task HandleCompletedRecharges: runs=120, success=98.3%, avg_duration=1.2s
```

### 2. Health Check Endpoint

Monitor database health programmatically:

```go
monitor := service.GetDatabaseMonitor()
if !monitor.IsHealthy() {
    // Handle unhealthy database
}
```

### 3. Test Script

Run the connection test script:

```bash
cd scripts
go run test_db_connections.go
```

## Expected Results

After implementing these changes:

1. **No more "Too many connections" errors**
2. **Better resource utilization** with connection pooling
3. **Improved task coordination** preventing overlaps
4. **Real-time monitoring** of database health
5. **Reduced database load** with optimized scheduling

## Monitoring Commands

### Check Current Connection Usage

```sql
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';
SHOW VARIABLES LIKE 'max_connections';
```

### View Process List

```sql
SHOW PROCESSLIST;
```

### Monitor Application Logs

```bash
# Watch for connection-related logs
tail -f logs/*.log | grep -E "(DatabaseMonitor|TaskCoordinator|connection)"

# Watch for errors
tail -f logs/*.log | grep -E "(ERROR|ERRO)"
```

## Troubleshooting

### If Connection Issues Persist

1. **Check MySQL configuration:**
   ```sql
   SHOW VARIABLES LIKE 'max_connections';
   SET GLOBAL max_connections = 500;  -- Increase if needed
   ```

2. **Monitor connection usage:**
   ```bash
   # Run the test script
   go run scripts/test_db_connections.go
   ```

3. **Check task coordination:**
   - Look for "Cannot start task" messages in logs
   - Verify tasks are not running simultaneously

4. **Adjust configuration:**
   - Increase `maxOpen` if needed
   - Reduce task frequency if still overloaded
   - Add more random delays between tasks

### Performance Tuning

- **For high load**: Increase `maxOpen` to 300-500
- **For low resources**: Decrease `maxIdle` to 10-15
- **For stability**: Increase `maxLifetime` to 600s (10 minutes)

## Configuration Examples

### High Traffic Environment
```yaml
database:
  default:
    maxIdle: 50
    maxOpen: 500
    maxLifetime: "600s"
```

### Resource Constrained Environment
```yaml
database:
  default:
    maxIdle: 10
    maxOpen: 100
    maxLifetime: "300s"
```
