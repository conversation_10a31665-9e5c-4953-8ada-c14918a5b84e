# 防抖动机制使用说明

## 概述

防抖动（Debounce）服务用于防止用户在短时间内重复执行相同操作。系统会在指定时间窗口内（默认0.5秒）拦截重复的操作请求。

## 配置

在 `manifest/config/config.yaml` 中配置：

```yaml
debounce:
  enabled: true         # 是否启用防抖动
  windowMs: 500        # 防抖动窗口（毫秒）
  keyPrefix: "debounce:" # Redis键前缀
```

## 使用方法

### 1. 基本使用

```go
// 检查操作是否重复
isDuplicate, err := service.Debounce().Check(
    ctx, 
    userID,         // 用户ID
    "operation",    // 操作类型
    "data"          // 操作数据
)

if isDuplicate {
    // 操作被防抖，直接返回
    return
}
// 继续执行操作
```

### 2. 自定义时间窗口

```go
// 使用2秒的防抖窗口
isDuplicate, err := service.Debounce().CheckWithWindow(
    ctx, 
    userID, 
    "withdraw", 
    "USDT:100", 
    2*time.Second
)
```

### 3. 内置的便捷方法

```go
// 检查消息防抖
isDuplicate, err := service.Debounce().CheckMessage(ctx, userID, messageText)

// 检查回调防抖
isDuplicate, err := service.Debounce().CheckCallback(ctx, userID, callbackData)

// 检查内联查询防抖
isDuplicate, err := service.Debounce().CheckInlineQuery(ctx, userID, query)
```

## 实现原理

1. **键生成**：`debounce:{userID}:{operationType}:{operationData}`
2. **Redis操作**：使用 `SET NX EX` 实现原子操作
3. **数据哈希**：对于长数据（>32字符），使用MD5哈希
4. **故障降级**：Redis错误时不阻塞操作

## 使用场景

- 防止快速重复点击按钮
- 防止重复提交表单
- 防止频繁调用API
- 防止重复发送消息
- 防止并发执行敏感操作（如提现、转账）

## 注意事项

1. 防抖动是基于用户级别的，不同用户之间不会相互影响
2. Redis连接失败时会降级，允许操作继续（fail-open策略）
3. 对于关键操作，建议配合其他锁机制使用