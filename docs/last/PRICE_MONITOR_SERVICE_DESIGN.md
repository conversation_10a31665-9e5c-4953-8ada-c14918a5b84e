# 价格监控服务设计文档

## 项目概述

本文档详细描述了独立的价格监控和更新服务的完整架构设计，该服务通过WebSocket订阅币安实时价格数据，存储到Redis中，并为闪兑系统提供实时、可靠的价格数据服务。

## 业务需求

### 核心功能
- **实时价格订阅**：通过Binance WebSocket获取实时价格数据
- **Redis存储**：将价格数据存储到Redis中供其他服务使用
- **数据新鲜度检测**：监控价格数据的时效性，防止使用过期数据
- **多重容错机制**：WebSocket断线重连、多价格源备份
- **监控**：价格数据监控、连接状态监控

### 技术要求
- 独立部署的服务进程
- 支持水平扩展（多实例部署）
- 低延迟（<100ms）
- 高可用性（99.9%+）
- 数据一致性保证

## 系统架构

### 1. 服务架构图

```mermaid
graph TB
    subgraph "Price Monitor Service"
        A[WebSocket Manager] --> B[Price Processor]
        B --> C[Redis Writer]
        B --> D[Health Monitor]
        E[Config Manager] --> A
        E --> B
        F[Metrics Collector] --> G[Prometheus Exporter]
    end
    
    subgraph "External Services"
        H[Binance WebSocket] --> A
    end
    
    subgraph "Storage"
        C --> K[Redis Cluster]
    end
    
    subgraph "Monitoring"
        G --> L[Prometheus]
        L --> M[Grafana]
    end
    
    subgraph "Client Services"
        O[Swap Service] --> K
        P[Trading Bot] --> K
        Q[Admin Panel] --> K
    end
```

### 2. 核心组件设计

#### 2.1 WebSocket管理器
```go
// WebSocket连接管理器
type WebSocketManager struct {
    connections map[string]*WebSocketConnection
    reconnectCh chan string
    stopCh      chan struct{}
    config      *Config
    logger      *logrus.Logger
    metrics     *Metrics
}

type WebSocketConnection struct {
    providerName string
    conn         *websocket.Conn
    symbols      []string
    isConnected  atomic.Bool
    lastPong     atomic.Value // time.Time
    reconnectDelay time.Duration
}

// WebSocket事件处理器
type PriceEventHandler interface {
    OnPriceUpdate(event *PriceUpdateEvent) error
    OnConnectionEstablished(provider string) error
    OnConnectionLost(provider string) error
    OnError(provider string, err error) error
}
```

#### 2.2 价格处理器
```go
// 价格数据处理器
type PriceProcessor struct {
    redisWriter  *RedisWriter
    validator    *PriceValidator
    aggregator   *PriceAggregator
    eventBus     *EventBus
    metrics      *Metrics
}

// 价格验证器
type PriceValidator struct {
    maxPriceChange   float64 // 最大价格变动百分比
    minUpdateInterval time.Duration // 最小更新间隔
    priceHistory     map[string]*PriceHistory
}

// 价格聚合器
type PriceAggregator struct {
    weights map[string]float64 // 不同交易所的权重
    sources []string // 价格源列表
}
```

#### 2.3 Redis写入器
```go
// Redis数据写入器
type RedisWriter struct {
    client      *gredis.Redis  // 使用项目现有的gredis客户端
    keyPrefix   string
    ttl         time.Duration
    batchSize   int
    batchBuffer chan *PriceData
    flushTicker *time.Ticker
}

// 价格数据结构
type PriceData struct {
    Symbol          string          `json:"symbol"`           // 交易对符号
    Price           decimal.Decimal `json:"price"`            // 价格
    Volume24h       decimal.Decimal `json:"volume_24h"`       // 24小时交易量
    Change24h       decimal.Decimal `json:"change_24h"`       // 24小时涨跌幅
    High24h         decimal.Decimal `json:"high_24h"`         // 24小时最高价
    Low24h          decimal.Decimal `json:"low_24h"`          // 24小时最低价
    Provider        string          `json:"provider"`         // 价格提供商
    Timestamp       time.Time       `json:"timestamp"`        // 更新时间戳
    LastUpdated     int64           `json:"last_updated"`     // Unix时间戳
}
```

### 3. Redis数据结构设计

#### 3.1 价格数据存储
```
// 主要价格数据 (Hash结构)
Key: "price:live:{symbol}"
Fields:
  - price: "2456.78"
  - volume_24h: "125643.89"
  - change_24h: "0.0245"
  - high_24h: "2489.12"
  - low_24h: "2398.56"
  - provider: "binance"
  - timestamp: "**********789"
  - last_updated: "**********"

// 价格历史 (Sorted Set结构)
Key: "price:history:{symbol}"
Score: timestamp
Value: JSON price data

// 服务健康状态
Key: "price:health:{provider}"
Fields:
  - status: "connected"
  - last_ping: "**********789"
  - message_count: "12345"
  - error_count: "2"
  - last_error: "connection timeout"

// 价格源状态
Key: "price:sources"
Fields:
  - binance: "active"
```

#### 3.2 数据TTL策略
```go
const (
    PriceLiveTTL     = 5 * time.Minute   // 实时价格TTL
    PriceHistoryTTL  = 7 * 24 * time.Hour // 历史价格TTL
    HealthStatusTTL  = 1 * time.Minute   // 健康状态TTL
    MaxHistoryPoints = 1000              // 最大历史数据点
)
```

### 4. 配置管理

#### 4.1 服务配置
```yaml
# config.yaml
service:
  name: "price-monitor-service"
  port: 8080
  log_level: "info"
  
websocket:
  providers:
    binance:
      url: "wss://stream.binance.com:9443/ws/ethusdt@ticker/btcusdt@ticker"
      reconnect_delay: "5s"
      ping_interval: "30s"
      pong_timeout: "10s"
      enabled: true
      
redis:
  # 使用项目现有的Redis配置
  # 复用 internal/service/redis.go 中的Redis客户端
  config_name: "default"  # 使用默认Redis配置
  pool_size: 10
  max_retries: 3
  
symbols:
  - "ETHUSDT"
  - "BTCUSDT"
  - "BNBUSDT"
  
validation:
  max_price_change: 0.1  # 10%
  min_update_interval: "1s"
  stale_data_threshold: "30s"
  
monitoring:
  prometheus_port: 9090
  health_check_interval: "10s"
```

#### 4.2 动态配置结构
```go
type Config struct {
    Service    ServiceConfig    `yaml:"service"`
    WebSocket  WebSocketConfig  `yaml:"websocket"`
    Redis      RedisConfig      `yaml:"redis"`
    Symbols    []string         `yaml:"symbols"`
    Validation ValidationConfig `yaml:"validation"`
    Monitoring MonitoringConfig `yaml:"monitoring"`
}

type ServiceConfig struct {
    Name     string `yaml:"name"`
    Port     int    `yaml:"port"`
    LogLevel string `yaml:"log_level"`
}

type WebSocketConfig struct {
    Providers map[string]ProviderConfig `yaml:"providers"`
}

type ProviderConfig struct {
    URL             string        `yaml:"url"`
    ReconnectDelay  time.Duration `yaml:"reconnect_delay"`
    PingInterval    time.Duration `yaml:"ping_interval"`
    PongTimeout     time.Duration `yaml:"pong_timeout"`
    Enabled         bool          `yaml:"enabled"`
}
```

### 5. WebSocket消息处理

#### 5.1 Binance消息格式
```go
// Binance Ticker消息结构
type BinanceTickerEvent struct {
    EventType   string `json:"e"`  // "24hrTicker"
    EventTime   int64  `json:"E"`  // Event time
    Symbol      string `json:"s"`  // Symbol
    PriceChange string `json:"p"`  // Price change
    PriceChangePercent string `json:"P"` // Price change percent
    WeightedAvgPrice   string `json:"w"` // Weighted average price
    PrevClosePrice     string `json:"x"` // Previous day's close price
    LastPrice          string `json:"c"` // Last price
    LastQty            string `json:"Q"` // Last quantity
    BidPrice           string `json:"b"` // Best bid price
    BidQty             string `json:"B"` // Best bid quantity
    AskPrice           string `json:"a"` // Best ask price
    AskQty             string `json:"A"` // Best ask quantity
    OpenPrice          string `json:"o"` // Open price
    HighPrice          string `json:"h"` // High price
    LowPrice           string `json:"l"` // Low price
    Volume             string `json:"v"` // Total traded base asset volume
    QuoteVolume        string `json:"q"` // Total traded quote asset volume
    OpenTime           int64  `json:"O"` // Statistics open time
    CloseTime          int64  `json:"C"` // Statistics close time
    FirstID            int64  `json:"F"` // First trade ID
    LastID             int64  `json:"L"` // Last trade Id
    Count              int64  `json:"n"` // Total number of trades
}

// 消息处理器
func (p *PriceProcessor) ProcessBinanceMessage(data []byte) error {
    var event BinanceTickerEvent
    if err := json.Unmarshal(data, &event); err != nil {
        return fmt.Errorf("failed to unmarshal binance message: %w", err)
    }
    
    priceData, err := p.convertBinanceEventToPriceData(&event)
    if err != nil {
        return fmt.Errorf("failed to convert binance event: %w", err)
    }
    
    if err := p.validator.Validate(priceData); err != nil {
        return fmt.Errorf("price validation failed: %w", err)
    }
    
    return p.redisWriter.Write(priceData)
}
```

### 6. 健康监控系统

#### 6.1 健康检查器
```go
type HealthMonitor struct {
    checks   map[string]HealthCheck
    interval time.Duration
    metrics  *Metrics
}

type HealthCheck interface {
    Name() string
    Check(ctx context.Context) HealthStatus
}

type HealthStatus struct {
    Status    string                 `json:"status"`    // "healthy", "unhealthy", "degraded"
    Message   string                 `json:"message"`
    Timestamp time.Time             `json:"timestamp"`
    Metadata  map[string]interface{} `json:"metadata"`
}

// WebSocket连接健康检查
type WebSocketHealthCheck struct {
    manager *WebSocketManager
}

func (w *WebSocketHealthCheck) Check(ctx context.Context) HealthStatus {
    unhealthyProviders := w.manager.GetUnhealthyProviders()
    if len(unhealthyProviders) == 0 {
        return HealthStatus{
            Status:    "healthy",
            Message:   "All WebSocket connections are healthy",
            Timestamp: time.Now(),
        }
    }
    
    if len(unhealthyProviders) == len(w.manager.GetAllProviders()) {
        return HealthStatus{
            Status:    "unhealthy",
            Message:   "All WebSocket connections are down",
            Timestamp: time.Now(),
            Metadata:  map[string]interface{}{"unhealthy_providers": unhealthyProviders},
        }
    }
    
    return HealthStatus{
        Status:    "degraded",
        Message:   fmt.Sprintf("Some WebSocket connections are down: %v", unhealthyProviders),
        Timestamp: time.Now(),
        Metadata:  map[string]interface{}{"unhealthy_providers": unhealthyProviders},
    }
}
```


### 7. 指标收集

#### 7.1 Prometheus指标
```go
type Metrics struct {
    // WebSocket连接指标
    wsConnections    prometheus.GaugeVec
    wsReconnects     prometheus.CounterVec
    wsMessagesTotal  prometheus.CounterVec
    wsMessageLatency prometheus.HistogramVec
    
    // 价格数据指标
    priceUpdatesTotal prometheus.CounterVec
    priceAnomalies    prometheus.CounterVec
    priceLatency      prometheus.HistogramVec
    staleDataCount    prometheus.GaugeVec
    
    // Redis操作指标
    redisOpsTotal     prometheus.CounterVec
    redisLatency      prometheus.HistogramVec
    redisErrors       prometheus.CounterVec
    
    // 健康状态指标
    healthStatus      prometheus.GaugeVec
    uptimeTotal       prometheus.CounterVec
}

func NewMetrics() *Metrics {
    return &Metrics{
        wsConnections: prometheus.NewGaugeVec(
            prometheus.GaugeOpts{
                Name: "price_monitor_websocket_connections",
                Help: "Number of active WebSocket connections",
            },
            []string{"provider", "status"},
        ),
        priceUpdatesTotal: prometheus.NewCounterVec(
            prometheus.CounterOpts{
                Name: "price_monitor_price_updates_total",
                Help: "Total number of price updates processed",
            },
            []string{"symbol", "provider", "status"},
        ),
        // ... 其他指标定义
    }
}
```

### 8. 客户端SDK

#### 8.1 价格客户端接口
```go
// 价格服务客户端接口
type IPriceClient interface {
    // 获取实时价格 (自动检查数据新鲜度)
    GetRealTimePrice(ctx context.Context, symbol string) (*PriceData, error)
    
    // 批量获取价格 (自动检查数据新鲜度)
    GetMultiplePrices(ctx context.Context, symbols []string) (map[string]*PriceData, error)
    
    // 获取价格历史
    GetPriceHistory(ctx context.Context, symbol string, from, to time.Time) ([]*PriceData, error)
    
    // 订阅价格更新
    SubscribePriceUpdates(ctx context.Context, symbols []string) (<-chan *PriceUpdateEvent, error)
}

// 价格客户端实现
type PriceClient struct {
    redis  *gredis.Redis  // 使用项目现有的gredis客户端
    config *ClientConfig
    logger *logrus.Logger
}

type ClientConfig struct {
    RedisConfigName     string        `yaml:"redis_config_name"`     // 使用项目现有的Redis配置名称
    DefaultStaleTimeout time.Duration `yaml:"default_stale_timeout"` // 自动检查数据新鲜度的默认超时时间
    RetryAttempts       int           `yaml:"retry_attempts"`
    RetryDelay          time.Duration `yaml:"retry_delay"`
}
```

#### 8.2 使用示例
```go
// 在闪兑服务中使用价格客户端
func (s *SwapService) GetSwapQuote(ctx context.Context, req *SwapQuoteRequest) (*SwapQuote, error) {
    // 获取实时价格 (自动检查数据新鲜度)
    priceData, err := s.priceClient.GetRealTimePrice(ctx, req.FromSymbol+"USDT")
    if err != nil {
        return nil, fmt.Errorf("failed to get price: %w", err)
    }
    
    // 计算兑换金额
    exchangeRate := priceData.Price
    toAmount := req.FromAmount.Mul(exchangeRate)
    
    return &SwapQuote{
        FromAmount:   req.FromAmount,
        ToAmount:     toAmount,
        ExchangeRate: exchangeRate,
        PriceSource:  priceData.Provider,
        ExpiresAt:    time.Now().Add(30 * time.Second),
    }, nil
}
```

### 9. 启动入口和部署

#### 9.1 独立启动入口
价格监控服务需要有独立的启动入口，放置在 `cmd/price-monitor/` 目录下：

```
cmd/
├── price-monitor/
│   └── main.go        # 价格监控服务启动入口
├── task/
│   └── main.go        # 现有任务服务
└── kafka-test/
    └── main.go        # 现有Kafka测试
```

#### 9.1.1 启动入口实现
```go
// cmd/price-monitor/main.go
package main

import (
    "context"
    "flag"
    "fmt"
    "log"
    "os"
    "os/signal"
    "syscall"
    "time"

    "github.com/your-project/internal/service"
    "github.com/your-project/internal/config"
    "github.com/your-project/internal/price-monitor"
)

func main() {
    var configPath = flag.String("config", "config.yaml", "配置文件路径")
    flag.Parse()

    // 加载配置
    cfg, err := config.LoadPriceMonitorConfig(*configPath)
    if err != nil {
        log.Fatalf("Failed to load config: %v", err)
    }

    // 初始化服务
    priceMonitor, err := price_monitor.NewPriceMonitorService(cfg)
    if err != nil {
        log.Fatalf("Failed to create price monitor service: %v", err)
    }

    // 启动服务
    ctx, cancel := context.WithCancel(context.Background())
    defer cancel()

    if err := priceMonitor.Start(ctx); err != nil {
        log.Fatalf("Failed to start price monitor service: %v", err)
    }

    // 等待退出信号
    quit := make(chan os.Signal, 1)
    signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
    <-quit

    log.Println("Shutting down price monitor service...")

    // 优雅关闭
    shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer shutdownCancel()

    if err := priceMonitor.Stop(shutdownCtx); err != nil {
        log.Printf("Error during shutdown: %v", err)
    }

    log.Println("Price monitor service stopped")
}
```

#### 9.2 Docker配置
```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o price-monitor ./cmd/price-monitor

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/price-monitor .
COPY --from=builder /app/config.yaml .

EXPOSE 8080 9090
CMD ["./price-monitor"]
```

#### 9.2 Docker Compose部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  price-monitor:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: price-monitor-service
    ports:
      - "8080:8080"
      - "9090:9090"
    environment:
      - REDIS_ADDRESSES=redis:6379
      - LOG_LEVEL=info
    volumes:
      - ./config.yaml:/root/config.yaml:ro
      - ./logs:/root/logs
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    container_name: price-monitor-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  redis-data:
```

### 10. 错误处理和容错

#### 10.1 错误类型定义
```go
// 错误类型枚举
type ErrorType string

const (
    ErrorTypeConnection   ErrorType = "connection"
    ErrorTypeValidation   ErrorType = "validation"
    ErrorTypeStaleData    ErrorType = "stale_data"
    ErrorTypeRateLimit    ErrorType = "rate_limit"
    ErrorTypeInternal     ErrorType = "internal"
)

// 价格服务错误
type PriceError struct {
    Type      ErrorType `json:"type"`
    Code      string    `json:"code"`
    Message   string    `json:"message"`
    Symbol    string    `json:"symbol,omitempty"`
    Provider  string    `json:"provider,omitempty"`
    Timestamp time.Time `json:"timestamp"`
    Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

func (e *PriceError) Error() string {
    return fmt.Sprintf("[%s:%s] %s", e.Type, e.Code, e.Message)
}

// 常用错误定义
var (
    ErrStaleData = &PriceError{
        Type:    ErrorTypeStaleData,
        Code:    "STALE_DATA",
        Message: "Price data is too old",
    }
    
    ErrConnectionLost = &PriceError{
        Type:    ErrorTypeConnection,
        Code:    "CONNECTION_LOST",
        Message: "WebSocket connection lost",
    }
    
    ErrPriceAnomaly = &PriceError{
        Type:    ErrorTypeValidation,
        Code:    "PRICE_ANOMALY",
        Message: "Price change exceeds threshold",
    }
)
```

### 11. 性能优化

#### 11.1 批量处理
```go
// 批量写入器
type BatchWriter struct {
    redis       redis.UniversalClient
    batchSize   int
    flushDelay  time.Duration
    buffer      chan *PriceData
    stopCh      chan struct{}
}

func (b *BatchWriter) Start() {
    ticker := time.NewTicker(b.flushDelay)
    defer ticker.Stop()
    
    batch := make([]*PriceData, 0, b.batchSize)
    
    for {
        select {
        case data := <-b.buffer:
            batch = append(batch, data)
            if len(batch) >= b.batchSize {
                b.flushBatch(batch)
                batch = batch[:0]
            }
            
        case <-ticker.C:
            if len(batch) > 0 {
                b.flushBatch(batch)
                batch = batch[:0]
            }
            
        case <-b.stopCh:
            if len(batch) > 0 {
                b.flushBatch(batch)
            }
            return
        }
    }
}
```

#### 11.2 连接池优化
```go
// Redis连接池配置
type RedisConfig struct {
    ConfigName   string        `yaml:"config_name"`    // 使用项目现有的Redis配置名称
    PoolSize     int           `yaml:"pool_size"`
    MaxRetries   int           `yaml:"max_retries"`
}

func NewRedisClient(config *RedisConfig) *gredis.Redis {
    // 复用项目现有的Redis服务
    return service.Redis().Client(config.ConfigName)
}
```

## 总结

该价格监控服务提供了完整的实时价格数据解决方案，具备以下特点：

1. **高可用性**：多重容错机制，自动重连，健康监控
2. **低延迟**：WebSocket实时数据，Redis缓存，批量处理
3. **可扩展性**：支持多实例部署，水平扩展
4. **可观测性**：完整的监控指标，告警系统
5. **易于集成**：标准化的客户端SDK，清晰的API接口

该服务可以独立部署和维护，为闪兑系统及其他需要实时价格数据的服务提供可靠的数据支撑。