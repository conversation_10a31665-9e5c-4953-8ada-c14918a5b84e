# 转账消息统一化迁移指南

## 概述

本指南说明如何将现有的分散转账消息代码迁移到新的统一转账消息服务 `TransferMessageService`。

## 当前问题

### 1. 代码重复
- `internal/logic/transfer/transfer_common.go` - 转账通用消息构建
- `internal/logic/transfer/responses.go` - 转账响应消息构建  
- `internal/task/transfer_notification.go` - 转账通知处理
- `internal/bot/transfer/message_handler.go` - 转账消息处理

### 2. 消息模板不统一
- 不同地方使用不同的国际化键
- 金额格式化逻辑重复
- 用户名显示逻辑不一致

### 3. 通知发送逻辑分散
- 多个地方都有发送通知的代码
- 错误处理不统一
- 语言设置处理不一致

## 新的统一方案

### 核心服务
```go
// 统一的转账消息服务
type TransferMessageService interface {
    BuildTransferMessage(ctx context.Context, req *TransferMessageRequest) (*TransferMessageResponse, error)
    SendTransferNotification(ctx context.Context, req *TransferNotificationRequestV2) error
    FormatTransferAmount(ctx context.Context, transfer *entity.Transfers) (string, error)
    GetUserDisplayName(ctx context.Context, userId uint64, username string) string
}
```

### 消息类型
- `MessageTypeStatus` - 状态消息
- `MessageTypeSuccess` - 成功消息  
- `MessageTypeNotification` - 通知消息
- `MessageTypeConfirmation` - 确认消息
- `MessageTypeInitial` - 初始消息

### 查看者类型
- `ViewerTypeSender` - 发送方视角
- `ViewerTypeReceiver` - 接收方视角
- `ViewerTypePublic` - 公共视角

## 迁移步骤

### 第一步：替换 transfer_common.go

**旧代码：**
```go
// internal/logic/transfer/transfer_common.go
func BuildTransferSuccessMessage(ctx context.Context, transfer *entity.Transfers) string {
    // 复杂的消息构建逻辑
}

func SendTransferNotificationToReceiver(ctx context.Context, transfer *entity.Transfers) error {
    // 复杂的通知发送逻辑
}
```

**新代码：**
```go
// 构建成功消息
req := &TransferMessageRequest{
    Transfer:    transfer,
    MessageType: MessageTypeSuccess,
    ViewerType:  ViewerTypeSender,
}
resp, err := TransferMessage().BuildTransferMessage(ctx, req)

// 发送通知
notifyReq := &TransferNotificationRequestV2{
    Transfer:         transfer,
    ReceiverTgID:     receiverTgID,
    NotificationType: NotificationTypeCompleted,
}
err = TransferMessage().SendTransferNotification(ctx, notifyReq)
```

### 第二步：替换 responses.go

**旧代码：**
```go
// internal/logic/transfer/responses.go
func BuildFinalTransferMessage(ctx context.Context, transfer *entity.Transfers) string {
    // 状态消息构建逻辑
}

func BuildInitialTransferMessage(ctx context.Context, transfer *entity.Transfers) string {
    // 初始消息构建逻辑
}
```

**新代码：**
```go
// 构建最终状态消息
finalReq := &TransferMessageRequest{
    Transfer:    transfer,
    MessageType: MessageTypeStatus,
    ViewerType:  ViewerTypePublic,
}
finalResp, err := TransferMessage().BuildTransferMessage(ctx, finalReq)

// 构建初始消息
initialReq := &TransferMessageRequest{
    Transfer:    transfer,
    MessageType: MessageTypeInitial,
    ViewerType:  ViewerTypePublic,
}
initialResp, err := TransferMessage().BuildTransferMessage(ctx, initialReq)
```

### 第三步：替换 transfer_notification.go

**旧代码：**
```go
// internal/task/transfer_notification.go
func HandleTransferNotifications(ctx context.Context, transfer *entity.Transfers) error {
    // 复杂的通知处理逻辑
    // 手动构建消息
    // 手动发送通知
}
```

**新代码：**
```go
// 统一的通知处理
req := &TransferNotificationRequestV2{
    Transfer:         transfer,
    ReceiverTgID:     receiverTgID,
    NotificationType: NotificationTypeCompleted,
}
err := TransferMessage().SendTransferNotification(ctx, req)
```

### 第四步：替换消息处理器

**旧代码：**
```go
// internal/bot/transfer/message_handler.go
func buildTransferConfirmMessage(ctx context.Context, transfer *entity.Transfers) string {
    // 手动构建确认消息
}
```

**新代码：**
```go
// 使用统一服务构建确认消息
req := &TransferMessageRequest{
    Transfer:    transfer,
    MessageType: MessageTypeConfirmation,
    ViewerType:  ViewerTypeSender,
    Context: map[string]string{
        "recipientUsername":   recipientUsername,
        "recipientTelegramId": recipientTelegramId,
    },
}
resp, err := TransferMessage().BuildTransferMessage(ctx, req)
```

## 优势

### 1. 代码复用
- 统一的消息构建逻辑
- 统一的金额格式化
- 统一的用户名显示

### 2. 一致性
- 统一的国际化键使用
- 统一的错误处理
- 统一的语言设置

### 3. 可维护性
- 单一职责原则
- 易于测试
- 易于扩展

### 4. 类型安全
- 强类型的消息类型
- 强类型的查看者类型
- 强类型的通知类型

## 测试

使用提供的示例代码进行测试：

```go
example := NewTransferMessageUsageExample()
err := example.RunAllExamples(ctx)
if err != nil {
    log.Printf("测试失败: %v", err)
}
```

## 注意事项

1. **渐进式迁移**：建议逐个文件迁移，确保每次迁移后功能正常
2. **保持向后兼容**：在迁移期间保留旧的函数，标记为废弃
3. **充分测试**：每次迁移后进行充分的功能测试
4. **文档更新**：更新相关的API文档和使用说明

## 迁移检查清单

- [ ] 替换 `transfer_common.go` 中的消息构建函数
- [ ] 替换 `responses.go` 中的消息构建函数
- [ ] 替换 `transfer_notification.go` 中的通知处理逻辑
- [ ] 替换各个消息处理器中的消息构建逻辑
- [ ] 更新单元测试
- [ ] 更新集成测试
- [ ] 更新文档
- [ ] 删除废弃的代码

## 后续优化

1. **缓存优化**：对频繁使用的用户信息进行缓存
2. **模板优化**：进一步优化国际化模板
3. **性能优化**：对消息构建性能进行优化
4. **监控添加**：添加消息发送成功率监控
