# API Layer for Swap Feature

## 1. Overview

This document defines the service interfaces, data structures, and API contracts for the swap feature implementation.

## 2. Service Interfaces

### 2.1 ISwapService Interface

```go
package service

import (
    "context"
    "time"
    "github.com/shopspring/decimal"
)

// ISwapService defines the main swap service interface
type ISwapService interface {
    // Product Management
    GetActiveProducts(ctx context.Context) ([]*SwapProduct, error)
    GetProductBySymbol(ctx context.Context, symbol string) (*SwapProduct, error)
    GetProductByTokenPair(ctx context.Context, baseTokenID, quoteTokenID uint) (*SwapProduct, error)
    
    // Quote Management
    CreateQuote(ctx context.Context, req *SwapQuoteRequest) (*SwapQuote, error)
    GetQuote(ctx context.Context, quoteID string) (*SwapQuote, error)
    ValidateQuote(ctx context.Context, quoteID string) error
    
    // Order Management
    CreateOrder(ctx context.Context, req *SwapOrderRequest) (*SwapOrder, error)
    ExecuteOrder(ctx context.Context, orderID string) (*SwapResult, error)
    GetOrder(ctx context.Context, orderID string) (*SwapOrder, error)
    GetUserOrders(ctx context.Context, userID uint64, filter *OrderFilter) ([]*SwapOrder, error)
    
    // Statistics
    GetUserSwapStats(ctx context.Context, userID uint64) (*UserSwapStats, error)
    GetProductStats(ctx context.Context, productID uint) (*ProductStats, error)
}
```

### 2.2 Using Existing Price Service

> **Note**: The swap feature should use the existing `IPriceClient` interface from `internal/service/price_client.go` instead of defining a new price service interface.

```go
// Use the existing IPriceClient from internal/service
import "telegram-bot-api/internal/service"

// The existing IPriceClient provides:
// - GetRealTimePrice(ctx, symbol) - Get crypto prices with auto freshness check
// - GetMultiplePrices(ctx, symbols) - Batch get crypto prices
// - GetFiatPrice(ctx, asset, currency) - Get fiat exchange rates
// - And more...

// Example usage in swap service:
priceClient := service.PriceClientInstance()
```

### 2.3 IWalletService Extension

```go
// Additional methods for IWalletService to support swap
type IWalletService interface {
    // Existing methods...
    
    // Swap-specific methods
    LockBalance(ctx context.Context, userID uint64, tokenID uint, amount decimal.Decimal) (string, error)
    UnlockBalance(ctx context.Context, lockID string) error
    TransferForSwap(ctx context.Context, req *SwapTransferRequest) error
}
```

## 3. Data Structures

### 3.1 Request Structures

```go
// SwapQuoteRequest represents a request for swap quote
type SwapQuoteRequest struct {
    UserID       uint64          `json:"user_id"`
    FromTokenID  uint            `json:"from_token_id"`
    ToTokenID    uint            `json:"to_token_id"`
    Amount       decimal.Decimal `json:"amount"`
    AmountType   string          `json:"amount_type"` // "base" or "quote"
}

// SwapOrderRequest represents a request to create swap order
type SwapOrderRequest struct {
    UserID          uint64 `json:"user_id"`
    QuoteID         string `json:"quote_id"`
    PaymentPassword string `json:"payment_password"`
    ClientOrderID   string `json:"client_order_id,omitempty"`
}

// OrderFilter for querying orders
type OrderFilter struct {
    Status    []string   `json:"status,omitempty"`
    StartTime *time.Time `json:"start_time,omitempty"`
    EndTime   *time.Time `json:"end_time,omitempty"`
    Page      int        `json:"page"`
    PageSize  int        `json:"page_size"`
}
```

### 3.2 Response Structures

```go
// SwapProduct represents a trading pair configuration
type SwapProduct struct {
    ProductID              uint            `json:"product_id"`
    BaseTokenID            uint            `json:"base_token_id"`
    BaseTokenSymbol        string          `json:"base_token_symbol"`
    QuoteTokenID           uint            `json:"quote_token_id"`
    QuoteTokenSymbol       string          `json:"quote_token_symbol"`
    Symbol                 string          `json:"symbol"`
    IsActive               bool            `json:"is_active"`
    AllowBuy               bool            `json:"allow_buy"`
    AllowSell              bool            `json:"allow_sell"`
    MinBaseAmount          decimal.Decimal `json:"min_base_amount"`
    MaxBaseAmount          decimal.Decimal `json:"max_base_amount"`
    FeeRate                decimal.Decimal `json:"fee_rate"`
    AllowedSlippagePercent decimal.Decimal `json:"allowed_slippage_percent"`
    CurrentPrice           decimal.Decimal `json:"current_price,omitempty"`
}

// SwapQuote represents a price quote
type SwapQuote struct {
    QuoteID          string          `json:"quote_id"`
    UserID           uint64          `json:"user_id"`
    ProductID        uint            `json:"product_id"`
    FromTokenSymbol  string          `json:"from_token_symbol"`
    ToTokenSymbol    string          `json:"to_token_symbol"`
    FromAmount       decimal.Decimal `json:"from_amount"`
    ToAmount         decimal.Decimal `json:"to_amount"`
    Rate             decimal.Decimal `json:"rate"`
    PriceSource      string          `json:"price_source"`
    FeeAmount        decimal.Decimal `json:"fee_amount"`
    FeeTokenSymbol   string          `json:"fee_token_symbol"`
    NetReceiveAmount decimal.Decimal `json:"net_receive_amount"`
    CreatedAt        time.Time       `json:"created_at"`
    ExpiresAt        time.Time       `json:"expires_at"`
    ValidSeconds     int             `json:"valid_seconds"`
}

// SwapOrder represents a swap order
type SwapOrder struct {
    OrderID       uint64          `json:"order_id"`
    OrderSN       string          `json:"order_sn"`
    UserID        uint64          `json:"user_id"`
    ProductID     uint            `json:"product_id"`
    Symbol        string          `json:"symbol"`
    TradeType     string          `json:"trade_type"`
    AmountBase    decimal.Decimal `json:"amount_base"`
    AmountQuote   decimal.Decimal `json:"amount_quote"`
    Price         decimal.Decimal `json:"price"`
    FeeAmount     decimal.Decimal `json:"fee_amount"`
    FeeTokenID    uint            `json:"fee_token_id"`
    Status        string          `json:"status"`
    ErrorMessage  string          `json:"error_message,omitempty"`
    CreatedAt     time.Time       `json:"created_at"`
    CompletedAt   *time.Time      `json:"completed_at,omitempty"`
}

// SwapResult represents the result of swap execution
type SwapResult struct {
    Success         bool            `json:"success"`
    OrderID         uint64          `json:"order_id"`
    OrderSN         string          `json:"order_sn"`
    Status          string          `json:"status"`
    FromAmount      decimal.Decimal `json:"from_amount"`
    ToAmount        decimal.Decimal `json:"to_amount"`
    ActualRate      decimal.Decimal `json:"actual_rate"`
    FeeAmount       decimal.Decimal `json:"fee_amount"`
    Slippage        decimal.Decimal `json:"slippage,omitempty"`
    ErrorCode       string          `json:"error_code,omitempty"`
    ErrorMessage    string          `json:"error_message,omitempty"`
    CompletedAt     time.Time       `json:"completed_at,omitempty"`
}
```

### 3.3 Statistics Structures

```go
// UserSwapStats represents user's swap statistics
type UserSwapStats struct {
    UserID           uint64          `json:"user_id"`
    TotalSwaps       int             `json:"total_swaps"`
    SuccessfulSwaps  int             `json:"successful_swaps"`
    TotalVolumeUSDT  decimal.Decimal `json:"total_volume_usdt"`
    TotalFeesUSDT    decimal.Decimal `json:"total_fees_usdt"`
    LastSwapAt       *time.Time      `json:"last_swap_at,omitempty"`
    FavoriteProducts []string        `json:"favorite_products"`
}

// ProductStats represents product trading statistics
type ProductStats struct {
    ProductID        uint            `json:"product_id"`
    Symbol           string          `json:"symbol"`
    Volume24h        decimal.Decimal `json:"volume_24h"`
    Volume7d         decimal.Decimal `json:"volume_7d"`
    TransactionCount int             `json:"transaction_count"`
    SuccessRate      decimal.Decimal `json:"success_rate"`
    AverageSlippage  decimal.Decimal `json:"average_slippage"`
}
```

## 4. Service Implementation

### 4.1 SwapService Implementation Structure

```go
package impl

import (
    "context"
    "sync"
    "time"
    
    "github.com/shopspring/decimal"
    "telegram-bot-api/internal/service"
)

type SwapService struct {
    priceService   service.IPriceService
    walletService  service.IWalletService
    userService    service.IUserService
    
    // DAOs
    productDAO     dao.IExchangeProductDAO
    orderDAO       dao.IExchangeOrderDAO
    
    // Configuration
    config         *SwapConfig
    
    // Cache
    productCache   sync.Map
    quoteTTL       time.Duration
}

// NewSwapService creates a new swap service instance
func NewSwapService(
    priceService service.IPriceService,
    walletService service.IWalletService,
    userService service.IUserService,
    config *SwapConfig,
) service.ISwapService {
    return &SwapService{
        priceService:  priceService,
        walletService: walletService,
        userService:   userService,
        config:        config,
        quoteTTL:      30 * time.Second,
    }
}
```

### 4.2 Core Method Implementations

```go
// CreateQuote generates a new swap quote
func (s *SwapService) CreateQuote(ctx context.Context, req *SwapQuoteRequest) (*SwapQuote, error) {
    // Validate request
    if err := s.validateQuoteRequest(req); err != nil {
        return nil, err
    }
    
    // Get product configuration
    product, err := s.getProduct(req.FromTokenID, req.ToTokenID)
    if err != nil {
        return nil, err
    }
    
    // Check product status
    if !product.IsActive {
        return nil, service.ErrProductInactive
    }
    
    // Get current price
    price, err := s.priceService.CalculateExchangeRate(
        ctx, 
        product.BaseTokenSymbol, 
        product.QuoteTokenSymbol,
    )
    if err != nil {
        return nil, err
    }
    
    // Apply spread
    effectiveRate := s.applySpread(price, product.SpreadRate, req.TradeType)
    
    // Calculate amounts
    amounts := s.calculateAmounts(req.Amount, effectiveRate, req.AmountType)
    
    // Calculate fees
    fee := s.calculateFee(amounts, product)
    
    // Create quote
    quote := &SwapQuote{
        QuoteID:          generateQuoteID(),
        UserID:           req.UserID,
        ProductID:        product.ProductID,
        FromTokenSymbol:  s.getFromSymbol(product, req.TradeType),
        ToTokenSymbol:    s.getToSymbol(product, req.TradeType),
        FromAmount:       amounts.FromAmount,
        ToAmount:         amounts.ToAmount,
        Rate:             effectiveRate,
        PriceSource:      price.Provider,
        FeeAmount:        fee.Amount,
        FeeTokenSymbol:   fee.TokenSymbol,
        NetReceiveAmount: s.calculateNetAmount(amounts, fee),
        CreatedAt:        time.Now(),
        ExpiresAt:        time.Now().Add(s.quoteTTL),
        ValidSeconds:     int(s.quoteTTL.Seconds()),
    }
    
    // Store quote in cache
    if err := s.storeQuote(ctx, quote); err != nil {
        return nil, err
    }
    
    return quote, nil
}

// ExecuteOrder executes a swap order
func (s *SwapService) ExecuteOrder(ctx context.Context, orderID string) (*SwapResult, error) {
    // Get order
    order, err := s.orderDAO.GetByOrderSN(ctx, orderID)
    if err != nil {
        return nil, err
    }
    
    // Validate order status
    if order.Status != "pending" {
        return nil, service.ErrOrderNotPending
    }
    
    // Start transaction
    tx, err := s.beginTransaction(ctx)
    if err != nil {
        return nil, err
    }
    defer tx.Rollback()
    
    // Update order status to processing
    order.Status = "processing"
    if err := s.orderDAO.Update(tx, order); err != nil {
        return nil, err
    }
    
    // Validate current price
    currentPrice, err := s.priceService.GetRealTimePrice(ctx, order.Symbol)
    if err != nil {
        return s.failOrder(tx, order, "price_fetch_failed", err.Error())
    }
    
    // Check slippage
    slippage := s.calculateSlippage(order.Price, currentPrice.Price)
    product, _ := s.getProductByID(order.ProductID)
    if slippage.GreaterThan(product.AllowedSlippagePercent) {
        return s.failOrder(tx, order, "slippage_exceeded", "Price moved too much")
    }
    
    // Execute wallet transfers
    transferReq := &SwapTransferRequest{
        UserID:       order.UserID,
        FromTokenID:  s.getFromTokenID(order),
        ToTokenID:    s.getToTokenID(order),
        FromAmount:   order.AmountBase,
        ToAmount:     order.AmountQuote,
        FeeAmount:    order.FeeAmount,
        FeeTokenID:   order.FeeTokenID,
    }
    
    if err := s.walletService.TransferForSwap(tx, transferReq); err != nil {
        return s.failOrder(tx, order, "transfer_failed", err.Error())
    }
    
    // Update order as completed
    order.Status = "completed"
    order.CompletedAt = time.Now()
    if err := s.orderDAO.Update(tx, order); err != nil {
        return nil, err
    }
    
    // Commit transaction
    if err := tx.Commit(); err != nil {
        return nil, err
    }
    
    // Return result
    return &SwapResult{
        Success:      true,
        OrderID:      order.OrderID,
        OrderSN:      order.OrderSN,
        Status:       order.Status,
        FromAmount:   order.AmountBase,
        ToAmount:     order.AmountQuote,
        ActualRate:   currentPrice.Price,
        FeeAmount:    order.FeeAmount,
        Slippage:     slippage,
        CompletedAt:  order.CompletedAt,
    }, nil
}
```

## 5. Error Handling

### 5.1 Service Error Types

```go
package service

import "errors"

var (
    // Product errors
    ErrProductNotFound     = errors.New("swap product not found")
    ErrProductInactive     = errors.New("swap product is inactive")
    ErrDirectionNotAllowed = errors.New("trade direction not allowed")
    
    // Quote errors
    ErrQuoteNotFound = errors.New("quote not found")
    ErrQuoteExpired  = errors.New("quote has expired")
    ErrQuoteUsed     = errors.New("quote already used")
    
    // Order errors
    ErrOrderNotFound   = errors.New("order not found")
    ErrOrderNotPending = errors.New("order is not in pending status")
    
    // Validation errors
    ErrInvalidAmount       = errors.New("invalid amount")
    ErrAmountTooSmall      = errors.New("amount below minimum")
    ErrAmountTooLarge      = errors.New("amount exceeds maximum")
    ErrInsufficientBalance = errors.New("insufficient balance")
    
    // Price errors
    ErrPriceStale       = errors.New("price data is stale")
    ErrSlippageExceeded = errors.New("price slippage exceeded")
)
```

### 5.2 Error Response Wrapper

```go
type ServiceError struct {
    Code    string                 `json:"code"`
    Message string                 `json:"message"`
    Details map[string]interface{} `json:"details,omitempty"`
}

func WrapServiceError(err error, details ...map[string]interface{}) *ServiceError {
    svcErr := &ServiceError{
        Code:    getErrorCode(err),
        Message: err.Error(),
    }
    
    if len(details) > 0 {
        svcErr.Details = details[0]
    }
    
    return svcErr
}
```

## 6. Service Registration

### 6.1 Service Factory

```go
package container

func RegisterSwapService(c *Container) {
    c.Register("swap_service", func() interface{} {
        config := &SwapConfig{
            DefaultQuoteTTL:     30 * time.Second,
            MaxSlippagePercent:  decimal.NewFromFloat(0.005),
            EnableMetrics:       true,
        }
        
        return impl.NewSwapService(
            c.PriceService(),
            c.WalletService(),
            c.UserService(),
            config,
        )
    })
}
```

### 6.2 Service Initialization

```go
// In boot/boot.go
func InitializeSwapService(ctx context.Context) error {
    // Register service
    container.RegisterSwapService(container.Default())
    
    // Initialize service
    swapService := container.Default().SwapService()
    
    // Load product configurations
    if err := swapService.LoadProducts(ctx); err != nil {
        return fmt.Errorf("failed to load swap products: %w", err)
    }
    
    // Start background tasks
    go swapService.StartQuoteCleanup(ctx)
    go swapService.StartMetricsCollection(ctx)
    
    log.Info("Swap service initialized successfully")
    return nil
}
```

## 7. Testing

### 7.1 Unit Test Example

```go
func TestSwapService_CreateQuote(t *testing.T) {
    // Setup
    mockPriceService := &MockPriceService{}
    mockWalletService := &MockWalletService{}
    service := NewSwapService(mockPriceService, mockWalletService, nil, testConfig)
    
    // Test valid quote creation
    req := &SwapQuoteRequest{
        UserID:      1,
        FromTokenID: 2, // ETH
        ToTokenID:   1, // USDT
        Amount:      decimal.NewFromFloat(1.0),
        AmountType:  "base",
    }
    
    mockPriceService.On("CalculateExchangeRate", "ETH", "USDT").
        Return(decimal.NewFromFloat(2450.00), nil)
    
    quote, err := service.CreateQuote(context.Background(), req)
    
    assert.NoError(t, err)
    assert.NotEmpty(t, quote.QuoteID)
    assert.Equal(t, decimal.NewFromFloat(2450.00), quote.Rate)
}
```

## 8. API Documentation

### 8.1 REST API Endpoints (if applicable)

```yaml
# OpenAPI 3.0 specification
paths:
  /api/v1/swap/products:
    get:
      summary: Get active swap products
      responses:
        200:
          description: List of active products
          
  /api/v1/swap/quote:
    post:
      summary: Create swap quote
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SwapQuoteRequest'
              
  /api/v1/swap/order:
    post:
      summary: Create and execute swap order
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SwapOrderRequest'
```