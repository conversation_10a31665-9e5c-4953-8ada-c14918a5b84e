# Swap Metrics Visualization Guide

## Overview
This guide explains how to visualize swap metrics using the bot commands and external monitoring tools.

## Bot Command Access

### Admin Metrics Command
Administrators can access comprehensive metrics through the Telegram bot:

```
/metrics - Display real-time swap metrics dashboard
```

The metrics command provides:
- Real-time performance overview
- System health status
- Security event monitoring
- Product performance statistics
- Historical reports (24h, 7d)

### Metrics Navigation
```
┌─────────────────────────────────────┐
│        📊 Swap Service Metrics       │
├─────────────────────────────────────┤
│ Real-time Overview                   │
│ • Quotes/Min: 45.2                  │
│ • Orders/Min: 12.3                  │
│ • Success Rate: 98.5%               │
│ • Volume Today: $1.23M              │
├─────────────────────────────────────┤
│ [📈 24h Report] [📊 7d Report]      │
│ [🔒 Security] [💰 Products]         │
│ [🔄 Refresh]                        │
└─────────────────────────────────────┘
```

## Metrics Data Structure

### Real-time Metrics
```go
type RealtimeSwapMetrics struct {
    QuotesPerMinute        float64
    OrdersPerMinute        float64
    SuccessRate            float64
    AverageProcessingTime  int64
    ActiveQuotes           int
    PendingOrders          int
    VolumeToday            decimal.Decimal
    PriceServiceHealth     string
    DatabaseLatency        int64
    RedisLatency           int64
    ErrorRate              float64
    SecurityEventsLastHour int
    BlockedTransactions    int
    Timestamp              time.Time
}
```

### Historical Metrics
```go
type HistoricalSwapMetrics struct {
    Period          string
    StartTime       time.Time
    EndTime         time.Time
    TotalOrders     int64
    CompletedOrders int64
    FailedOrders    int64
    TotalVolumeUSD  decimal.Decimal
    TotalFeesUSD    decimal.Decimal
    UniqueUsers     int64
    SuccessRate     float64
    ProductVolumes  map[uint]ProductVolumeMetrics
    TopUsers        []UserVolumeMetrics
    FailureReasons  map[string]int64
}
```

## Grafana Dashboard Setup

### 1. Data Source Configuration
```yaml
datasources:
  - name: SwapMetrics
    type: mysql
    url: ${DB_HOST}:${DB_PORT}
    database: ${DB_NAME}
    user: ${DB_USER}
    password: ${DB_PASSWORD}
```

### 2. Dashboard Panels

#### Orders Overview Panel
```sql
SELECT 
  created_at as time,
  COUNT(*) as orders_total,
  SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as orders_completed,
  SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as orders_failed
FROM exchange_orders
WHERE $__timeFilter(created_at)
GROUP BY time
ORDER BY time
```

#### Volume Tracking Panel
```sql
SELECT 
  created_at as time,
  SUM(amount_quote) as volume_usd,
  SUM(fee_amount * CASE 
    WHEN fee_charged_in = 'quote' THEN 1 
    ELSE price 
  END) as fees_usd
FROM exchange_orders
WHERE status = 'completed'
  AND $__timeFilter(created_at)
GROUP BY time
ORDER BY time
```

#### Success Rate Gauge
```sql
SELECT 
  (SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as success_rate
FROM exchange_orders
WHERE $__timeFilter(created_at)
```

#### Product Performance Table
```sql
SELECT 
  p.symbol,
  COUNT(o.order_id) as total_orders,
  SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as successful,
  SUM(o.amount_quote) as volume_usd,
  AVG(o.processing_time) as avg_latency_ms
FROM exchange_products p
LEFT JOIN exchange_orders o ON p.product_id = o.product_id
WHERE $__timeFilter(o.created_at)
GROUP BY p.product_id, p.symbol
ORDER BY volume_usd DESC
```

### 3. Alert Rules

```yaml
groups:
  - name: swap_alerts
    interval: 1m
    rules:
      - alert: LowSuccessRate
        expr: swap_success_rate < 95
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Swap success rate below threshold"
          description: "Success rate is {{ $value }}%"
      
      - alert: HighErrorRate
        expr: swap_error_rate > 5
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High swap error rate"
          description: "Error rate is {{ $value }}%"
      
      - alert: ServiceDown
        expr: swap_service_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Swap service is down"
```

## Custom Metrics Queries

### Daily Performance Summary
```sql
SELECT 
  DATE(created_at) as date,
  COUNT(*) as total_orders,
  SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful,
  SUM(amount_quote) as volume_usd,
  COUNT(DISTINCT user_id) as unique_users,
  AVG(processing_time) as avg_processing_ms
FROM exchange_orders
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

### User Activity Analysis
```sql
SELECT 
  u.telegram_id,
  COUNT(o.order_id) as total_swaps,
  SUM(o.amount_quote) as total_volume,
  AVG(sm.risk_score) as avg_risk_score,
  MAX(o.created_at) as last_swap
FROM users u
JOIN exchange_orders o ON u.id = o.user_id
LEFT JOIN swap_metrics sm ON o.order_sn = sm.order_id
WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY u.id
ORDER BY total_volume DESC
LIMIT 20;
```

### Security Events Timeline
```sql
SELECT 
  DATE_FORMAT(timestamp, '%Y-%m-%d %H:00:00') as hour,
  event_type,
  severity,
  COUNT(*) as event_count
FROM swap_security_events
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY hour, event_type, severity
ORDER BY hour DESC;
```

## Monitoring Best Practices

### 1. Key Metrics to Track
- **Performance**: Orders/minute, Processing time, Success rate
- **Business**: Volume (USD), Fee revenue, User growth
- **Security**: Risk events, Blocked transactions, Anomalies
- **System**: Service health, Latency, Error rates

### 2. Dashboard Refresh Intervals
- Real-time panels: 5-10 seconds
- Aggregated data: 1-5 minutes
- Historical reports: 15-30 minutes

### 3. Data Retention Policy
- Real-time metrics: 24 hours
- Hourly aggregates: 30 days
- Daily aggregates: 1 year
- Raw transaction data: As per compliance requirements

### 4. Performance Optimization
- Use materialized views for complex aggregations
- Index time-based columns for faster queries
- Implement data partitioning for large tables
- Cache frequently accessed metrics in Redis

## Troubleshooting Metrics

### Common Issues

1. **Missing Data Points**
   - Check metrics service status
   - Verify database connectivity
   - Review aggregation jobs

2. **Incorrect Calculations**
   - Validate timezone settings
   - Check decimal precision
   - Review aggregation logic

3. **Performance Issues**
   - Optimize database queries
   - Add missing indexes
   - Increase cache TTL

### Debug Commands
```sql
-- Check metrics collection status
SELECT 
  MAX(created_at) as last_metric,
  COUNT(*) as metrics_today
FROM swap_metrics
WHERE created_at >= CURDATE();

-- Verify aggregation jobs
SELECT 
  hour,
  created_at,
  TIMESTAMPDIFF(MINUTE, hour, created_at) as delay_minutes
FROM swap_metrics_hourly
ORDER BY hour DESC
LIMIT 10;

-- Analyze slow queries
SELECT 
  digest_text,
  count_star,
  avg_timer_wait/1000000000 as avg_ms,
  sum_rows_examined/count_star as avg_rows
FROM performance_schema.events_statements_summary_by_digest
WHERE schema_name = 'your_db'
ORDER BY avg_timer_wait DESC
LIMIT 10;
```