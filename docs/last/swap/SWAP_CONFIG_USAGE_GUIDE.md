# 闪兑配置参数使用指南

## 概述

本文档详细说明了闪兑系统中各种配置参数的使用场景、优先级和作用机制。闪兑系统采用多层配置架构，包括全局配置和产品特定配置，确保系统的灵活性和可控性。

## 配置架构

### 配置来源层次
1. **全局配置** - 存储在 `admin_config_items` 表，通过 `SwapConfigManager.GetConfig()` 读取
2. **产品配置** - 存储在 `exchange_products` 表，针对特定交易对的配置
3. **代码默认值** - 硬编码的兜底配置

### 配置优先级
```
产品特定配置 > 全局配置 > 代码默认值
```

## 全局配置参数详解

### 1. 服务控制配置







### 2. 交易限额配置

#### swap.limits_global_daily_limit_usd
- **类型**: Decimal
- **默认值**: 1,000,000 USD
- **作用**: 全平台每日交易总限额
- **使用场景**:
  - 风险控制
  - 流动性管理
  - 监管合规

#### swap.limits_user_daily_limit_usd
- **类型**: Decimal
- **默认值**: 10,000 USD
- **作用**: 单用户每日交易限额
- **使用场景**:
  - 用户风险控制
  - 反洗钱合规
  - 防止异常交易




#### swap.limits_min_order_amount_usd
- **类型**: Decimal
- **默认值**: 1 USD
- **作用**: 单笔交易最小金额
- **使用场景**:
  - 防止垃圾交易
  - 降低系统负载
  - 确保手续费合理性

#### swap.limits_max_order_amount_usd
- **类型**: Decimal
- **默认值**: 100,000 USD
- **作用**: 单笔交易最大金额
- **使用场景**:
  - 大额交易风控
  - 流动性保护
  - 价格冲击控制

### 3. 定价配置

#### swap.pricing_default_slippage
- **类型**: Decimal
- **默认值**: 0.01 (1%)
- **作用**: 默认滑点容忍度
- **使用场景**:
  - 价格波动保护
  - 用户体验优化

#### swap.pricing_max_slippage
- **类型**: Decimal
- **默认值**: 0.05 (5%)
- **作用**: 最大允许滑点
- **使用场景**:
  - 极端市场条件保护
  - 防止恶意套利

#### swap.pricing_price_validity_seconds
- **类型**: Integer
- **默认值**: 30 秒
- **作用**: 价格数据有效期
- **使用场景**:
  - 确保价格时效性
  - 防止过期价格交易

#### swap.pricing_quote_expiry_seconds
- **类型**: Integer
- **默认值**: 120 秒
- **作用**: 报价有效期
- **使用场景**:
  - 用户决策时间
  - 价格锁定期限

### 4. 手续费配置

#### swap.fees_default_rate
- **类型**: Decimal
- **默认值**: 0.002 (0.2%)
- **作用**: 默认手续费率
- **使用场景**:
  - 产品未设置特定费率时使用
  - 新产品的初始费率

#### swap.fees_discount_enabled
- **类型**: Boolean
- **默认值**: false
- **作用**: 是否启用手续费折扣
- **使用场景**:
  - VIP用户优惠
  - 交易量激励

### 5. 风控配置

#### swap.risk_price_deviation_threshold
- **类型**: Decimal
- **默认值**: 0.02 (2%)
- **作用**: 价格偏差告警阈值
- **使用场景**:
  - 异常价格监控
  - 市场操纵检测

#### swap.risk_velocity_check_enabled
- **类型**: Boolean
- **默认值**: false
- **作用**: 是否启用交易频率检查
- **使用场景**:
  - 防止高频套利
  - 系统负载保护

#### swap.risk_max_orders_per_minute
- **类型**: Integer
- **默认值**: 10
- **作用**: 每分钟最大订单数
- **使用场景**:
  - 频率限制
  - 防止刷单

### 6. 监控配置

#### swap.monitoring_alert_threshold_usd
- **类型**: Decimal
- **默认值**: 10,000 USD
- **作用**: 大额交易告警阈值
- **使用场景**:
  - 大额交易监控
  - 风险预警

#### swap.monitoring_log_all_orders
- **类型**: Boolean
- **默认值**: false
- **作用**: 是否记录所有订单详情
- **使用场景**:
  - 调试模式
  - 审计需求

## 产品配置参数详解

### 1. 基础信息

#### product_id
- **作用**: 产品唯一标识
- **使用场景**: 配置关联和查询

#### base_token / quote_token
- **作用**: 定义交易对的基础代币和计价代币
- **使用场景**: 交易方向判断和金额计算

#### symbol
- **作用**: 交易对符号（如 BTC/USDT）
- **使用场景**: 价格查询和显示

### 2. 状态控制

#### is_active
- **作用**: 产品总开关
- **使用场景**: 产品上下线控制

#### allow_buy / allow_sell
- **作用**: 控制买入/卖出方向
- **使用场景**: 单向交易限制

#### maintenance_message
- **作用**: 产品维护信息
- **使用场景**: 特定产品维护提示

### 3. 交易限制

#### min_base_amount_per_tx
- **作用**: 单笔最小基础代币数量
- **使用场景**: 产品特定的最小交易量

#### max_base_amount_per_tx
- **作用**: 单笔最大基础代币数量
- **使用场景**: 产品特定的最大交易量

#### daily_base_volume_limit
- **作用**: 产品每日交易量限制
- **使用场景**: 产品级别的流动性控制

### 4. 定价参数

#### price_source
- **作用**: 价格数据来源标识
- **使用场景**: 
  - 价格获取路由
  - 多数据源切换

#### allowed_slippage_percent
- **作用**: 产品特定的最大滑点
- **使用场景**: 
  - 覆盖全局滑点设置
  - 特殊产品的滑点控制

#### spread_rate
- **作用**: 价差率
- **使用场景**:
  - 买入价格 = 市场价格 × (1 + spread_rate)
  - 卖出价格 = 市场价格 × (1 - spread_rate)

### 5. 手续费参数

#### fee_strategy
- **作用**: 手续费策略标识
- **使用场景**: 确定手续费计算方式

#### output_fee_rate
- **作用**: 输出代币手续费率
- **使用场景**: 
  - 覆盖全局默认费率
  - 产品特定定价策略

#### min_output_fee_amount
- **作用**: 最小手续费金额
- **使用场景**: 确保小额交易的手续费收入

## 闪兑完整流程与配置参数使用

### 阶段1: 产品列表获取

**流程**: 用户请求可用交易对列表

**使用的配置参数**:
- `swap.enabled` - 检查服务是否启用
- `swap.maintenance_mode` - 检查是否维护模式
- `is_active` - 过滤激活的产品
- `status` - 产品状态检查
- `display_order` - 排序显示

**配置影响**:
- 服务关闭时返回维护信息
- 只显示激活且可用的交易对
- 按配置顺序排列产品

### 阶段2: 交易方向选择

**流程**: 用户选择买入或卖出方向

**使用的配置参数**:
- `allow_buy` - 是否允许买入基础代币
- `allow_sell` - 是否允许卖出基础代币
- `maintenance_message` - 产品特定维护信息

**配置影响**:
- 禁用的方向不显示或置灰
- 显示相应的限制说明

### 阶段3: 金额输入与验证

**流程**: 用户输入交易金额，系统进行验证

**使用的配置参数**:
- `min_base_amount_per_tx` - 产品最小交易量
- `max_base_amount_per_tx` - 产品最大交易量
- `swap.limits_min_order_amount_usd` - 全局最小金额
- `swap.limits_max_order_amount_usd` - 全局最大金额
- `swap.limits_user_daily_limit_usd` - 用户日限额
- `daily_base_volume_limit` - 产品日限额

**配置影响**:
- 金额低于最小值时提示错误
- 金额超过最大值时提示错误
- 超过日限额时拒绝交易

### 阶段4: 报价生成

**流程**: 系统根据市场价格和配置生成报价

**使用的配置参数**:
- `price_source` - 确定价格数据来源
- `spread_rate` - 计算买卖价差
- `output_fee_rate` - 计算手续费
- `swap.fees_default_rate` - 默认手续费率
- `min_output_fee_amount` - 最小手续费（产品级别）
- `swap.pricing_quote_expiry_seconds` - 报价有效期

**配置影响**:
- 买入价 = 市场价 × (1 + spread_rate)
- 卖出价 = 市场价 × (1 - spread_rate)
- 手续费 = 输出金额 × fee_rate (不低于最小值)
- 报价在指定时间后过期

### 阶段5: 限额检查

**流程**: 系统检查各种交易限制

**使用的配置参数**:
- `swap.limits_global_daily_limit_usd` - 全局日限额
- `swap.limits_user_daily_limit_usd` - 用户日限额
- `swap.risk_velocity_check_enabled` - 是否检查交易频率
- `swap.risk_max_orders_per_minute` - 频率限制

**配置影响**:
- 超过全局限额时拒绝所有用户交易
- 超过用户限额时拒绝该用户交易
- 交易过于频繁时要求等待

### 阶段6: 订单创建

**流程**: 用户确认报价，系统创建订单

**使用的配置参数**:
- 所有报价阶段的参数重新验证
- `swap.pricing_quote_expiry_seconds` - 检查报价是否过期

**配置影响**:
- 报价过期时要求重新获取
- 参数变化时重新计算

### 阶段7: 订单执行

**流程**: 系统执行实际的代币兑换

**使用的配置参数**:
- `swap.maintenance_mode` - 执行前再次检查维护状态
- `allowed_slippage_percent` - 产品特定滑点限制
- `swap.pricing_max_slippage` - 全局最大滑点
- `swap.pricing_price_validity_seconds` - 价格数据时效性

**配置影响**:
- 维护模式下拒绝执行
- 价格滑点超限时执行失败
- 价格数据过期时重新获取

### 阶段8: 资金操作

**流程**: 执行实际的资金转移

**使用的配置参数**:
- `output_fee_rate` - 确定实际扣除的手续费
- `min_output_fee_amount` - 应用最小手续费（产品级别）

**配置影响**:
- 从用户输出代币中扣除手续费
- 确保手续费不低于产品设置的最小值

### 阶段9: 监控与告警

**流程**: 系统监控交易并触发相应告警

**使用的配置参数**:
- `swap.monitoring_alert_threshold_usd` - 大额交易告警
- `swap.risk_price_deviation_threshold` - 价格异常告警
- `swap.monitoring_log_all_orders` - 详细日志记录

**配置影响**:
- 大额交易触发告警通知
- 价格异常时发送风险提醒
- 根据配置决定日志详细程度

## 配置参数优先级实例

### 手续费率优先级
1. **产品特定费率** (`exchange_products.output_fee_rate`)
2. **全局默认费率** (`swap.fees_default_rate`)
3. **代码默认值** (0.002)

### 滑点限制优先级
1. **产品特定滑点** (`exchange_products.allowed_slippage_percent`)
2. **全局最大滑点** (`swap.pricing_max_slippage`)
3. **代码默认值** (0.01)

### 交易限额优先级
1. **产品特定限额** (`exchange_products.min/max_base_amount_per_tx`)
2. **全局限额** (`swap.limits_min/max_order_amount_usd`)
3. **代码默认值**

## 配置管理最佳实践

### 1. 配置变更流程
- 通过管理后台修改 `admin_config_items` 表
- 配置变更实时生效（60秒缓存TTL）
- 重要变更需要通知相关人员

### 2. 监控建议
- 监控配置参数的使用频率
- 跟踪因配置限制被拒绝的交易
- 定期评估限额设置的合理性

### 3. 应急处理
- 紧急情况下可通过 `swap.enabled` 快速关闭服务
- 使用 `maintenance_mode` 进行计划性维护
- 通过调整限额应对异常市场情况

## 总结

闪兑系统的配置参数设计遵循分层管理原则，既保证了系统的灵活性，又确保了风险控制的有效性。通过合理配置这些参数，可以实现：

- **风险控制**: 通过多层限额和滑点控制
- **用户体验**: 通过合理的费率和时效设置
- **系统稳定**: 通过频率限制和维护模式
- **业务灵活**: 通过产品特定配置满足不同需求

配置参数的使用贯穿整个闪兑流程，从产品展示到最终执行，每个环节都有相应的配置控制，确保系统的安全、稳定和高效运行。
