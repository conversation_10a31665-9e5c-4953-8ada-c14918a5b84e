# 闪兑功能技术细节解答

## 目录
1. [报价详情中的当前价格与市场价格的关系](#1-报价详情中的当前价格与市场价格的关系)
2. [价格精度处理方式](#2-价格精度处理方式)
3. [兑换成功页面的汇率来源](#3-兑换成功页面的汇率来源)
4. [价差率配置](#4-价差率配置)
5. [手续费率配置](#5-手续费率配置)
6. [滑点参数配置](#6-滑点参数配置)
7. [总结](#总结)

## 1. 报价详情中的当前价格与市场价格的关系

**答案**：报价详情中的当前价格（如 1 ETH = 2407.11471 USDT）**不是**纯市场价格，而是**应用了价差（spread）后的价格**。

### 价格计算逻辑：

文档中提到的市场价格 1 ETH = 2450 USDT 是从交易所获取的原始价格，而用户看到的价格是经过价差调整后的价格。

```go
// 获取实时市场价格
priceData, err := s.priceClient.GetRealTimePrice(ctx, priceSymbol)
price = priceData.Price  // 这是市场价格，如 2450 USDT

// 应用价差
if tradeType == "buy" {
    // 用户买入：提高价格
    price = price.Mul(decimal.NewFromInt(1).Add(spreadRate))
    // 假设 spreadRate = 0.002 (0.2%)
    // price = 2450 × 1.002 = 2454.90
} else {
    // 用户卖出：降低价格
    price = price.Mul(decimal.NewFromInt(1).Sub(spreadRate))
    // price = 2450 × 0.998 = 2445.10
}
```

### 价格示例计算：
- 市场价格：2450.00 USDT
- 价差率：0.002 (0.2%)
- 买入价格：2450 × 1.002 = 2454.90 USDT
- 卖出价格：2450 × 0.998 = 2445.10 USDT

所以报价中显示的 2407.11471 是应用了价差后的实际交易价格。

## 2. 价格精度处理方式

**答案**：系统使用 `decimal.Decimal` 类型进行**精确计算**，不进行四舍五入或截取，直到最终展示或存储时才格式化。

### 精度处理特点：

1. **内部计算精度**：
   - 使用 `shopspring/decimal` 库进行高精度计算
   - 内部计算保留完整精度（如 2407.11471）
   - 避免浮点数精度问题

2. **显示格式化**：
   ```go
   // 根据代币类型确定显示精度
   var decimals int32
   switch product.BaseToken {
   case "ETH", "BTC":
       decimals = 2  // ETH、BTC 显示 2 位小数
   case "TRX":
       decimals = 4  // TRX 显示 4 位小数
   default:
       decimals = 4  // 默认显示 4 位小数
   }
   ```

3. **订单金额处理**：
   ```go
   // 金额截取函数（注意：是截取而非四舍五入）
   func truncateAmountByDecimals(amount decimal.Decimal, decimals uint) decimal.Decimal {
       truncated := amount.StringFixed(int32(decimals))
       result, _ := decimal.NewFromString(truncated)
       return result
   }
   ```

### 关键点：
- 报价计算使用完整精度
- 订单执行时根据代币的 decimals 进行截取（truncate）
- 不使用四舍五入，避免精度累积误差

## 3. 兑换成功页面的汇率来源

**答案**：兑换成功页面的汇率（如 2405.87347）是**订单执行时的实际价格**，可能与报价时略有差异。

### 汇率变化流程：

1. **报价生成时**：
   ```go
   quote := &interfaces.SwapQuote{
       Price: price,  // 报价时的价格，如 2407.11471
       ExpiresAt: time.Now().Add(120 * time.Second),
   }
   ```

2. **订单执行时**：
   ```go
   // 重新获取当前价格
   currentPrice := getCurrentPrice()  // 如 2405.87347
   
   // 计算滑点
   priceSlippage := currentPrice.Sub(order.Price).Div(order.Price).Abs()
   
   // 如果滑点在允许范围内，使用新价格
   if priceSlippage.LessThan(maxSlippage) {
       // 更新实际成交价格
       tx.Model(&entity.ExchangeOrders{}).
           Where("order_sn = ?", orderID).
           Data(g.Map{
               "actual_price": currentPrice,  // 2405.87347
           }).Update()
   }
   ```

3. **成功页面显示**：
   - 显示的是 `actual_price`（实际成交价）
   - 这解释了为什么与报价时的价格略有差异

## 4. 价差率配置

**答案**：价差率对应数据库表 `exchange_products` 的 `spread_rate` 字段，**不是** `swap.risk_price_deviation_threshold`。

### 配置位置和说明：

```sql
-- exchange_products 表
spread_rate decimal(10,6)  -- 价差率，如 0.002000 表示 0.2%
```

### 特点：
- 每个交易对可以设置不同的价差率
- 价差率直接影响买卖价格
- 买入时加价差，卖出时减价差
- 不是风险偏差阈值（risk_price_deviation_threshold）

### 代码引用：
```go
// internal/service/v2/impl/swap_service.go:301-308
spreadRate := product.SpreadRate
if tradeType == "buy" {
    price = price.Mul(decimal.NewFromInt(1).Add(spreadRate))
} else {
    price = price.Mul(decimal.NewFromInt(1).Sub(spreadRate))
}
```

## 5. 手续费率配置

**答案**：手续费率对应数据库表 `exchange_products` 的 `output_fee_rate` 字段，**不是** `swap.fees_default_rate`。

### 配置说明：

```sql
-- exchange_products 表
output_fee_rate decimal(10,6)      -- 输出代币手续费率，如 0.002000 表示 0.2%
min_output_fee_amount decimal(36,18) -- 最小手续费金额
```

### 手续费策略特点：

1. **输出代币扣费策略**：
   ```go
   // internal/service/v2/impl/fee_calculator.go:79-97
   if req.TradeType == "buy" {
       // 买入：从基础代币（用户收到的）扣费
       outputAmount = req.BaseAmount
       feeAmount = outputAmount.Mul(c.product.OutputFeeRate)
       outputAfterFee = outputAmount.Sub(feeAmount)
   } else {
       // 卖出：从报价代币（用户收到的）扣费
       outputAmount = req.QuoteAmount
       feeAmount = outputAmount.Mul(c.product.OutputFeeRate)
       outputAfterFee = outputAmount.Sub(feeAmount)
   }
   ```

2. **标准费率**：0.002（0.2%）
3. **支持最小手续费限制**
4. **手续费总是从用户收到的代币中扣除**

## 6. 滑点参数配置

**答案**：系统使用的滑点参数在配置文件 `swap_config.yaml` 中的 `max_slippage` 字段。

### 配置位置：

```yaml
# config/swap_config.yaml
swap:
  price:
    max_slippage: 0.01  # 最大允许滑点（1%）
```

### 滑点保护机制：

```go
// internal/service/v2/impl/swap_service.go:975-984
// 获取最大允许滑点（默认1%）
maxSlippage := decimal.NewFromFloat(0.01)
if s.configMgr != nil {
    config, err := s.configMgr.GetConfig(ctx)
    if err == nil && config.MaxSlippage.GreaterThan(decimal.Zero) {
        maxSlippage = config.MaxSlippage
    }
}

// 计算实际滑点
priceSlippage := currentPrice.Sub(order.Price).Div(order.Price).Abs()

// 检查是否超过最大滑点
if priceSlippage.GreaterThan(maxSlippage) {
    // 订单失败，价格变动过大
    return nil, fmt.Errorf("price slippage too high: %s%%", 
                          priceSlippage.Mul(decimal.NewFromInt(100)).String())
}
```

### 滑点保护的作用：
- 默认最大滑点：1%
- 超过滑点限制时订单自动失败

### 价格体系
1. **市场价格 → 应用价差 → 用户看到的价格**
   - 透明的价格计算机制
   - 买卖价差为平台提供合理收益

2. **精确计算，延迟格式化**
   - 使用 decimal 类型确保计算精度
   - 只在最终显示或存储时进行格式化

3. **实时价格更新**
   - 订单执行时使用最新价格
   - 滑点保护防止价格剧烈变动

### 配置管理
1. **价差率**：`exchange_products.spread_rate`
2. **手续费率**：`exchange_products.output_fee_rate`
3. **滑点限制**：`swap_config.yaml` 中的 `max_slippage`

