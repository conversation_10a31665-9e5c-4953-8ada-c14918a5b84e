# Price Service Integration for Swap Feature

> **Note**: The price service has already been implemented in the system at `/internal/service/price_client.go`. This document describes how the swap feature should integrate with the existing price service.

## 1. Overview

The swap feature relies on real-time price data from the existing Price Client Service. This document describes how the swap feature integrates with the price service to ensure accurate and timely exchange rates.

## 2. Architecture

### 2.1 Component Interaction

```
┌─────────────────────────┐     ┌──────────────────────┐
│   Price Monitor Service │     │      Swap Service    │
│  (Independent Process)  │     │                      │
│                         │     │  ┌────────────────┐  │
│  ┌─────────────────┐   │     │  │  Price Client  │  │
│  │ Binance WS API  │   │     │  │                │  │
│  └────────┬────────┘   │     │  └───────┬────────┘  │
│           │            │     │          │           │
│  ┌────────▼────────┐   │     │          │           │
│  │ Price Processor │   │     │          │           │
│  └────────┬────────┘   │     │          │           │
│           │            │     │          │           │
│  ┌────────▼────────┐   │     │          │           │
│  │  Redis Writer   │   │     │          │           │
│  └────────┬────────┘   │     │          │           │
└───────────┼─────────────┘     └──────────┼───────────┘
            │                              │
            │      ┌─────────────┐         │
            └─────►│    Redis    │◄────────┘
                   │   Cluster   │
                   └─────────────┘
```

### 2.2 Data Flow

1. **Price Monitor Service** continuously:
   - Connects to exchange WebSocket APIs
   - Processes real-time price updates
   - Validates and stores data in Redis

2. **Swap Service** when needed:
   - Uses Price Client to fetch prices from Redis
   - Validates data freshness
   - Applies business logic (fees, spreads)

## 3. Price Client Implementation

### 3.1 Interface Definition

```go
// IPriceClient defines the interface for accessing price data
type IPriceClient interface {
    // GetRealTimePrice fetches the current price for a symbol
    GetRealTimePrice(ctx context.Context, symbol string) (*PriceData, error)
    
    // GetMultiplePrices fetches prices for multiple symbols
    GetMultiplePrices(ctx context.Context, symbols []string) (map[string]*PriceData, error)
    
    // CheckPriceFreshness validates if price data is recent enough
    CheckPriceFreshness(ctx context.Context, symbol string, maxStale time.Duration) error
    
    // GetLastUpdateTime returns when the price was last updated
    GetLastUpdateTime(ctx context.Context, symbol string) (time.Time, error)
}
```

### 3.2 Price Data Structure

```go
type PriceData struct {
    Symbol      string          `json:"symbol"`       // Trading pair (e.g., "ETHUSDT")
    Price       decimal.Decimal `json:"price"`        // Current price
    Volume24h   decimal.Decimal `json:"volume_24h"`   // 24-hour volume
    Change24h   decimal.Decimal `json:"change_24h"`   // 24-hour change percentage
    High24h     decimal.Decimal `json:"high_24h"`     // 24-hour high
    Low24h      decimal.Decimal `json:"low_24h"`      // 24-hour low
    Provider    string          `json:"provider"`     // Price source (e.g., "binance")
    Timestamp   time.Time       `json:"timestamp"`    // Data timestamp
    LastUpdated int64           `json:"last_updated"` // Unix timestamp
}
```

## 4. Redis Data Schema

### 4.1 Key Structure

```
# Current price data
price:realtime:{symbol} → JSON PriceData

# Price history (optional)
price:history:{symbol}:{timestamp} → JSON PriceData

# Last update time
price:lastupdate:{symbol} → Unix timestamp

# Health status
price:health:{provider} → JSON HealthStatus
```

### 4.2 Example Redis Data

```json
// Key: price:realtime:ETHUSDT
{
  "symbol": "ETHUSDT",
  "price": "2453.50",
  "volume_24h": "1234567.89",
  "change_24h": "2.35",
  "high_24h": "2498.00",
  "low_24h": "2401.20",
  "provider": "binance",
  "timestamp": "2024-01-09T12:34:56Z",
  "last_updated": **********
}
```

## 5. Integration Best Practices

### 5.1 Price Freshness Validation

```go
func (s *SwapService) validatePriceFreshness(ctx context.Context, symbol string) error {
    const maxStaleTime = 45 * time.Second
    
    lastUpdate, err := s.priceClient.GetLastUpdateTime(ctx, symbol)
    if err != nil {
        return fmt.Errorf("failed to get last update time: %w", err)
    }
    
    age := time.Since(lastUpdate)
    if age > maxStaleTime {
        return &PriceStaleError{
            Symbol:      symbol,
            LastUpdate:  lastUpdate,
            Age:         age,
            MaxAllowed:  maxStaleTime,
        }
    }
    
    return nil
}
```

### 5.2 Price Fetching with Retry

```go
func (s *SwapService) fetchPriceWithRetry(ctx context.Context, symbol string) (*PriceData, error) {
    var lastErr error
    
    for i := 0; i < 3; i++ {
        // Check freshness first
        if err := s.validatePriceFreshness(ctx, symbol); err != nil {
            lastErr = err
            time.Sleep(time.Second * time.Duration(i+1))
            continue
        }
        
        // Fetch price
        price, err := s.priceClient.GetRealTimePrice(ctx, symbol)
        if err != nil {
            lastErr = err
            time.Sleep(time.Second * time.Duration(i+1))
            continue
        }
        
        return price, nil
    }
    
    return nil, fmt.Errorf("failed after 3 retries: %w", lastErr)
}
```

## 6. Configuration

### 6.1 Price Client Configuration

```yaml
price_client:
  redis:
    addresses: ["localhost:6379"]
    password: ""
    db: 0
    pool_size: 10
    dial_timeout: "5s"
    read_timeout: "3s"
    write_timeout: "3s"
  
  staleness:
    max_allowed_stale: "45s"      # Maximum allowed age for price data
    warning_threshold: "30s"      # Warning threshold for monitoring
    check_interval: "5s"          # How often to check freshness
  
  retry:
    max_attempts: 3               # Maximum retry attempts
    retry_delay: "1s"             # Initial retry delay
    backoff_multiplier: 2.0       # Exponential backoff multiplier
  
  monitoring:
    enable_metrics: true          # Enable Prometheus metrics
    log_slow_queries: true        # Log slow Redis queries
    slow_query_threshold: "100ms" # Threshold for slow query logging
```

### 6.2 Environment Variables

```bash
# Redis connection
PRICE_REDIS_ADDR=localhost:6379
PRICE_REDIS_PASSWORD=
PRICE_REDIS_DB=0

# Staleness settings
PRICE_MAX_STALE_SECONDS=45
PRICE_WARNING_STALE_SECONDS=30

# Monitoring
PRICE_METRICS_ENABLED=true
```

## 7. Error Handling

### 7.1 Error Types

```go
// PriceStaleError indicates price data is too old
type PriceStaleError struct {
    Symbol     string
    LastUpdate time.Time
    Age        time.Duration
    MaxAllowed time.Duration
}

// PriceNotFoundError indicates price data is not available
type PriceNotFoundError struct {
    Symbol string
}

// PriceServiceError indicates a service-level error
type PriceServiceError struct {
    Provider string
    Reason   string
}
```

### 7.2 Error Handling Strategy

1. **Stale Data**: Reject swap with clear error message
2. **Missing Price**: Try fallback providers or reject
3. **Service Error**: Retry with exponential backoff
4. **Network Error**: Circuit breaker pattern

## 8. Monitoring and Alerting

### 8.1 Key Metrics

```go
// Prometheus metrics
var (
    priceDataAge = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_price_data_age_seconds",
            Help: "Age of price data in seconds",
        },
        []string{"symbol"},
    )
    
    priceFetchErrors = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "swap_price_fetch_errors_total",
            Help: "Total number of price fetch errors",
        },
        []string{"symbol", "error_type"},
    )
    
    priceStaleRejections = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "swap_price_stale_rejections_total",
            Help: "Total swaps rejected due to stale prices",
        },
        []string{"symbol"},
    )
)
```

### 8.2 Alert Rules

```yaml
# Prometheus alert rules
groups:
- name: swap_price_alerts
  rules:
  - alert: PriceDataStale
    expr: swap_price_data_age_seconds > 60
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Price data is stale for {{ $labels.symbol }}"
      
  - alert: HighPriceFetchErrorRate
    expr: rate(swap_price_fetch_errors_total[5m]) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High price fetch error rate"
```

## 9. Testing Strategy

### 9.1 Unit Tests

```go
func TestPriceClient_GetRealTimePrice(t *testing.T) {
    // Test successful price fetch
    // Test stale data handling
    // Test missing price handling
    // Test retry logic
}
```

### 9.2 Integration Tests

```go
func TestSwapService_PriceIntegration(t *testing.T) {
    // Test with mock Redis
    // Test staleness validation
    // Test concurrent price fetches
    // Test error scenarios
}
```

## 10. Using the Existing Price Service

### 10.1 Getting Started

The price service is already implemented and available at `internal/service/price_client.go`. To use it in the swap service:

```go
import "telegram-bot-api/internal/service"

// Get the price client instance (singleton)
priceClient := service.PriceClientInstance()

// Get real-time cryptocurrency price
priceData, err := priceClient.GetRealTimePrice(ctx, "ETHUSDT")
if err != nil {
    return fmt.Errorf("failed to get price: %w", err)
}

// Use the price data
currentPrice := priceData.Price
lastUpdate := priceData.Timestamp
```

### 10.2 Available Methods

The existing price client provides these methods:

1. **GetRealTimePrice**: Get single cryptocurrency price with automatic freshness check
2. **GetMultiplePrices**: Get multiple cryptocurrency prices in batch
3. **GetFiatPrice**: Get fiat currency exchange rates (buy/sell prices)
4. **GetMultipleFiatPrices**: Get multiple fiat prices in batch
5. **GetPriceHistory**: Get historical price data for cryptocurrencies
6. **GetFiatPriceHistory**: Get historical fiat price data

### 10.3 Integration Example for Swap Service

```go
// In swap service implementation
type SwapService struct {
    priceClient service.IPriceClient
    // other fields...
}

func NewSwapService() *SwapService {
    return &SwapService{
        priceClient: service.PriceClientInstance(),
        // other initializations...
    }
}

func (s *SwapService) GetQuote(ctx context.Context, req *GetQuoteRequest) (*Quote, error) {
    // Get current price
    priceData, err := s.priceClient.GetRealTimePrice(ctx, req.Symbol)
    if err != nil {
        return nil, fmt.Errorf("failed to get price: %w", err)
    }
    
    // Price is automatically checked for freshness (30s default)
    // Use the price for quote calculation
    basePrice := priceData.Price
    
    // Apply spread, fees, etc.
    // ...
}
```

### 10.4 Configuration

The existing price client uses these default configurations:
- **Redis Config**: Uses the "default" Redis configuration
- **Stale Timeout**: 30 seconds for crypto, 300 seconds for fiat
- **Retry Attempts**: 3 times with 100ms delay

To customize configuration:

```go
config := &service.ClientConfig{
    RedisConfigName:     "default",
    DefaultStaleTimeout: 45 * time.Second,  // Custom timeout
    RetryAttempts:       5,
    RetryDelay:          200 * time.Millisecond,
}
priceClient := service.NewPriceClient(config)
```

## 11. Troubleshooting Guide

### 11.1 Common Issues

1. **"Price data stale" errors**
   - Check Price Monitor Service health
   - Verify Redis connectivity
   - Check WebSocket connections to exchanges

2. **Inconsistent prices**
   - Verify time synchronization
   - Check for network latency
   - Review price source configuration

3. **High latency**
   - Monitor Redis performance
   - Check network connectivity
   - Review connection pool settings

### 11.2 Debug Commands

```bash
# Check price in Redis
redis-cli GET price:realtime:ETHUSDT

# Check last update time
redis-cli GET price:lastupdate:ETHUSDT

# Monitor price updates
redis-cli MONITOR | grep price:realtime

# Check Price Monitor Service logs
tail -f /var/log/price-monitor/app.log
```