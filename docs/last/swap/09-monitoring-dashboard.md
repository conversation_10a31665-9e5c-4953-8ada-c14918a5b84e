# Swap Feature Monitoring Dashboard

## Overview
The swap monitoring dashboard provides comprehensive real-time and historical insights into swap operations, system health, and user behavior through internal metrics collection.

## Dashboard Components

### 1. Real-time Metrics Panel
```
┌─────────────────────────────────────────────────────────┐
│                   Real-time Overview                     │
├─────────────────┬────────────────┬────────────────────┤
│ Quotes/Min      │ Orders/Min     │ Success Rate       │
│ 45.2            │ 12.3           │ 98.5%              │
├─────────────────┼────────────────┼────────────────────┤
│ Active Quotes   │ Pending Orders │ Volume Today       │
│ 127             │ 3              │ $1,234,567         │
├─────────────────┼────────────────┼────────────────────┤
│ Avg Processing  │ Error Rate     │ Security Events    │
│ 245ms           │ 0.8%           │ 2                  │
└─────────────────┴────────────────┴────────────────────┘
```

### 2. System Health Monitor
```
┌─────────────────────────────────────────────────────────┐
│                    System Health                         │
├─────────────────┬────────────────┬────────────────────┤
│ Service         │ Status         │ Latency            │
├─────────────────┼────────────────┼────────────────────┤
│ Swap Service    │ ✓ Healthy      │ -                  │
│ Price Service   │ ✓ Healthy      │ 12ms               │
│ Database        │ ✓ Healthy      │ 3ms                │
│ Redis           │ ✓ Healthy      │ 1ms                │
└─────────────────┴────────────────┴────────────────────┘
```

### 3. Product Performance Grid
```
┌─────────────────────────────────────────────────────────┐
│                 Product Performance                      │
├────────────┬─────────┬─────────┬──────────┬───────────┤
│ Product    │ Orders  │ Volume  │ Success  │ Avg Size  │
├────────────┼─────────┼─────────┼──────────┼───────────┤
│ ETH/USDT   │ 1,234   │ $456K   │ 99.1%    │ $370      │
│ BTC/USDT   │ 567     │ $789K   │ 98.5%    │ $1,392    │
│ USDT/TON   │ 890     │ $234K   │ 97.8%    │ $263      │
└────────────┴─────────┴─────────┴──────────┴───────────┘
```

### 4. Security & Risk Panel
```
┌─────────────────────────────────────────────────────────┐
│                  Security Overview                       │
├─────────────────┬────────────────┬────────────────────┤
│ Blocked Today   │ High Risk Users│ Anomalies          │
│ 15              │ 3              │ 7                  │
├─────────────────┴────────────────┴────────────────────┤
│ Recent Security Events:                                  │
│ • [14:32] User 12345 blocked - velocity check failed   │
│ • [14:15] Anomaly detected - unusual pattern from IP   │
│ • [13:58] High risk score (85) for order #SWAP2024... │
└─────────────────────────────────────────────────────────┘
```

## Internal Metrics Collection

### Metrics Service Usage
```go
// Get real-time metrics
metrics := service.SwapMetrics()
realtimeData, err := metrics.GetRealtimeMetrics(ctx)

// Get historical metrics
startTime := time.Now().AddDate(0, 0, -7) // 7 days ago
endTime := time.Now()
historicalData, err := metrics.GetHistoricalMetrics(ctx, startTime, endTime)

// Get product metrics
productMetrics, err := metrics.GetProductMetrics(ctx, productID, 24*time.Hour)

// Get user metrics
userMetrics, err := metrics.GetUserMetrics(ctx, userID, 30*24*time.Hour)

## Monitoring Best Practices

### 1. Alert Thresholds
- **Success Rate**: Alert if drops below 95%
- **Error Rate**: Alert if exceeds 5%
- **Processing Time**: Alert if p95 > 1000ms
- **Service Health**: Alert on any service down
- **Security Events**: Alert on critical severity

### 2. Dashboard Refresh Rates
- Real-time metrics: 5 seconds
- Product performance: 1 minute
- Historical data: 5 minutes
- Health status: 10 seconds

### 3. Key Performance Indicators (KPIs)
1. **Operational KPIs**
   - Orders per minute
   - Success rate
   - Average processing time
   - System uptime

2. **Business KPIs**
   - Daily volume (USD)
   - Fee revenue
   - Unique active users
   - User retention rate

3. **Security KPIs**
   - Security events per hour
   - Blocked transaction rate
   - Average risk score
   - Anomaly detection rate

## Grafana Integration

### Sample Grafana Dashboard JSON
```json
{
  "dashboard": {
    "title": "Swap Service Monitoring",
    "panels": [
      {
        "title": "Orders Per Minute",
        "targets": [
          {
            "expr": "swap_orders_per_minute",
            "refId": "A"
          }
        ],
        "type": "graph"
      },
      {
        "title": "Success Rate",
        "targets": [
          {
            "expr": "swap_success_rate",
            "refId": "B"
          }
        ],
        "type": "gauge"
      }
    ]
  }
}
```

## Prometheus Metrics

### Metric Examples
```
# Order metrics
swap_orders_total{status="completed"} 14987
swap_orders_total{status="failed"} 247
swap_order_processing_time_seconds{quantile="0.5"} 0.234
swap_order_processing_time_seconds{quantile="0.95"} 0.456
swap_order_processing_time_seconds{quantile="0.99"} 0.789

# Volume metrics
swap_volume_usd_total{product="ETH/USDT"} 4567890.12
swap_fees_usd_total{product="ETH/USDT"} 9135.78

# System metrics
swap_service_up 1
price_service_latency_seconds 0.012
database_connections_active 45
redis_memory_bytes 123456789
```

## Troubleshooting Guide

### Common Issues

1. **High Error Rate**
   - Check price service connectivity
   - Verify database performance
   - Review recent deployments

2. **Low Success Rate**
   - Analyze failure reasons
   - Check slippage settings
   - Review security thresholds

3. **Performance Degradation**
   - Monitor database query times
   - Check Redis memory usage
   - Review concurrent request limits

### Debug Queries
```sql
-- Find failed orders in last hour
SELECT order_sn, failed_reason, created_at
FROM exchange_orders
WHERE status = 'failed'
  AND created_at > NOW() - INTERVAL 1 HOUR
ORDER BY created_at DESC;

-- Check product performance
SELECT 
  p.symbol,
  COUNT(*) as total_orders,
  SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as successful,
  AVG(o.processing_time) as avg_processing_ms
FROM exchange_orders o
JOIN exchange_products p ON o.product_id = p.product_id
WHERE o.created_at > NOW() - INTERVAL 1 DAY
GROUP BY p.product_id;

-- Security events analysis
SELECT 
  event_type,
  severity,
  COUNT(*) as event_count
FROM swap_security_events
WHERE timestamp > NOW() - INTERVAL 1 HOUR
GROUP BY event_type, severity
ORDER BY event_count DESC;
```

## Maintenance Operations

### Daily Tasks
1. Review security events
2. Check failed order reasons
3. Monitor volume trends
4. Verify system health

### Weekly Tasks
1. Generate performance report
2. Review user metrics
3. Analyze product performance
4. Update alert thresholds

### Monthly Tasks
1. Comprehensive metrics analysis
2. Capacity planning review
3. Security audit
4. Performance optimization