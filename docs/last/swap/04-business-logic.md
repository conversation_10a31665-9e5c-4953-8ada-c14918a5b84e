# Business Logic for Swap Feature

## 1. Overview

This document details the core business logic and rules for the swap feature, including transaction flow, validation rules, fee calculations, and error handling.

## 2. Core Business Rules

### 2.1 Trading Rules

1. **Supported Pairs**: Only configured and active trading pairs in `exchange_products`
2. **Direction Control**: Each pair can be configured for buy-only, sell-only, or both
3. **Amount Limits**: Enforce min/max per transaction and daily volume limits
4. **Price Validity**: Quotes expire after configured time (default 30 seconds)
5. **Slippage Protection**: Maximum allowed price deviation between quote and execution

### 2.2 Fee Structure

```go
// Fee calculation logic
type FeeCalculator struct {
    FeeRate      decimal.Decimal // e.g., 0.002 for 0.2%
    FeeChargedIn string          // "base" or "quote"
    MinFeeAmount decimal.Decimal // Minimum fee in base token equivalent
}

func (f *FeeCalculator) CalculateFee(baseAmount, quoteAmount decimal.Decimal) FeeResult {
    var feeAmount decimal.Decimal
    var feeTokenType string
    
    if f.FeeChargedIn == "base" {
        feeAmount = baseAmount.Mul(f.FeeRate)
        feeTokenType = "base"
    } else {
        feeAmount = quoteAmount.Mul(f.FeeRate)
        feeTokenType = "quote"
    }
    
    // Apply minimum fee if configured
    if f.MinFeeAmount.GreaterThan(decimal.Zero) {
        minFeeInCharged := f.convertToChargedToken(f.MinFeeAmount)
        if feeAmount.LessThan(minFeeInCharged) {
            feeAmount = minFeeInCharged
        }
    }
    
    return FeeResult{
        Amount:    feeAmount,
        TokenType: feeTokenType,
        Rate:      f.FeeRate,
    }
}
```

## 3. Transaction Flow

### 3.1 Quote Generation Flow

```mermaid
flowchart TD
    A[User requests quote] --> B{Validate trading pair}
    B -->|Invalid| C[Return error]
    B -->|Valid| D[Check product status]
    D -->|Inactive| C
    D -->|Active| E[Validate amount limits]
    E -->|Outside limits| C
    E -->|Within limits| F[Fetch current price]
    F -->|Price stale| C
    F -->|Price fresh| G[Calculate exchange amount]
    G --> H[Apply spread rate]
    H --> I[Calculate fees]
    I --> J[Generate quote with expiry]
    J --> K[Store quote in cache]
    K --> L[Return quote to user]
```

### 3.2 Swap Execution Flow

```mermaid
flowchart TD
    A[User confirms swap] --> B{Validate quote}
    B -->|Expired| C[Return quote expired error]
    B -->|Valid| D[Verify payment password]
    D -->|Failed| E[Return auth error]
    D -->|Success| F[Lock user funds]
    F --> G[Fetch latest price]
    G -->|Price stale| H[Unlock funds & return error]
    G -->|Price fresh| I{Check slippage}
    I -->|Exceeds limit| H
    I -->|Within limit| J[Begin transaction]
    J --> K[Deduct from balance]
    K --> L[Add to balance]
    L --> M[Deduct fees]
    M --> N[Record order]
    N --> O[Commit transaction]
    O -->|Failed| P[Rollback & unlock funds]
    O -->|Success| Q[Send success notification]
```

## 4. Validation Rules

### 4.1 Input Validation

```go
type SwapValidator struct {
    productConfig *ExchangeProduct
}

func (v *SwapValidator) ValidateSwapRequest(req *SwapRequest) error {
    // 1. Validate amount format
    if req.Amount.IsNegative() || req.Amount.IsZero() {
        return ErrInvalidAmount
    }
    
    // 2. Check minimum amount
    if req.Amount.LessThan(v.productConfig.MinBaseAmountPerTx) {
        return ErrAmountTooSmall
    }
    
    // 3. Check maximum amount
    if v.productConfig.MaxBaseAmountPerTx != nil && 
       req.Amount.GreaterThan(*v.productConfig.MaxBaseAmountPerTx) {
        return ErrAmountTooLarge
    }
    
    // 4. Validate trading direction
    if req.TradeType == "buy" && !v.productConfig.AllowBuy {
        return ErrBuyNotAllowed
    }
    if req.TradeType == "sell" && !v.productConfig.AllowSell {
        return ErrSellNotAllowed
    }
    
    // 5. Check user balance
    balance := getUserBalance(req.UserID, req.FromTokenID)
    totalRequired := req.Amount
    if v.productConfig.FeeChargedIn == "base" {
        fee := calculateFee(req.Amount, v.productConfig.FeeRate)
        totalRequired = totalRequired.Add(fee)
    }
    if balance.LessThan(totalRequired) {
        return ErrInsufficientBalance
    }
    
    return nil
}
```

### 4.2 Price Validation

```go
func ValidatePriceSlippage(quotedPrice, currentPrice, maxSlippage decimal.Decimal) error {
    // Calculate actual slippage
    priceDiff := currentPrice.Sub(quotedPrice).Abs()
    slippage := priceDiff.Div(quotedPrice)
    
    // Check against maximum allowed
    if slippage.GreaterThan(maxSlippage) {
        return &SlippageError{
            QuotedPrice:  quotedPrice,
            CurrentPrice: currentPrice,
            Slippage:     slippage,
            MaxAllowed:   maxSlippage,
        }
    }
    
    return nil
}
```

## 5. Exchange Rate Calculation

### 5.1 Rate Calculation with Spread

```go
func CalculateExchangeRate(marketPrice, spreadRate decimal.Decimal, tradeType string) decimal.Decimal {
    if tradeType == "sell" {
        // User selling: reduce the price they get
        return marketPrice.Mul(decimal.NewFromFloat(1).Sub(spreadRate))
    } else {
        // User buying: increase the price they pay
        return marketPrice.Mul(decimal.NewFromFloat(1).Add(spreadRate))
    }
}
```

### 5.2 Amount Calculation

```go
func CalculateSwapAmounts(req SwapRequest, rate decimal.Decimal) SwapAmounts {
    var result SwapAmounts
    
    if req.FixedSide == "base" {
        // User specified base amount
        result.BaseAmount = req.Amount
        result.QuoteAmount = req.Amount.Mul(rate)
    } else {
        // User specified quote amount
        result.QuoteAmount = req.Amount
        result.BaseAmount = req.Amount.Div(rate)
    }
    
    return result
}
```

## 6. Daily Volume Limits

### 6.1 Volume Tracking

```go
func CheckDailyVolumeLimit(productID uint, baseAmount decimal.Decimal) error {
    // Get product configuration
    product := getProductConfig(productID)
    if product.DailyBaseVolumeLimit == nil {
        return nil // No limit configured
    }
    
    // Calculate current daily volume
    startOfDay := time.Now().Truncate(24 * time.Hour)
    currentVolume := getDailyVolume(productID, startOfDay)
    
    // Check if new transaction would exceed limit
    newVolume := currentVolume.Add(baseAmount)
    if newVolume.GreaterThan(*product.DailyBaseVolumeLimit) {
        return &VolumeLimitError{
            CurrentVolume: currentVolume,
            Requested:     baseAmount,
            DailyLimit:    *product.DailyBaseVolumeLimit,
        }
    }
    
    return nil
}
```

## 7. Order Processing

### 7.1 Order Creation

```go
func CreateSwapOrder(ctx context.Context, req SwapRequest, quote SwapQuote) (*ExchangeOrder, error) {
    order := &ExchangeOrder{
        OrderSN:      generateOrderSN(),
        UserID:       req.UserID,
        ProductID:    req.ProductID,
        BaseTokenID:  req.BaseTokenID,
        QuoteTokenID: req.QuoteTokenID,
        Symbol:       req.Symbol,
        TradeType:    req.TradeType,
        AmountBase:   quote.BaseAmount,
        AmountQuote:  quote.QuoteAmount,
        Price:        quote.Rate,
        FeeAmount:    quote.FeeAmount,
        FeeTokenID:   quote.FeeTokenID,
        FeeRate:      quote.FeeRate,
        Status:       "pending",
        CreatedAt:    time.Now(),
    }
    
    // Save to database
    if err := saveOrder(ctx, order); err != nil {
        return nil, err
    }
    
    return order, nil
}
```

### 7.2 Order Execution

```go
func ExecuteSwapOrder(ctx context.Context, orderID uint64) error {
    // Start database transaction
    tx := db.BeginTransaction()
    defer tx.Rollback()
    
    // Update order status
    order := getOrder(orderID)
    order.Status = "processing"
    updateOrder(tx, order)
    
    // Execute wallet operations
    if order.TradeType == "sell" {
        // Deduct base token
        deductBalance(tx, order.UserID, order.BaseTokenID, order.AmountBase)
        // Add quote token (minus fee if applicable)
        netAmount := calculateNetAmount(order)
        addBalance(tx, order.UserID, order.QuoteTokenID, netAmount)
    } else {
        // Deduct quote token
        deductBalance(tx, order.UserID, order.QuoteTokenID, order.AmountQuote)
        // Add base token (minus fee if applicable)
        netAmount := calculateNetAmount(order)
        addBalance(tx, order.UserID, order.BaseTokenID, netAmount)
    }
    
    // Update order status
    order.Status = "completed"
    updateOrder(tx, order)
    
    // Commit transaction
    return tx.Commit()
}
```

## 8. Error Handling

### 8.1 Business Error Types

```go
var (
    ErrInvalidAmount        = errors.New("invalid amount")
    ErrAmountTooSmall       = errors.New("amount below minimum")
    ErrAmountTooLarge       = errors.New("amount exceeds maximum")
    ErrInsufficientBalance  = errors.New("insufficient balance")
    ErrQuoteExpired         = errors.New("quote has expired")
    ErrPriceStale           = errors.New("price data is stale")
    ErrSlippageExceeded     = errors.New("price slippage exceeded")
    ErrVolumeLimitExceeded  = errors.New("daily volume limit exceeded")
    ErrProductInactive      = errors.New("product is not active")
    ErrBuyNotAllowed        = errors.New("buy direction not allowed")
    ErrSellNotAllowed       = errors.New("sell direction not allowed")
)
```

### 8.2 Error Response Format

```go
type SwapError struct {
    Code    string                 `json:"code"`
    Message string                 `json:"message"`
    Details map[string]interface{} `json:"details,omitempty"`
}

func NewSwapError(err error, details ...map[string]interface{}) *SwapError {
    code := getErrorCode(err)
    message := getLocalizedMessage(err)
    
    swapErr := &SwapError{
        Code:    code,
        Message: message,
    }
    
    if len(details) > 0 {
        swapErr.Details = details[0]
    }
    
    return swapErr
}
```

## 9. State Management

### 9.1 Quote State

```go
type QuoteState struct {
    QuoteID      string
    UserID       uint64
    ProductID    uint
    BaseAmount   decimal.Decimal
    QuoteAmount  decimal.Decimal
    Rate         decimal.Decimal
    FeeAmount    decimal.Decimal
    CreatedAt    time.Time
    ExpiresAt    time.Time
    Status       string // "active", "used", "expired"
}

// Store quote in Redis with TTL
func StoreQuote(quote *QuoteState) error {
    key := fmt.Sprintf("swap:quote:%s", quote.QuoteID)
    data, _ := json.Marshal(quote)
    ttl := time.Until(quote.ExpiresAt)
    return redis.Set(key, data, ttl)
}
```

### 9.2 Order State Transitions

```
States: pending → processing → completed
                            → failed
                → cancelled
```

## 10. Audit and Compliance

### 10.1 Transaction Logging

```go
func LogSwapTransaction(order *ExchangeOrder) {
    log := &SwapAuditLog{
        OrderID:     order.OrderID,
        UserID:      order.UserID,
        Action:      "swap_executed",
        Details:     formatOrderDetails(order),
        IP:          getUserIP(),
        UserAgent:   getUserAgent(),
        Timestamp:   time.Now(),
    }
    
    saveAuditLog(log)
}
```

### 10.2 Compliance Checks

```go
func PerformComplianceChecks(userID uint64, amount decimal.Decimal) error {
    // Check user KYC status
    if !isKYCVerified(userID) && amount.GreaterThan(KYCThreshold) {
        return ErrKYCRequired
    }
    
    // Check for suspicious patterns
    if detectSuspiciousActivity(userID) {
        return ErrSuspiciousActivity
    }
    
    // Check sanctions list
    if isUserSanctioned(userID) {
        return ErrUserSanctioned
    }
    
    return nil
}
```