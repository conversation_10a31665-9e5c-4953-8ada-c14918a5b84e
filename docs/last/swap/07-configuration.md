# Configuration Management for Swap Feature

## 1. Overview

This document details the configuration management for the swap feature, including database configuration, application settings, and runtime parameters.

## 2. Database Configuration

### 2.1 Default Product Configuration

```sql
-- Insert default swap products for common trading pairs
INSERT INTO `exchange_products` (
    `base_token_id`, 
    `quote_token_id`, 
    `symbol`, 
    `product_type`,
    `is_active`, 
    `allow_buy`, 
    `allow_sell`,
    `min_base_amount_per_tx`, 
    `max_base_amount_per_tx`,
    `price_source`,
    `allowed_slippage_percent`,
    `spread_rate`,
    `fee_rate`,
    `fee_charged_in`,
    `display_order`,
    `description`
) VALUES
-- ETH/USDT (both directions)
(2, 1, 'ETH/USDT', 'swap', 1, 1, 1, '0.001', '100', 'binance_feed', 0.005, 0.001, 0.002, 'base', 1, 'Ethereum to Tether swap'),
(1, 2, 'USDT/ETH', 'swap', 1, 1, 1, '10', '100000', 'binance_feed', 0.005, 0.001, 0.002, 'base', 2, 'Tether to Ethereum swap'),

-- BTC/USDT (both directions)
(3, 1, 'BTC/USDT', 'swap', 1, 1, 1, '0.0001', '10', 'binance_feed', 0.005, 0.001, 0.002, 'base', 3, 'Bitcoin to Tether swap'),
(1, 3, 'USDT/BTC', 'swap', 1, 1, 1, '100', '500000', 'binance_feed', 0.005, 0.001, 0.002, 'base', 4, 'Tether to Bitcoin swap'),

-- BNB/USDT (disabled by default)
(4, 1, 'BNB/USDT', 'swap', 1, 0, 0, '0.01', '1000', 'binance_feed', 0.005, 0.001, 0.002, 'base', 5, 'Binance Coin to Tether swap'),
(1, 4, 'USDT/BNB', 'swap', 1, 0, 0, '10', '100000', 'binance_feed', 0.005, 0.001, 0.002, 'base', 6, 'Tether to Binance Coin swap');
```

### 2.2 Product Management Queries

```sql
-- Enable/disable a swap product
UPDATE exchange_products 
SET is_active = 1, allow_buy = 1, allow_sell = 1 
WHERE symbol = 'BNB/USDT';

-- Update fee rate for all products
UPDATE exchange_products 
SET fee_rate = 0.0015 
WHERE product_type = 'swap';

-- Set maintenance mode for a product
UPDATE exchange_products 
SET is_active = 0, 
    maintenance_message = 'ETH/USDT swap is under maintenance until 2024-01-10 12:00 UTC' 
WHERE symbol = 'ETH/USDT';

-- Update trading limits
UPDATE exchange_products 
SET min_base_amount_per_tx = '0.005',
    max_base_amount_per_tx = '50',
    daily_base_volume_limit = '1000'
WHERE symbol = 'ETH/USDT';

-- Change display order
UPDATE exchange_products 
SET display_order = CASE symbol
    WHEN 'BTC/USDT' THEN 1
    WHEN 'USDT/BTC' THEN 2
    WHEN 'ETH/USDT' THEN 3
    WHEN 'USDT/ETH' THEN 4
    ELSE display_order
END
WHERE product_type = 'swap';
```

## 3. Application Configuration

### 3.1 YAML Configuration (config.yaml)

```yaml
# Swap feature configuration
swap:
  # Service settings
  service:
    enabled: true
    max_concurrent_swaps: 100
    
  # Quote settings
  quote:
    default_ttl: "30s"
    max_ttl: "60s"
    cleanup_interval: "5m"
    
  # Price settings
  price:
    max_stale_time: "45s"
    warning_threshold: "30s"
    default_source: "binance_feed"
    fallback_sources: ["kucoin_feed", "okx_feed"]
    
  # Transaction limits
  limits:
    min_amount_usd: 1.0
    max_amount_usd: 50000.0
    daily_volume_usd: 100000.0
    per_user_daily_limit_usd: 10000.0
    
  # Risk control
  risk:
    max_slippage_percent: 0.01  # 1%
    price_deviation_alert: 0.005 # 0.5%
    suspicious_volume_threshold: 100000.0
    
  # Fee configuration
  fees:
    default_rate: 0.002  # 0.2%
    vip_rate: 0.001      # 0.1%
    
  # Redis cache
  cache:
    quote_prefix: "swap:quote:"
    order_prefix: "swap:order:"
    stats_prefix: "swap:stats:"
    user_state_prefix: "swap:state:"
    
  # Monitoring
  monitoring:
    enable_metrics: true
    metrics_interval: "10s"
    alert_webhook: "${SWAP_ALERT_WEBHOOK}"
```

### 3.2 Environment Variables

```bash
# Swap feature environment variables
SWAP_ENABLED=true
SWAP_MAX_CONCURRENT=100

# Price service
SWAP_PRICE_SERVICE_URL=redis://localhost:6379
SWAP_PRICE_MAX_STALE_SECONDS=45
SWAP_PRICE_DEFAULT_SOURCE=binance_feed

# Limits
SWAP_MIN_AMOUNT_USD=1.0
SWAP_MAX_AMOUNT_USD=50000.0
SWAP_DAILY_VOLUME_USD=100000.0

# Fees
SWAP_DEFAULT_FEE_RATE=0.002
SWAP_VIP_FEE_RATE=0.001

# Redis
SWAP_REDIS_URL=redis://localhost:6379
SWAP_REDIS_DB=1

# Monitoring
SWAP_METRICS_ENABLED=true
SWAP_ALERT_WEBHOOK=https://hooks.slack.com/services/xxx
```

## 4. Runtime Configuration

### 4.1 Configuration Service

```go
package config

import (
    "time"
    "github.com/shopspring/decimal"
)

// SwapConfig holds all swap-related configuration
type SwapConfig struct {
    // Service settings
    Enabled             bool          `mapstructure:"enabled"`
    MaxConcurrentSwaps  int           `mapstructure:"max_concurrent_swaps"`
    
    // Quote settings
    QuoteConfig struct {
        DefaultTTL      time.Duration `mapstructure:"default_ttl"`
        MaxTTL          time.Duration `mapstructure:"max_ttl"`
        CleanupInterval time.Duration `mapstructure:"cleanup_interval"`
    } `mapstructure:"quote"`
    
    // Price settings
    PriceConfig struct {
        MaxStaleTime      time.Duration `mapstructure:"max_stale_time"`
        WarningThreshold  time.Duration `mapstructure:"warning_threshold"`
        DefaultSource     string        `mapstructure:"default_source"`
        FallbackSources   []string      `mapstructure:"fallback_sources"`
    } `mapstructure:"price"`
    
    // Limits
    LimitsConfig struct {
        MinAmountUSD           decimal.Decimal `mapstructure:"min_amount_usd"`
        MaxAmountUSD           decimal.Decimal `mapstructure:"max_amount_usd"`
        DailyVolumeUSD         decimal.Decimal `mapstructure:"daily_volume_usd"`
        PerUserDailyLimitUSD   decimal.Decimal `mapstructure:"per_user_daily_limit_usd"`
    } `mapstructure:"limits"`
    
    // Risk control
    RiskConfig struct {
        MaxSlippagePercent         decimal.Decimal `mapstructure:"max_slippage_percent"`
        PriceDeviationAlert        decimal.Decimal `mapstructure:"price_deviation_alert"`
        SuspiciousVolumeThreshold  decimal.Decimal `mapstructure:"suspicious_volume_threshold"`
    } `mapstructure:"risk"`
    
    // Fees
    FeesConfig struct {
        DefaultRate decimal.Decimal `mapstructure:"default_rate"`
        VIPRate     decimal.Decimal `mapstructure:"vip_rate"`
    } `mapstructure:"fees"`
}

// LoadSwapConfig loads swap configuration from various sources
func LoadSwapConfig() (*SwapConfig, error) {
    config := &SwapConfig{}
    
    // Load from YAML file
    if err := viper.UnmarshalKey("swap", config); err != nil {
        return nil, err
    }
    
    // Override with environment variables
    overrideFromEnv(config)
    
    // Validate configuration
    if err := validateSwapConfig(config); err != nil {
        return nil, err
    }
    
    return config, nil
}

// validateSwapConfig validates the swap configuration
func validateSwapConfig(config *SwapConfig) error {
    if !config.Enabled {
        return nil // Skip validation if disabled
    }
    
    if config.MaxConcurrentSwaps <= 0 {
        return errors.New("max_concurrent_swaps must be positive")
    }
    
    if config.QuoteConfig.DefaultTTL <= 0 {
        return errors.New("quote default_ttl must be positive")
    }
    
    if config.LimitsConfig.MinAmountUSD.GreaterThan(config.LimitsConfig.MaxAmountUSD) {
        return errors.New("min_amount_usd cannot be greater than max_amount_usd")
    }
    
    return nil
}
```

### 4.2 Dynamic Configuration Updates

```go
// ConfigManager handles dynamic configuration updates
type ConfigManager struct {
    config      *SwapConfig
    mu          sync.RWMutex
    updateChan  chan ConfigUpdate
    subscribers []ConfigSubscriber
}

// UpdateConfig updates configuration dynamically
func (cm *ConfigManager) UpdateConfig(update ConfigUpdate) error {
    cm.mu.Lock()
    defer cm.mu.Unlock()
    
    // Apply update
    switch update.Key {
    case "swap.fees.default_rate":
        rate, err := decimal.NewFromString(update.Value)
        if err != nil {
            return err
        }
        cm.config.FeesConfig.DefaultRate = rate
        
    case "swap.limits.max_amount_usd":
        amount, err := decimal.NewFromString(update.Value)
        if err != nil {
            return err
        }
        cm.config.LimitsConfig.MaxAmountUSD = amount
        
    // Add more cases as needed
    }
    
    // Notify subscribers
    cm.notifySubscribers(update)
    
    return nil
}

// GetConfig returns current configuration
func (cm *ConfigManager) GetConfig() *SwapConfig {
    cm.mu.RLock()
    defer cm.mu.RUnlock()
    
    // Return a copy to prevent external modifications
    configCopy := *cm.config
    return &configCopy
}
```

## 5. Feature Flags

### 5.1 Feature Flag Configuration

```go
// FeatureFlags for swap functionality
type SwapFeatureFlags struct {
    EnableSwap              bool `json:"enable_swap"`
    EnableBTCSwap           bool `json:"enable_btc_swap"`
    EnableBNBSwap           bool `json:"enable_bnb_swap"`
    EnableAdvancedSlippage  bool `json:"enable_advanced_slippage"`
    EnableVIPFees           bool `json:"enable_vip_fees"`
    EnableBatchSwaps        bool `json:"enable_batch_swaps"`
    EnablePriceAlerts       bool `json:"enable_price_alerts"`
}

// IsFeatureEnabled checks if a feature is enabled
func IsFeatureEnabled(feature string) bool {
    flags := getFeatureFlags()
    
    switch feature {
    case "swap":
        return flags.EnableSwap
    case "swap_btc":
        return flags.EnableSwap && flags.EnableBTCSwap
    case "swap_bnb":
        return flags.EnableSwap && flags.EnableBNBSwap
    case "swap_vip_fees":
        return flags.EnableSwap && flags.EnableVIPFees
    default:
        return false
    }
}
```

### 5.2 A/B Testing Configuration

```go
// ABTestConfig for swap features
type SwapABTestConfig struct {
    Tests []ABTest `json:"tests"`
}

type ABTest struct {
    Name        string   `json:"name"`
    Enabled     bool     `json:"enabled"`
    Variants    []string `json:"variants"`
    Traffic     float64  `json:"traffic"` // Percentage of users
    UserGroups  []string `json:"user_groups"`
}

// Example A/B test configuration
var swapABTests = SwapABTestConfig{
    Tests: []ABTest{
        {
            Name:     "new_swap_ui",
            Enabled:  true,
            Variants: []string{"control", "variant_a", "variant_b"},
            Traffic:  0.1, // 10% of users
        },
        {
            Name:     "dynamic_fees",
            Enabled:  false,
            Variants: []string{"fixed", "dynamic"},
            Traffic:  0.05, // 5% of users
        },
    },
}
```

## 6. Configuration Management API

### 6.1 Admin API Endpoints

```go
// GET /admin/api/swap/config
func GetSwapConfig(c *gin.Context) {
    config := configManager.GetConfig()
    c.JSON(200, config)
}

// PUT /admin/api/swap/config
func UpdateSwapConfig(c *gin.Context) {
    var update ConfigUpdate
    if err := c.ShouldBindJSON(&update); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    if err := configManager.UpdateConfig(update); err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, gin.H{"message": "Configuration updated"})
}

// GET /admin/api/swap/products
func GetSwapProducts(c *gin.Context) {
    products, err := swapService.GetAllProducts(c.Request.Context())
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, products)
}

// PUT /admin/api/swap/products/{id}
func UpdateSwapProduct(c *gin.Context) {
    productID := c.Param("id")
    
    var update ProductUpdate
    if err := c.ShouldBindJSON(&update); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    if err := swapService.UpdateProduct(c.Request.Context(), productID, update); err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, gin.H{"message": "Product updated"})
}
```

## 7. Configuration Validation

### 7.1 Startup Validation

```go
// ValidateSwapConfiguration performs comprehensive validation at startup
func ValidateSwapConfiguration() error {
    // Load configuration
    config, err := LoadSwapConfig()
    if err != nil {
        return fmt.Errorf("failed to load swap config: %w", err)
    }
    
    // Check database configuration
    if err := validateDatabaseConfig(); err != nil {
        return fmt.Errorf("database validation failed: %w", err)
    }
    
    // Check Redis connectivity
    if err := validateRedisConfig(config); err != nil {
        return fmt.Errorf("redis validation failed: %w", err)
    }
    
    // Check price service
    if err := validatePriceService(config); err != nil {
        return fmt.Errorf("price service validation failed: %w", err)
    }
    
    // Validate products
    if err := validateProducts(); err != nil {
        return fmt.Errorf("product validation failed: %w", err)
    }
    
    log.Info("Swap configuration validation passed")
    return nil
}

// validateProducts checks product configuration consistency
func validateProducts() error {
    products, err := loadProductsFromDB()
    if err != nil {
        return err
    }
    
    if len(products) == 0 {
        return errors.New("no swap products configured")
    }
    
    // Check for duplicate symbols
    symbols := make(map[string]bool)
    for _, product := range products {
        if symbols[product.Symbol] {
            return fmt.Errorf("duplicate symbol found: %s", product.Symbol)
        }
        symbols[product.Symbol] = true
        
        // Validate token IDs exist
        if !tokenExists(product.BaseTokenID) {
            return fmt.Errorf("base token %d not found for product %s", 
                product.BaseTokenID, product.Symbol)
        }
        if !tokenExists(product.QuoteTokenID) {
            return fmt.Errorf("quote token %d not found for product %s", 
                product.QuoteTokenID, product.Symbol)
        }
    }
    
    return nil
}
```

## 8. Configuration Monitoring

### 8.1 Configuration Metrics

```go
var (
    configReloadCounter = prometheus.NewCounter(
        prometheus.CounterOpts{
            Name: "swap_config_reload_total",
            Help: "Total number of configuration reloads",
        },
    )
    
    configErrorCounter = prometheus.NewCounter(
        prometheus.CounterOpts{
            Name: "swap_config_error_total",
            Help: "Total number of configuration errors",
        },
    )
    
    activeProductsGauge = prometheus.NewGauge(
        prometheus.GaugeOpts{
            Name: "swap_active_products",
            Help: "Number of active swap products",
        },
    )
)
```

### 8.2 Configuration Health Check

```go
// HealthCheck verifies configuration health
func (cm *ConfigManager) HealthCheck() error {
    checks := []struct {
        name string
        fn   func() error
    }{
        {"config_loaded", cm.checkConfigLoaded},
        {"redis_connection", cm.checkRedisConnection},
        {"price_service", cm.checkPriceService},
        {"products_valid", cm.checkProductsValid},
    }
    
    for _, check := range checks {
        if err := check.fn(); err != nil {
            return fmt.Errorf("%s check failed: %w", check.name, err)
        }
    }
    
    return nil
}
```