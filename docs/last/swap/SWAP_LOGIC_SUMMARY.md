# 闪兑功能逻辑总结

## 目录
1. [概述](#概述)
2. [系统架构](#系统架构)
3. [核心业务流程](#核心业务流程)
4. [价格机制](#价格机制)
5. [手续费策略](#手续费策略)
6. [订单执行流程](#订单执行流程)
7. [风险控制](#风险控制)
8. [用户交互流程](#用户交互流程)
9. [技术实现细节](#技术实现细节)

## 概述

闪兑（Swap）功能是一个允许用户在 Telegram Bot 内即时兑换不同加密货币的服务。该功能支持多种代币对的即时兑换，包括加密货币之间的兑换以及加密货币与法币（CNY）的兑换。

### 主要特点
- **即时兑换**：实时获取市场价格，快速完成兑换
- **透明定价**：清晰显示兑换率、手续费和价差
- **安全保障**：支付密码验证、余额检查、滑点保护
- **用户友好**：直观的 Telegram Bot 界面，简单的操作流程

## 系统架构

### 分层架构设计

```
┌─────────────────────────────────────────────────────────────────┐
│                         用户界面层                                │
│                    (Telegram Bot Interface)                      │
└───────────────────┬─────────────────────────┬───────────────────┘
                    │                         │
┌───────────────────▼─────────────┐ ┌────────▼──────────────────┐
│       Bot Handler 层             │ │   Callback Handlers       │
│   - 消息处理器                   │ │   - 内联键盘处理          │
│   - 命令处理器                   │ │   - 按钮动作处理          │
└───────────────────┬─────────────┘ └────────┬──────────────────┘
                    │                         │
┌───────────────────▼─────────────────────────▼───────────────────┐
│                        服务层 (Service Layer)                     │
│  ┌─────────────┐  ┌─────────────┐  ┌──────────────────────┐   │
│  │ SwapService │  │PriceClient  │  │  WalletService       │   │
│  │ 闪兑服务    │  │价格客户端   │  │  钱包服务            │   │
│  └─────────────┘  └─────────────┘  └──────────────────────┘   │
└─────────────────────────┬───────────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────────┐
│                      数据访问层 (DAO Layer)                       │
│  ┌─────────────┐  ┌─────────────┐  ┌──────────────────────┐   │
│  │ExchangeDAO  │  │ TokenDAO    │  │   UserDAO            │   │
│  │兑换数据访问 │  │代币数据访问 │  │   用户数据访问       │   │
│  └─────────────┘  └─────────────┘  └──────────────────────┘   │
└─────────────────────────┬───────────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────────┐
│                     外部服务层                                    │
│  ┌─────────────┐  ┌─────────────┐  ┌──────────────────────┐   │
│  │Price Monitor│  │   Redis     │  │    MySQL Database    │   │
│  │价格监控服务 │  │   缓存      │  │    数据库            │   │
│  └─────────────┘  └─────────────┘  └──────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 核心组件说明

1. **SwapService** (`internal/service/v2/impl/swap_service.go`)
   - 闪兑业务逻辑的核心实现
   - 处理报价生成、订单创建和执行
   - 管理交易状态和错误处理

2. **PriceClient** (`internal/service/price_client.go`)
   - 实时价格数据获取
   - 支持加密货币和法币价格
   - 多数据源聚合（Binance、KuCoin等）

3. **WalletService** 
   - 余额查询和更新
   - 资金操作的原子性保证
   - 交易记录管理

## 核心业务流程

### 完整的闪兑交易流程

1. **产品选择阶段**
   ```
   用户点击闪兑 → 显示可用交易对 → 用户选择交易对
   ```

2. **方向选择阶段**
   ```
   显示买入/卖出选项 → 用户选择交易方向 → 确定输入代币
   ```

3. **金额输入阶段**
   ```
   显示余额和限额 → 用户输入金额 → 验证金额有效性
   ```

4. **报价生成阶段**
   ```
   获取实时价格 → 应用价差 → 计算兑换金额 → 计算手续费 → 生成报价
   ```

5. **确认执行阶段**
   ```
   显示报价详情 → 用户确认 → 输入支付密码 → 创建订单 → 执行兑换
   ```

### 报价生成逻辑

```go
// CreateQuote 核心逻辑
func (s *SwapService) CreateQuote(ctx context.Context, req *SwapQuoteRequest) (*SwapQuote, error) {
    // 1. 检查维护模式
    // 2. 确定交易方向和产品
    // 3. 获取实时价格
    // 4. 应用价差（Spread）
    // 5. 计算兑换金额
    // 6. 验证金额限制
    // 7. 计算手续费
    // 8. 生成报价并缓存
}
```

## 价格机制

### 价格获取流程

1. **加密货币价格**
   - 从 PriceMonitor 服务获取实时价格
   - 价格来源：Binance、KuCoin 等交易所
   - 通过 Redis 缓存提高性能

2. **法币价格（CNY）**
   - 使用专门的法币价格接口
   - 支持买入价、卖出价和中间价
   - 自动处理 CNY 基础货币和报价货币的转换

### 价差（Spread）应用

```go
// 价差计算逻辑
if tradeType == "buy" {
    // 用户买入：提高价格
    price = price.Mul(decimal.NewFromInt(1).Add(spreadRate))
} else {
    // 用户卖出：降低价格
    price = price.Mul(decimal.NewFromInt(1).Sub(spreadRate))
}
```

### 价格示例
- ETH/USDT 市场价：2450.00
- 价差率：0.002 (0.2%)
- 买入价格：2450.00 × 1.002 = 2454.90
- 卖出价格：2450.00 × 0.998 = 2445.10

## 手续费策略

### 手续费计算方式

系统采用"输出代币扣费"策略（Output Fee Strategy）：

```go
// NewOutputFeeCalculator 实现
type OutputFeeCalculator struct {
    product *entity.ExchangeProducts
}

func (c *OutputFeeCalculator) CalculateFee(ctx context.Context, req *FeeCalculationRequest) (*FeeResult, error) {
    // 手续费总是从用户收到的代币中扣除
    if req.TradeType == "buy" {
        // 买入：从基础代币（输出）扣费
        outputAmount := req.BaseAmount
        feeAmount := outputAmount.Mul(c.product.OutputFeeRate)
        outputAfterFee := outputAmount.Sub(feeAmount)
    } else {
        // 卖出：从报价代币（输出）扣费
        outputAmount := req.QuoteAmount
        feeAmount := outputAmount.Mul(c.product.OutputFeeRate)
        outputAfterFee := outputAmount.Sub(feeAmount)
    }
}
```

### 手续费特点
- 标准费率：0.2%（可配置）
- 从输出代币扣除，简化用户理解
- 支持最低手续费限制
- 支持用户等级优惠

## 订单执行流程

### 订单状态机

```
pending（待处理） → processing（处理中） → completed（已完成）
                                      → failed（失败）
                 → cancelled（已取消）
```

### 执行步骤详解

1. **前置检查**
   ```go
   // 1. 获取订单并加锁
   // 2. 验证订单状态
   // 3. 重新获取价格并检查滑点
   ```

2. **资金操作**
   ```go
   // 使用分布式锁保证原子性
   lockKey := fmt.Sprintf("swap:order:execute:%s", orderID)
   utils.WithLock(ctx, redis, lockKey, 30*time.Second, func() error {
       // 1. 扣除用户支付代币
       debitResult := wallet.ProcessFundOperationInTx(ctx, tx, debitReq)
       
       // 2. 增加用户收到代币
       creditResult := wallet.ProcessFundOperationInTx(ctx, tx, creditReq)
       
       // 3. 更新订单状态
       // 4. 提交事务
   })
   ```

3. **后置处理**
   - 记录交易指标
   - 发送成功通知
   - 清理缓存数据

## 风险控制

### 1. 交易限额控制

```go
// 单笔交易限制
MinBaseAmountPerTx: 最小交易金额
MaxBaseAmountPerTx: 最大交易金额

// 每日限额
DailyBaseVolumeLimit: 产品每日总量限制

// 用户限额（通过配置管理器）
- 单笔限额
- 每日限额
- 每月限额
```

### 2. 滑点保护

```go
// 滑点检查逻辑
priceSlippage := currentPrice.Sub(quotedPrice).Div(quotedPrice).Abs()
if priceSlippage.GreaterThan(maxSlippage) {
    return SlippageError{
        QuotedPrice:  quotedPrice,
        CurrentPrice: currentPrice,
        Slippage:     slippage,
        MaxAllowed:   maxSlippage,
    }
}
```

默认最大滑点：1%（可配置）

### 3. 余额验证

- 创建报价时预检查余额
- 执行订单时双重验证
- 考虑手续费的完整余额检查

### 4. 报价有效期

- 默认有效期：120秒
- Redis 缓存自动过期
- 执行前验证报价时效性

## 用户交互流程

### Telegram Bot 界面流程

1. **主菜单入口**
   ```
   [💱 闪兑] → 进入闪兑功能
   ```

2. **产品选择界面**
   ```
   选择交易对：
   [ETH/USDT]
   [BTC/USDT]
   [USDT/CNY]
   [返回]
   ```

3. **方向选择界面**
   ```
   ETH/USDT
   当前价格：1 ETH = 2,450.00 USDT
   
   [买入 ETH] [卖出 ETH]
   [返回] [主菜单]
   ```

4. **金额输入界面**
   ```
   请输入您要支付的 USDT 金额：
   
   当前价格：1 ETH = 2,454.90 USDT
   您的余额：10,000.00 USDT
   最小金额：0.001 ETH (约 2.45 USDT)
   最大金额：0.1 ETH (约 245.49 USDT)
   
   [取消]
   ```

5. **报价确认界面**
   ```
   闪兑报价详情：
   
   支付：245.49 USDT
   收到：0.1 ETH
   
   兑换率：1 ETH = 2,454.90 USDT
   手续费：0.0002 ETH
   
   ⚠️ 价格可能会变动，请尽快确认
   
   [确认] [取消]
   ```

6. **密码输入提示**
   ```
   请输入6位支付密码：
   ```

7. **成功结果界面**
   ```
   ✅ 闪兑成功！
   
   支付：245.49 USDT
   收到：0.0998 ETH
   实际汇率：2,459.32
   手续费：0.0002 ETH
   订单号：SWAP20240109123456
   
   [查看详情]
   [再次闪兑] [主菜单]
   ```

### 状态管理

用户状态通过 `UserState` 管理，主要状态包括：

```go
const (
    StateSwapSelectingProduct = "swap_selecting_product"  // 选择产品
    StateSwapInputAmount      = "swap_input_amount"       // 输入金额
    StateSwapConfirmQuote     = "swap_confirm_quote"      // 确认报价
    StateSwapInputPassword    = "swap_input_password"     // 输入密码
)
```

状态上下文存储：
```go
userState.Context = map[string]string{
    "product_id":   "1",
    "direction":    "buy",
    "from_token_id": "1",
    "to_token_id":   "2",
    "quote_id":     "xxx",
    "amount":       "100",
}
```

## 技术实现细节

### 1. 数据库设计

**exchange_products 表**（交易产品配置）
```sql
- product_id: 产品ID
- base_token_id: 基础代币ID
- quote_token_id: 报价代币ID
- symbol: 交易对符号（如 ETH/USDT）
- is_active: 是否激活
- allow_buy/allow_sell: 允许买卖方向
- min_base_amount_per_tx: 最小交易量
- max_base_amount_per_tx: 最大交易量
- spread_rate: 价差率
- fee_rate: 手续费率
- output_fee_rate: 输出手续费率
```

**exchange_orders 表**（兑换订单）
```sql
- order_id: 订单ID
- order_sn: 订单号
- user_id: 用户ID
- product_id: 产品ID
- trade_type: 交易类型（buy/sell）
- amount_base: 基础代币数量
- amount_quote: 报价代币数量
- price: 成交价格
- fee_amount: 手续费金额
- status: 订单状态
- transaction_hash: 交易哈希
```

### 2. 错误处理机制

```go
// 自定义错误类型
type SwapError struct {
    Code    string
    Message string
    Details map[string]interface{}
}

// 错误码定义
const (
    ErrCodeInvalidAmount       = "INVALID_AMOUNT"
    ErrCodeInsufficientBalance = "INSUFFICIENT_BALANCE"
    ErrCodeQuoteExpired        = "QUOTE_EXPIRED"
    ErrCodeSlippageExceeded    = "SLIPPAGE_EXCEEDED"
    ErrCodeServiceUnavailable  = "SERVICE_UNAVAILABLE"
)
```

### 3. 性能优化

1. **缓存策略**
   - 报价缓存：Redis，TTL 与报价有效期一致
   - 价格缓存：Redis，实时更新
   - 产品配置缓存：应用内存缓存

2. **并发控制**
   - 分布式锁防止重复执行
   - 数据库行锁保证数据一致性
   - 批量操作减少数据库交互

3. **查询优化**
   - 合理的索引设计
   - 分页查询历史订单
   - 预加载关联数据

### 4. 监控和指标

```go
// SwapMetricsService 记录的核心指标
- 报价创建数量和延迟
- 订单创建、成功、失败数量
- 交易金额统计（USD）
- 手续费收入统计
- 滑点统计
- 用户活跃度
```

### 5. 国际化支持

所有用户界面文本都支持多语言：
- 中文（zh-CN）
- 英文（en）

通过 `service.I18n()` 服务实现动态语言切换。

## 总结

闪兑功能是一个设计完善、实现严谨的加密货币即时兑换系统。它通过分层架构、实时价格获取、智能手续费策略、严格的风险控制和友好的用户界面，为用户提供了安全、便捷的代币兑换服务。

### 核心优势

1. **用户体验优秀**：简洁的交互流程，清晰的信息展示
2. **安全可靠**：多重验证、滑点保护、原子性操作
3. **灵活配置**：支持多种代币对、可调整的参数
4. **性能优化**：缓存机制、并发控制、查询优化
5. **可扩展性**：模块化设计，易于添加新功能

### 后续优化方向

1. 支持限价单功能
2. 添加价格预警
3. 历史价格图表
4. 批量兑换功能
5. 更多的流动性来源集成