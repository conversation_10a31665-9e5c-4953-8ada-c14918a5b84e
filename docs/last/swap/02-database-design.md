# Database Design for Swap Feature

## 1. Overview

This document details the database schema design for the swap feature, including the main tables, relationships, and data integrity constraints.

## 2. Core Tables

### 2.1 exchange_products (兑换产品配置表)

This table stores the configuration for all available swap products/trading pairs.

```sql
CREATE TABLE `exchange_products` (
  `product_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '兑换产品内部 ID (主键)',
  `base_token_id` int unsigned NOT NULL COMMENT '基础代币 ID (用户卖出/花费的代币, 外键关联 tokens.token_id)',
  `quote_token_id` int unsigned NOT NULL COMMENT '计价代币 ID (用户买入/获得的代币, 外键关联 tokens.token_id)',
  `symbol` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品交易对符号 (例如: BTC/USDT, ETH/BTC)',
  `product_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'swap' COMMENT '产品类型: swap-即时兑换, spot_limit-现货限价(若支持), 或其他自定义类型',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '该兑换产品是否激活可用 (总开关)',
  `allow_buy` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否允许买入基础代币 (即 Quote->Base 的兑换)',
  `allow_sell` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否允许卖出基础代币 (即 Base->Quote 的兑换)',
  `maintenance_message` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '维护信息 (当产品不可用或部分功能禁用时显示)',
  `min_base_amount_per_tx` decimal(40,18) NOT NULL DEFAULT '0.000000000000000000' COMMENT '单笔最小兑换的基础代币数量',
  `max_base_amount_per_tx` decimal(40,18) DEFAULT NULL COMMENT '单笔最大兑换的基础代币数量 (NULL 表示无特定限制)',
  `daily_base_volume_limit` decimal(40,18) DEFAULT NULL COMMENT '产品每日总兑换基础代币量上限 (平台风控, NULL 不限制)',
  `total_base_volume_limit` decimal(40,18) DEFAULT NULL COMMENT '产品累计总兑换基础代币量上限 (平台风控, NULL 不限制)',
  `price_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '价格/汇率来源标识 (例如: internal_oracle, binance_feed, weighted_average)',
  `allowed_slippage_percent` decimal(5,4) DEFAULT '0.0050' COMMENT '允许的最大价格滑点百分比 (例如 0.0050 表示 0.5%). NULL 表示此产品无特定滑点设置',
  `spread_rate` decimal(10,8) NOT NULL DEFAULT '0.00000000' COMMENT '平台在报价基础上额外添加的价差率 (例如 0.001 表示 0.1%)',
  `rate_refresh_interval_sec` int unsigned DEFAULT '5' COMMENT '建议的价格刷新频率 (秒), 应用层参考 (NULL 表示无建议)',
  `fee_rate` decimal(10,8) NOT NULL DEFAULT '0.00200000' COMMENT '标准兑换手续费率 (例如: 0.002 表示 0.2%)',
  `fee_charged_in` enum('base','quote') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'base' COMMENT '手续费扣除币种: base-从花费的基础代币里扣, quote-从获得的计价代币里扣',
  `min_fee_amount_base_equivalent` decimal(40,18) DEFAULT NULL COMMENT '最低手续费 (以基础代币计价, NULL 不设最低)',
  `display_order` int NOT NULL DEFAULT '9999' COMMENT '显示排序 (数字越小越靠前)',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '产品或交易对的描述信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`product_id`) USING BTREE,
  UNIQUE KEY `uq_symbol` (`symbol`) USING BTREE,
  UNIQUE KEY `uq_base_quote` (`base_token_id`,`quote_token_id`) USING BTREE,
  KEY `idx_is_active_type` (`is_active`,`product_type`) USING BTREE,
  KEY `idx_base_token` (`base_token_id`) USING BTREE,
  KEY `idx_quote_token` (`quote_token_id`) USING BTREE,
  KEY `idx_price_source` (`price_source`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='币币兑换产品/交易对配置表 (扩展版 v2)';
```

#### Key Fields Explained:

- **product_id**: Primary key for the product
- **base_token_id/quote_token_id**: Foreign keys to tokens table
- **symbol**: Trading pair symbol (e.g., "ETH/USDT")
- **is_active**: Master switch for the product
- **allow_buy/allow_sell**: Direction control
- **min/max_base_amount_per_tx**: Transaction limits
- **price_source**: Where to get price data
- **allowed_slippage_percent**: Maximum allowed price slippage
- **spread_rate**: Platform spread on top of market price
- **fee_rate**: Trading fee percentage
- **fee_charged_in**: Which token to charge fees in

### 2.2 exchange_orders (兑换订单表)

This table records all swap transactions.

```sql
CREATE TABLE `exchange_orders` (
  `order_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '订单内部 ID (主键)',
  `order_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单唯一编号 (业务层生成, 便于跟踪和对外展示)',
  `user_id` bigint unsigned NOT NULL COMMENT '执行兑换的用户 ID (外键关联 users.user_id)',
  `product_id` int unsigned NOT NULL COMMENT '关联的兑换产品 ID (外键关联 exchange_products.product_id)',
  `base_token_id` int unsigned NOT NULL COMMENT '基础代币 ID (来自 exchange_products)',
  `quote_token_id` int unsigned NOT NULL COMMENT '计价代币 ID (来自 exchange_products)',
  `symbol` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对符号 (来自 exchange_products)',
  `trade_type` enum('buy','sell') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易类型: buy-用户买入基础代币(花费计价代币), sell-用户卖出基础代币(获得计价代币)',
  `amount_base` decimal(40,18) NOT NULL COMMENT '涉及的基础代币数量',
  `amount_quote` decimal(40,18) NOT NULL COMMENT '涉及的计价代币数量',
  `price` decimal(40,18) NOT NULL COMMENT '本次交易实际成交价格 (计价代币数量 / 基础代币数量)',
  `fee_amount` decimal(40,18) NOT NULL COMMENT '收取的手续费金额',
  `fee_token_id` int unsigned NOT NULL COMMENT '手续费收取的币种 ID (通常是 base_token_id 或 quote_token_id, 根据产品配置决定)',
  `fee_rate` decimal(10,8) NOT NULL COMMENT '当时应用的手续费率 (冗余记录)',
  `status` enum('pending','processing','completed','failed','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `error_message` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单失败或取消时的错误信息',
  `client_order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端提供的订单 ID (用于幂等性检查或客户端关联)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '订单最后更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`order_id`) USING BTREE,
  UNIQUE KEY `uq_order_sn` (`order_sn`) USING BTREE,
  KEY `idx_user_id_status` (`user_id`,`status`) USING BTREE,
  KEY `idx_product_id_status` (`product_id`,`status`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_base_token_id` (`base_token_id`) USING BTREE,
  KEY `idx_quote_token_id` (`quote_token_id`) USING BTREE,
  KEY `idx_fee_token_id` (`fee_token_id`) USING BTREE,
  KEY `idx_client_order_id` (`client_order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='币币兑换订单表';
```

#### Key Fields Explained:

- **order_id**: Auto-increment primary key
- **order_sn**: Business-level unique order number
- **user_id**: Who executed the swap
- **product_id**: Which swap product was used
- **trade_type**: 'buy' or 'sell' direction
- **amount_base/amount_quote**: Token amounts involved
- **price**: Actual execution price
- **fee_amount/fee_token_id**: Fee details
- **status**: Order lifecycle status
- **error_message**: Failure details if any

### 2.3 Relationships with Existing Tables

#### tokens table
- Referenced by: exchange_products (base_token_id, quote_token_id)
- Referenced by: exchange_orders (base_token_id, quote_token_id, fee_token_id)

#### users table
- Referenced by: exchange_orders (user_id)

## 3. Data Integrity

### 3.1 Foreign Key Constraints
- exchange_products.base_token_id → tokens.token_id
- exchange_products.quote_token_id → tokens.token_id
- exchange_orders.user_id → users.user_id
- exchange_orders.product_id → exchange_products.product_id

### 3.2 Unique Constraints
- exchange_products: (symbol), (base_token_id, quote_token_id)
- exchange_orders: (order_sn), (client_order_id)

### 3.3 Business Rules Enforced by Schema
1. Each trading pair must have unique base/quote token combination
2. Order status follows defined lifecycle
3. Fee must be charged in either base or quote token
4. Soft delete support for audit trail

## 4. Performance Optimization

### 4.1 Indexes
- Composite indexes for common query patterns
- Status-based indexes for order filtering
- Time-based indexes for historical queries

### 4.2 Partitioning Strategy (Future)
- exchange_orders can be partitioned by created_at for better performance
- Archive old orders to separate tables/database

## 5. Migration from Current Design

### 5.1 Removed Tables
- `swap_price_cache` - Price data now comes from Redis/Price Service

### 5.2 Modified Tables
- Renamed `swap_configs` → `exchange_products` with enhanced fields
- Renamed `swap_orders` → `exchange_orders` with simplified structure

### 5.3 Data Migration Steps
1. Create new tables with updated schema
2. Migrate configuration data from old to new format
3. Update application code to use new tables
4. Verify data integrity
5. Drop old tables after successful migration

## 6. Sample Data

### 6.1 Exchange Products Configuration
```sql
-- ETH/USDT trading pair (both directions)
INSERT INTO exchange_products (base_token_id, quote_token_id, symbol, fee_rate, allowed_slippage_percent) 
VALUES 
(2, 1, 'ETH/USDT', 0.002, 0.005),  -- ETH to USDT
(1, 2, 'USDT/ETH', 0.002, 0.005);  -- USDT to ETH
```

### 6.2 Sample Order
```sql
-- User swapping 1 ETH for USDT
INSERT INTO exchange_orders (
  order_sn, user_id, product_id, base_token_id, quote_token_id,
  symbol, trade_type, amount_base, amount_quote, price,
  fee_amount, fee_token_id, fee_rate, status
) VALUES (
  'SWAP20240109123456', 1001, 1, 2, 1,
  'ETH/USDT', 'sell', 1.0, 2453.50, 2453.50,
  0.002, 2, 0.002, 'completed'
);
```

## 7. Monitoring & Maintenance

### 7.1 Key Metrics to Monitor
- Order creation rate
- Success/failure ratio by product
- Average execution time
- Fee collection totals

### 7.2 Regular Maintenance
- Index optimization based on query patterns
- Archival of old orders
- Statistics table updates

## 8. Security Considerations

1. **Access Control**: Strict permissions on order tables
2. **Audit Trail**: All modifications logged
3. **Data Validation**: Application-level validation before DB insert
4. **Rate Limiting**: Prevent order spam at application level