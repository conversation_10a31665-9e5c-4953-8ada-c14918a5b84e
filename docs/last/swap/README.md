# Swap Feature Documentation (闪兑功能文档)

This directory contains comprehensive documentation for the swap (闪兑) feature implementation.

## 📚 核心文档

### 新增总结文档
- [**闪兑逻辑总结**](./SWAP_LOGIC_SUMMARY.md) - 🆕 完整的闪兑功能逻辑深度分析
- [**闪兑流程图**](./SWAP_FLOW_DIAGRAM.md) - 🆕 可视化的流程图和状态图
- [**关键代码示例**](./SWAP_CODE_EXAMPLES.md) - 🆕 核心实现的代码片段

### 原有设计文档
- [Architecture Overview](./01-architecture-overview.md) - High-level architecture and design principles
- [Database Design](./02-database-design.md) - Database schema and table structures
- [Price Service Integration](./03-price-service-integration.md) - Integration with price monitoring service
- [Business Logic](./04-business-logic.md) - Core business logic and flow diagrams
- [API Layer](./05-api-layer.md) - Service interfaces and API contracts
- [Bot Integration](./06-bot-integration.md) - Telegram bot integration and UI flows
- [Configuration](./07-configuration.md) - Configuration management and settings
- [Security & Risk Control](./08-security-risk-control.md) - Security measures and risk management
- [Monitoring & Metrics](./09-monitoring-metrics.md) - Monitoring, logging, and metrics collection
- [Deployment Guide](./10-deployment-guide.md) - Deployment procedures and checklist

## 🔗 Quick Links

- [Main Architecture Document](../../SWAP_FEATURE_ARCHITECTURE.md) - Original architecture document
- [Price Monitor Service Design](../../PRICE_MONITOR_SERVICE_DESIGN.md) - Price monitoring service details

## 📊 Feature Status

- **Design Phase**: ✅ Completed
- **Implementation**: ✅ Completed
  - ✅ Core Swap Service (`/internal/service/v2/impl/swap_service.go`)
  - ✅ Price Service Integration (using existing `/internal/service/price_client.go`)
  - ✅ Bot Integration (`/internal/bot/swap/`)
  - ✅ Database Schema (`exchange_products`, `exchange_orders`)
  - ✅ Fee Calculation Strategy
  - ✅ Risk Control & Slippage Protection
- **Testing**: 🔄 In Progress
- **Production**: 🚀 Ready for Deployment

## 🎯 核心功能特性

1. **即时兑换**
   - 支持加密货币间兑换（ETH/USDT, BTC/USDT等）
   - 支持法币兑换（USDT/CNY）
   - 实时价格，快速执行

2. **透明定价**
   - 清晰的买入/卖出价格
   - 明确的手续费展示
   - 价差率可配置

3. **安全保障**
   - 支付密码验证
   - 余额双重检查
   - 滑点保护（默认1%）
   - 分布式锁防止重复执行

4. **用户体验**
   - 简洁的Telegram Bot界面
   - 多语言支持（中文/英文）
   - 实时状态反馈

## 🛠 Implementation Notes

- **Price Service**: The price service integration is already implemented in the system. The swap feature uses the existing `service.PriceClientInstance()` to get real-time prices.
- **Fee Strategy**: Uses output token fee deduction strategy for better user experience.
- **State Management**: User state is managed through Redis-backed state service.
- **Transaction Atomicity**: All fund operations are wrapped in database transactions with proper rollback handling.