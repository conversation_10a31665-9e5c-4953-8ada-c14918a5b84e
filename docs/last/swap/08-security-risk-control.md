# Swap Security and Risk Control

This document describes the comprehensive security and risk control system implemented for the swap feature.

## Overview

The swap security system provides multiple layers of protection against fraud, abuse, and operational risks. It performs real-time risk assessment and can block or flag suspicious transactions.

## Security Architecture

### 1. Multi-Layer Security Checks

The system performs these checks in sequence:

1. **Blacklist Check** - Immediate block if user is blacklisted
2. **User Risk Profile** - Evaluates user's historical behavior
3. **KYC Requirements** - Enforces limits based on verification level
4. **Anomaly Detection** - Identifies unusual patterns
5. **Device Validation** - Checks device consistency
6. **Velocity Limits** - Prevents rapid-fire attempts
7. **Risk Score Calculation** - Overall risk assessment

### 2. Risk Scoring System

Risk scores range from 0-100:
- **0-30**: Low risk (normal processing)
- **31-60**: Medium risk (may trigger additional checks)
- **61-80**: High risk (manual review may be required)
- **81-100**: Very high risk (transaction blocked)

## Security Components

### 1. User Risk Profile

Factors considered:
- Account age
- Total swap volume
- Success/failure ratio
- KYC verification level
- Previous security incidents
- Account restrictions

```go
type UserRiskProfile struct {
    UserID              uint64
    RiskLevel           string  // low, medium, high, blocked
    RiskScore           int     // 0-100
    AccountAge          int     // days
    TotalSwapVolume     decimal.Decimal
    SwapCount           int
    FailedSwapCount     int
    KYCLevel            int
    SuspiciousActivity  bool
    Restrictions        []string
}
```

### 2. KYC-Based Limits

| KYC Level | Description | Daily Limit |
|-----------|-------------|-------------|
| 0 | Unverified | $100 |
| 1 | Basic KYC | $1,000 |
| 2 | Advanced KYC | $10,000 |
| 3 | Full KYC | $100,000 |
| 4+ | Institutional | $1,000,000 |

### 3. Anomaly Detection

The system detects:
- **Amount anomalies**: Transactions significantly larger than user's average
- **Frequency anomalies**: Unusual number of transactions in short time
- **Pattern anomalies**: Suspicious patterns like round numbers
- **Time anomalies**: Transactions at unusual hours
- **Device anomalies**: New or multiple devices

### 4. Velocity Controls

Rate limiting prevents abuse:
- Maximum transactions per minute (configurable)
- Progressive delays for rapid attempts
- Automatic temporary blocks for excessive attempts

### 5. Device Fingerprinting

Tracks user devices to detect:
- New device usage (increases risk score)
- Multiple device usage (potential account sharing)
- Device switching patterns

### 6. Price Integrity Validation

Ensures pricing hasn't been manipulated:
- Compares against recent transaction history
- Checks for statistical anomalies
- Validates against configured deviation thresholds

## Security Rules Engine

### Configurable Rules

Rules are stored in the database and can be updated without code changes:

```sql
CREATE TABLE swap_security_rules (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255),
    rule_type ENUM('limit', 'pattern', 'blacklist', 'whitelist', 'threshold'),
    enabled BOOLEAN,
    priority INT,
    conditions JSON,
    actions JSON
);
```

### Default Rules

1. **Maximum Daily Amount**: Block transactions exceeding daily limit
2. **High Risk Score Block**: Block transactions with risk score > 80
3. **New User Restriction**: Limit new users to small amounts
4. **Transaction Velocity**: Block rapid transaction attempts
5. **VPN/Proxy Detection**: Flag transactions from anonymous IPs

## Security Events and Audit

### Swap Attempts Logging

Every swap attempt is logged with:
- User and transaction details
- Risk score and security checks performed
- Success/failure status
- Blocking reason (if blocked)
- Processing time

### Anomaly Tracking

Detected anomalies are stored for analysis:
- Anomaly type and severity
- Detection timestamp
- User affected
- Resolution status

## Integration Points

### 1. Quote Creation

```go
// Security check during quote creation
secReq := &SwapSecurityRequest{
    UserID:      userID,
    ProductID:   productID,
    Amount:      amount,
    AmountUSD:   amountUSD,
    ClientIP:    clientIP,
    DeviceID:    deviceID,
}

if err := security.ValidateSwapRequest(ctx, secReq); err != nil {
    return nil, fmt.Errorf("security validation failed: %w", err)
}
```

### 2. Order Execution

```go
// Quote integrity check before execution
if err := security.ValidateQuoteIntegrity(ctx, quote); err != nil {
    return nil, fmt.Errorf("quote tampered or invalid: %w", err)
}
```

### 3. Real-time Monitoring

```go
// Check for anomalies in user behavior
if err := security.CheckForAnomalies(ctx, userID, amount); err != nil {
    // Log but don't necessarily block
    log.Warning("Anomaly detected: %v", err)
}
```

## Security Administration

### Managing Blacklists

```sql
-- Add user to blacklist
INSERT INTO user_blacklist (user_id, reason, blocked_by, is_active)
VALUES (12345, 'Fraudulent activity detected', '<EMAIL>', 1);

-- Remove from blacklist
UPDATE user_blacklist 
SET is_active = 0, updated_at = NOW() 
WHERE user_id = 12345;
```

### Setting User Flags

```sql
-- Flag user as high risk
INSERT INTO user_flags (user_id, flag_type, flag_value, reason)
VALUES (12345, 'high_risk', 'fraud_suspected', 'Multiple failed attempts');

-- Add swap restriction
INSERT INTO user_flags (user_id, flag_type, expires_at)
VALUES (12345, 'swap_blocked', DATE_ADD(NOW(), INTERVAL 7 DAY));
```

### IP Reputation Management

```sql
-- Update IP reputation
UPDATE ip_reputation 
SET reputation_score = 20, risk_level = 'high', is_vpn = 1
WHERE ip_address = '*******';
```

## Monitoring and Alerts

### Key Metrics

1. **Security Performance**
   - Total attempts vs blocked attempts
   - Average risk scores
   - False positive rate

2. **Anomaly Detection**
   - Anomalies detected per hour/day
   - Top anomaly types
   - Resolution rate

3. **User Risk Distribution**
   - Users by risk level
   - High-risk user activities
   - KYC level distribution

### Alert Thresholds

- High-value transaction (> $10,000)
- Risk score > 70
- Multiple failed attempts
- Blacklisted user attempt
- New device from high-risk country

## Best Practices

### 1. Regular Review
- Review security rules monthly
- Analyze blocked transactions for false positives
- Update risk scoring algorithms based on data

### 2. Progressive Security
- Start with conservative limits
- Gradually increase based on user history
- Reward good behavior with higher limits

### 3. User Communication
- Clear error messages for security blocks
- Provide support channel for legitimate users
- Document KYC requirements clearly

### 4. Incident Response
- Have clear escalation procedures
- Document all security incidents
- Regular security audits

## Emergency Procedures

### 1. Suspected Breach
```bash
# Immediately disable swap for affected users
consul kv put swap/users/{userId}/enabled false

# Enable global maintenance mode
consul kv put swap/maintenanceMode true
consul kv put swap/maintenanceMessage "Security maintenance in progress"
```

### 2. Mass Attack Detection
```bash
# Enable strict velocity controls
consul kv put swap/risk/velocityCheckEnabled true
consul kv put swap/risk/maxOrdersPerMinute 1

# Increase risk thresholds
consul kv put swap/risk/minRiskScoreBlock 60
```

### 3. Recovery
- Review all transactions during incident
- Update security rules based on attack patterns
- Notify affected users if necessary
- Document lessons learned

## Compliance Considerations

The security system helps meet regulatory requirements:

1. **AML/KYC Compliance**
   - Transaction limits based on verification
   - Suspicious activity reporting
   - Transaction history retention

2. **Data Protection**
   - Secure storage of security data
   - Limited retention periods
   - User privacy protection

3. **Audit Trail**
   - Complete transaction history
   - Security decision logging
   - Administrator action tracking

This comprehensive security system provides robust protection while maintaining a smooth user experience for legitimate users.