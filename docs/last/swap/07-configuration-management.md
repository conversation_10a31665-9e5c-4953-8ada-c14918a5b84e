# Swap Configuration Management

This document describes the comprehensive configuration management system for the swap feature.

## Configuration Hierarchy

The swap feature uses a three-tier configuration hierarchy:

1. **Static Configuration (config.yaml)** - Default values and service settings
2. **Dynamic Configuration (Consul)** - Runtime adjustable settings
3. **Database Configuration (exchange_products)** - Product-specific settings

### Priority Order
- Consul (highest priority) → Database → config.yaml (lowest priority)

## Configuration Structure

### 1. Static Configuration (config.yaml)

Add the following section to your `config.yaml`:

```yaml
swap:
  service:
    name: "swap-service"
    enabled: true
  
  limits:
    globalDailyLimit: 1000000  # USD equivalent
    userDailyLimit: 10000      # USD equivalent
    minOrderAmount: 1          # USD equivalent
    maxOrderAmount: 100000     # USD equivalent
  
  pricing:
    defaultSlippage: 0.01      # 1%
    maxSlippage: 0.05          # 5%
    priceValiditySeconds: 30
    quoteExpirySeconds: 60
  
  fees:
    defaultRate: 0.002         # 0.2%
  
  risk:
    priceDeviationThreshold: 0.02  # 2%
    maxOrdersPerMinute: 10
  
  monitoring:
    alertThresholdUSD: 10000   # Alert for orders > $10,000
```

### 2. Dynamic Configuration (Consul)

Dynamic configuration keys in Consul:

#### Service Control
```
swap/enabled                    # Enable/disable swap service
swap/maintenanceMode           # Enable maintenance mode
swap/maintenanceMessage        # Custom maintenance message
```

#### Limits
```
swap/limits/globalDailyLimitUSD   # Global daily volume limit
swap/limits/userDailyLimitUSD     # Default user daily limit
swap/limits/minOrderAmountUSD     # Minimum order amount
swap/limits/maxOrderAmountUSD     # Maximum order amount
```

#### Pricing
```
swap/pricing/defaultSlippage      # Default slippage tolerance
swap/pricing/maxSlippage          # Maximum allowed slippage
swap/pricing/priceValiditySeconds # Price data validity period
swap/pricing/quoteExpirySeconds   # Quote expiration time
```

#### Fees
```
swap/fees/defaultRate            # Default fee rate
swap/fees/discountEnabled        # Enable volume-based discounts
```

#### Risk Control
```
swap/risk/priceDeviationThreshold  # Max price deviation allowed
swap/risk/velocityCheckEnabled      # Enable rate limiting
swap/risk/maxOrdersPerMinute        # Rate limit per user
```

#### Monitoring
```
swap/monitoring/alertThresholdUSD   # Alert threshold for large orders
swap/monitoring/logAllOrders        # Enable detailed logging
```

### 3. Product-Specific Configuration

Product configurations can be overridden in Consul:

```
swap/products/{productId}    # JSON object with overrides
```

Example:
```json
{
  "enabled": true,
  "spreadRateOverride": "0.003",
  "feeRateOverride": "0.001",
  "minAmountOverride": "10",
  "maxAmountOverride": "50000",
  "customMessage": "Limited availability"
}
```

### 4. User-Specific Configuration

User permissions and limits in Consul:

```
swap/users/{userId}/enabled           # Enable/disable for user
swap/users/{userId}/dailyLimitUSD     # Custom daily limit
swap/users/{userId}/feeDiscount       # Fee discount rate (0-1)
```

## API Endpoints

### Get Configuration
```
GET /api/v1/admin/swap/config
```

Response:
```json
{
  "data": {
    "enabled": true,
    "maintenanceMode": false,
    "globalDailyLimitUSD": "1000000",
    "userDailyLimitUSD": "10000",
    "minOrderAmountUSD": "1",
    "maxOrderAmountUSD": "100000",
    "defaultSlippage": "0.01",
    "maxSlippage": "0.05",
    "priceValiditySeconds": 30,
    "quoteExpirySeconds": 60,
    "defaultFeeRate": "0.002",
    "minFeeAmountUSD": "0.1",
    "feeDiscountEnabled": false,
    "priceDeviationThreshold": "0.02",
    "velocityCheckEnabled": false,
    "maxOrdersPerMinute": 10,
    "alertThresholdUSD": "10000",
    "logAllOrders": false,
    "updatedAt": "2024-01-06T10:00:00Z"
  }
}
```

### Update Configuration
```
PUT /api/v1/admin/swap/config
Content-Type: application/json

{
  "maintenanceMode": true,
  "maintenanceMessage": "Swap service will be back in 30 minutes",
  "maxSlippage": "0.02",
  "userDailyLimitUSD": "5000"
}
```

### Get Product Configuration
```
GET /api/v1/admin/swap/products/{productId}/config
```

### Update Product Configuration
```
PUT /api/v1/admin/swap/products/{productId}/config
Content-Type: application/json

{
  "enabled": false,
  "customMessage": "BTCUSDT swap temporarily disabled"
}
```

## Configuration Use Cases

### 1. Emergency Maintenance
```bash
# Via Consul
consul kv put swap/maintenanceMode true
consul kv put swap/maintenanceMessage "Emergency maintenance - back in 1 hour"
```

### 2. Adjust Risk Parameters
```bash
# Reduce slippage during volatile markets
consul kv put swap/pricing/maxSlippage "0.005"

# Increase velocity checks
consul kv put swap/risk/velocityCheckEnabled true
consul kv put swap/risk/maxOrdersPerMinute 5
```

### 3. User-Specific Settings
```bash
# VIP user with higher limits and fee discount
consul kv put swap/users/12345/dailyLimitUSD "100000"
consul kv put swap/users/12345/feeDiscount "0.5"
```

### 4. Product-Specific Overrides
```bash
# Disable a specific product
consul kv put swap/products/1 '{"enabled": false, "customMessage": "Under maintenance"}'
```

## Configuration Validation

The configuration manager performs these validations:

1. **Amount Limits**: Ensures min < max for all amount limits
2. **Rate Limits**: Validates percentage values are between 0 and 1
3. **Time Limits**: Ensures positive values for time-based settings
4. **Dependency Checks**: Validates related settings are consistent

## Monitoring Configuration Changes

All configuration changes are:
- Logged with timestamp and admin ID
- Stored in Consul's history
- Trigger cache invalidation
- Can generate alerts for critical changes

## Best Practices

1. **Use Consul for Dynamic Settings**: Any setting that might need adjustment without restart
2. **Document Changes**: Always include reason for configuration changes
3. **Test in Staging**: Test configuration changes in staging environment first
4. **Monitor Impact**: Watch metrics after configuration changes
5. **Have Rollback Plan**: Keep previous configuration values documented

## Integration with Swap Service

The swap service automatically:
- Checks maintenance mode before operations
- Validates amounts against configured limits
- Applies fee discounts for eligible users
- Enforces rate limits when enabled
- Logs high-value transactions based on alert threshold

This configuration system provides flexible control over the swap feature while maintaining security and operational stability.