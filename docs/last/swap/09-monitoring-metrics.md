# Monitoring & Metrics for Swap Feature

## 1. Overview

This document describes the comprehensive monitoring, metrics collection, and observability strategy for the swap feature to ensure reliability, performance, and business insights.

## 2. Metrics Architecture

### 2.1 Metrics Collection Stack

```
┌─────────────────────────────────────────────────────┐
│                Application Layer                     │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────┐│
│  │   Business   │  │  Performance │  │   System   ││
│  │   Metrics    │  │   Metrics    │  │  Metrics   ││
│  └──────┬──────┘  └──────┬───────┘  └─────┬──────┘│
└─────────┼─────────────────┼────────────────┼───────┘
          │                 │                 │
          └─────────────────┼─────────────────┘
                           │
┌──────────────────────────▼──────────────────────────┐
│                 Prometheus Client                    │
│         (Counters, Gauges, Histograms)              │
└──────────────────────────┬──────────────────────────┘
                           │
┌──────────────────────────▼──────────────────────────┐
│               Prometheus Server                      │
│         (TSDB, PromQL, Aggregation)                 │
└──────────────────────────┬──────────────────────────┘
                           │
         ┌─────────────────┴──────────────────┐
         │                                    │
┌────────▼────────┐              ┌───────────▼─────────┐
│     Grafana     │              │   Alert Manager     │
│  (Dashboards)   │              │   (Notifications)   │
└─────────────────┘              └───────────────────┘
```

## 3. Business Metrics

### 3.1 Transaction Metrics

```go
package metrics

import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    // Transaction volume metrics
    swapTransactionTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "swap_transaction_total",
            Help: "Total number of swap transactions",
        },
        []string{"status", "from_token", "to_token"},
    )
    
    swapVolumeUSD = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "swap_volume_usd_total",
            Help: "Total swap volume in USD",
        },
        []string{"token_pair", "direction"},
    )
    
    swapFeeCollectedUSD = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "swap_fee_collected_usd_total",
            Help: "Total fees collected in USD",
        },
        []string{"token"},
    )
    
    // Transaction status distribution
    swapTransactionDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "swap_transaction_duration_seconds",
            Help:    "Time taken to complete swap transaction",
            Buckets: []float64{0.1, 0.5, 1, 2, 5, 10, 30},
        },
        []string{"status"},
    )
    
    // Current active swaps
    activeSwapsGauge = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_active_transactions",
            Help: "Number of currently active swap transactions",
        },
        []string{"step"},
    )
)

// RecordSwapTransaction records metrics for a swap transaction
func RecordSwapTransaction(tx *SwapTransaction, result *SwapResult) {
    // Record transaction count
    swapTransactionTotal.WithLabelValues(
        result.Status,
        tx.FromTokenSymbol,
        tx.ToTokenSymbol,
    ).Inc()
    
    // Record volume
    volumeUSD := convertToUSD(tx.FromAmount, tx.FromTokenSymbol)
    swapVolumeUSD.WithLabelValues(
        tx.Symbol,
        tx.TradeType,
    ).Add(volumeUSD.InexactFloat64())
    
    // Record fees
    feeUSD := convertToUSD(result.FeeAmount, result.FeeTokenSymbol)
    swapFeeCollectedUSD.WithLabelValues(
        result.FeeTokenSymbol,
    ).Add(feeUSD.InexactFloat64())
    
    // Record duration
    duration := result.CompletedAt.Sub(tx.CreatedAt).Seconds()
    swapTransactionDuration.WithLabelValues(
        result.Status,
    ).Observe(duration)
}
```

### 3.2 User Behavior Metrics

```go
var (
    // User activity metrics
    uniqueSwapUsers = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_unique_users_daily",
            Help: "Number of unique users performing swaps daily",
        },
        []string{"user_type"},
    )
    
    userSwapFrequency = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "swap_user_transaction_frequency",
            Help:    "Number of swaps per user per day",
            Buckets: []float64{1, 2, 5, 10, 20, 50, 100},
        },
        []string{"user_segment"},
    )
    
    // Popular trading pairs
    tradingPairPopularity = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_trading_pair_popularity",
            Help: "Popularity score of trading pairs",
        },
        []string{"pair"},
    )
    
    // User retention
    userRetention = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_user_retention_rate",
            Help: "User retention rate for swap feature",
        },
        []string{"cohort", "period"},
    )
)

// UpdateUserMetrics updates user behavior metrics
func UpdateUserMetrics() {
    go func() {
        ticker := time.NewTicker(5 * time.Minute)
        defer ticker.Stop()
        
        for range ticker.C {
            // Update unique users
            dailyUsers := countUniqueUsers(24 * time.Hour)
            uniqueSwapUsers.WithLabelValues("all").Set(float64(dailyUsers))
            
            // Update VIP users
            vipUsers := countUniqueVIPUsers(24 * time.Hour)
            uniqueSwapUsers.WithLabelValues("vip").Set(float64(vipUsers))
            
            // Update trading pair popularity
            popularity := calculateTradingPairPopularity()
            for pair, score := range popularity {
                tradingPairPopularity.WithLabelValues(pair).Set(score)
            }
        }
    }()
}
```

## 4. Performance Metrics

### 4.1 API Performance

```go
var (
    // API latency
    apiRequestDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "swap_api_request_duration_seconds",
            Help:    "API request duration in seconds",
            Buckets: prometheus.DefBuckets,
        },
        []string{"method", "endpoint", "status"},
    )
    
    // Database query performance
    dbQueryDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "swap_db_query_duration_seconds",
            Help:    "Database query duration in seconds",
            Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1},
        },
        []string{"operation", "table"},
    )
    
    // Cache performance
    cacheHitRate = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_cache_hit_rate",
            Help: "Cache hit rate percentage",
        },
        []string{"cache_type"},
    )
    
    // Price service latency
    priceServiceLatency = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "swap_price_service_latency_seconds",
            Help:    "Price service response latency",
            Buckets: []float64{0.01, 0.05, 0.1, 0.25, 0.5, 1},
        },
        []string{"symbol", "source"},
    )
)

// Middleware for API metrics
func MetricsMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        // Process request
        c.Next()
        
        // Record metrics
        duration := time.Since(start).Seconds()
        status := strconv.Itoa(c.Writer.Status())
        
        apiRequestDuration.WithLabelValues(
            c.Request.Method,
            c.FullPath(),
            status,
        ).Observe(duration)
    }
}

// Database metrics wrapper
func RecordDBMetrics(operation, table string, fn func() error) error {
    start := time.Now()
    err := fn()
    duration := time.Since(start).Seconds()
    
    dbQueryDuration.WithLabelValues(operation, table).Observe(duration)
    
    if err != nil {
        dbErrorTotal.WithLabelValues(operation, table).Inc()
    }
    
    return err
}
```

### 4.2 System Performance

```go
var (
    // Goroutine metrics
    goroutineCount = promauto.NewGauge(
        prometheus.GaugeOpts{
            Name: "swap_goroutine_count",
            Help: "Number of goroutines",
        },
    )
    
    // Memory usage
    memoryUsage = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_memory_usage_bytes",
            Help: "Memory usage in bytes",
        },
        []string{"type"},
    )
    
    // Connection pool metrics
    connectionPoolSize = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_connection_pool_size",
            Help: "Size of connection pools",
        },
        []string{"pool_name"},
    )
    
    // Queue metrics
    queueSize = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_queue_size",
            Help: "Number of items in queues",
        },
        []string{"queue_name"},
    )
    
    queueProcessingTime = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "swap_queue_processing_time_seconds",
            Help:    "Time to process queue items",
            Buckets: prometheus.DefBuckets,
        },
        []string{"queue_name"},
    )
)

// System metrics collector
func CollectSystemMetrics() {
    go func() {
        ticker := time.NewTicker(10 * time.Second)
        defer ticker.Stop()
        
        for range ticker.C {
            // Goroutines
            goroutineCount.Set(float64(runtime.NumGoroutine()))
            
            // Memory stats
            var m runtime.MemStats
            runtime.ReadMemStats(&m)
            memoryUsage.WithLabelValues("heap").Set(float64(m.HeapAlloc))
            memoryUsage.WithLabelValues("stack").Set(float64(m.StackInuse))
            memoryUsage.WithLabelValues("sys").Set(float64(m.Sys))
            
            // Connection pools
            updateConnectionPoolMetrics()
            
            // Queue sizes
            updateQueueMetrics()
        }
    }()
}
```

## 5. Error and Reliability Metrics

### 5.1 Error Tracking

```go
var (
    // Error metrics
    swapErrorTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "swap_error_total",
            Help: "Total number of swap errors",
        },
        []string{"error_type", "severity"},
    )
    
    // Service availability
    serviceAvailability = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_service_availability",
            Help: "Service availability percentage",
        },
        []string{"service"},
    )
    
    // Circuit breaker metrics
    circuitBreakerState = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_circuit_breaker_state",
            Help: "Circuit breaker state (0=closed, 1=open, 2=half-open)",
        },
        []string{"breaker_name"},
    )
    
    // Retry metrics
    retryAttempts = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "swap_retry_attempts",
            Help:    "Number of retry attempts",
            Buckets: []float64{1, 2, 3, 5, 10},
        },
        []string{"operation"},
    )
)

// Error recorder
func RecordError(errType string, severity string, err error) {
    swapErrorTotal.WithLabelValues(errType, severity).Inc()
    
    // Log error details
    log.WithFields(log.Fields{
        "error_type": errType,
        "severity":   severity,
        "error":      err.Error(),
    }).Error("Swap error occurred")
}

// Service health tracker
type ServiceHealthTracker struct {
    checks map[string]*HealthCheck
    mu     sync.RWMutex
}

func (sht *ServiceHealthTracker) UpdateHealth(service string, healthy bool) {
    sht.mu.Lock()
    defer sht.mu.Unlock()
    
    if check, exists := sht.checks[service]; exists {
        check.Healthy = healthy
        check.LastCheck = time.Now()
        
        // Update metric
        availability := sht.calculateAvailability(service)
        serviceAvailability.WithLabelValues(service).Set(availability)
    }
}
```

### 5.2 SLA Metrics

```go
var (
    // SLA compliance
    slaCompliance = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_sla_compliance_percent",
            Help: "SLA compliance percentage",
        },
        []string{"sla_type"},
    )
    
    // Uptime tracking
    uptimeSeconds = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "swap_uptime_seconds_total",
            Help: "Total uptime in seconds",
        },
        []string{"component"},
    )
    
    // Response time percentiles
    responseTimePercentile = promauto.NewSummaryVec(
        prometheus.SummaryOpts{
            Name: "swap_response_time_percentile",
            Help: "Response time percentiles",
            Objectives: map[float64]float64{
                0.5:  0.05,
                0.9:  0.01,
                0.95: 0.005,
                0.99: 0.001,
            },
        },
        []string{"operation"},
    )
)

// SLA Monitor
type SLAMonitor struct {
    targets map[string]SLATarget
}

type SLATarget struct {
    Name            string
    TargetValue     float64
    MeasurementFunc func() float64
}

func (sm *SLAMonitor) CheckSLACompliance() {
    for _, target := range sm.targets {
        actual := target.MeasurementFunc()
        compliance := (actual / target.TargetValue) * 100
        
        if compliance > 100 {
            compliance = 100
        }
        
        slaCompliance.WithLabelValues(target.Name).Set(compliance)
    }
}
```

## 6. Business Intelligence Metrics

### 6.1 Revenue Analytics

```go
var (
    // Revenue metrics
    dailyRevenue = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_daily_revenue_usd",
            Help: "Daily revenue in USD",
        },
        []string{"revenue_type"},
    )
    
    // Profit margins
    profitMargin = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_profit_margin_percent",
            Help: "Profit margin percentage",
        },
        []string{"token_pair"},
    )
    
    // Customer lifetime value
    customerLifetimeValue = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "swap_customer_lifetime_value_usd",
            Help:    "Customer lifetime value in USD",
            Buckets: []float64{10, 50, 100, 500, 1000, 5000, 10000},
        },
        []string{"user_segment"},
    )
    
    // Conversion funnel
    conversionFunnel = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_conversion_funnel_percent",
            Help: "Conversion rate at each funnel stage",
        },
        []string{"stage"},
    )
)

// Revenue calculator
func CalculateRevueMetrics() {
    go func() {
        ticker := time.NewTicker(1 * time.Hour)
        defer ticker.Stop()
        
        for range ticker.C {
            // Calculate daily revenue
            feeRevenue := calculateDailyFeeRevenue()
            spreadRevenue := calculateDailySpreadRevenue()
            
            dailyRevenue.WithLabelValues("fees").Set(feeRevenue)
            dailyRevenue.WithLabelValues("spread").Set(spreadRevenue)
            dailyRevenue.WithLabelValues("total").Set(feeRevenue + spreadRevenue)
            
            // Calculate profit margins
            margins := calculateProfitMargins()
            for pair, margin := range margins {
                profitMargin.WithLabelValues(pair).Set(margin)
            }
            
            // Update conversion funnel
            updateConversionFunnel()
        }
    }()
}
```

### 6.2 Market Analysis Metrics

```go
var (
    // Market share
    marketShare = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_market_share_percent",
            Help: "Market share percentage by trading pair",
        },
        []string{"pair"},
    )
    
    // Price competitiveness
    priceCompetitiveness = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "swap_price_competitiveness_score",
            Help: "Price competitiveness score (0-100)",
        },
        []string{"pair", "competitor"},
    )
    
    // Slippage analysis
    actualSlippage = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "swap_actual_slippage_percent",
            Help:    "Actual slippage percentage",
            Buckets: []float64{0.01, 0.05, 0.1, 0.25, 0.5, 1, 2, 5},
        },
        []string{"pair"},
    )
)
```

## 7. Alerting Rules

### 7.1 Prometheus Alert Rules

```yaml
groups:
  - name: swap_alerts
    interval: 30s
    rules:
      # High error rate
      - alert: SwapHighErrorRate
        expr: rate(swap_error_total[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High swap error rate detected"
          description: "Error rate is {{ $value }} errors per second"
      
      # Low success rate
      - alert: SwapLowSuccessRate
        expr: |
          rate(swap_transaction_total{status="completed"}[5m]) / 
          rate(swap_transaction_total[5m]) < 0.95
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low swap success rate"
          description: "Success rate is {{ $value }}"
      
      # Price staleness
      - alert: SwapPriceDataStale
        expr: swap_price_data_age_seconds > 60
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Price data is stale"
          description: "Price data for {{ $labels.symbol }} is {{ $value }} seconds old"
      
      # High response time
      - alert: SwapHighResponseTime
        expr: |
          histogram_quantile(0.95, 
            rate(swap_api_request_duration_seconds_bucket[5m])
          ) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High API response time"
          description: "95th percentile response time is {{ $value }} seconds"
      
      # Circuit breaker open
      - alert: SwapCircuitBreakerOpen
        expr: swap_circuit_breaker_state == 1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Circuit breaker is open"
          description: "Circuit breaker {{ $labels.breaker_name }} is open"
```

### 7.2 Business Alerts

```yaml
      # Abnormal trading volume
      - alert: SwapAbnormalVolume
        expr: |
          abs(rate(swap_volume_usd_total[1h]) - 
              avg_over_time(rate(swap_volume_usd_total[1h])[24h:1h])) > 
          3 * stddev_over_time(rate(swap_volume_usd_total[1h])[24h:1h])
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Abnormal trading volume detected"
          description: "Current volume deviates significantly from 24h average"
      
      # Revenue drop
      - alert: SwapRevenueDrop
        expr: |
          swap_daily_revenue_usd{revenue_type="total"} < 
          avg_over_time(swap_daily_revenue_usd{revenue_type="total"}[7d]) * 0.7
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "Significant revenue drop"
          description: "Daily revenue dropped below 70% of 7-day average"
      
      # High slippage
      - alert: SwapHighSlippage
        expr: |
          histogram_quantile(0.95, 
            rate(swap_actual_slippage_percent_bucket[5m])
          ) > 1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High slippage detected"
          description: "95th percentile slippage is {{ $value }}%"
```

## 8. Dashboards

### 8.1 Operations Dashboard

```json
{
  "dashboard": {
    "title": "Swap Operations Dashboard",
    "panels": [
      {
        "title": "Transaction Volume",
        "targets": [
          {
            "expr": "rate(swap_transaction_total[5m])",
            "legendFormat": "{{status}}"
          }
        ]
      },
      {
        "title": "Success Rate",
        "targets": [
          {
            "expr": "rate(swap_transaction_total{status=\"completed\"}[5m]) / rate(swap_transaction_total[5m]) * 100"
          }
        ]
      },
      {
        "title": "Response Time (95th percentile)",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(swap_api_request_duration_seconds_bucket[5m]))"
          }
        ]
      },
      {
        "title": "Active Swaps",
        "targets": [
          {
            "expr": "swap_active_transactions",
            "legendFormat": "{{step}}"
          }
        ]
      },
      {
        "title": "Error Rate",
        "targets": [
          {
            "expr": "rate(swap_error_total[5m])",
            "legendFormat": "{{error_type}}"
          }
        ]
      },
      {
        "title": "Price Data Freshness",
        "targets": [
          {
            "expr": "swap_price_data_age_seconds",
            "legendFormat": "{{symbol}}"
          }
        ]
      }
    ]
  }
}
```

### 8.2 Business Dashboard

```json
{
  "dashboard": {
    "title": "Swap Business Metrics",
    "panels": [
      {
        "title": "Daily Revenue (USD)",
        "targets": [
          {
            "expr": "swap_daily_revenue_usd",
            "legendFormat": "{{revenue_type}}"
          }
        ]
      },
      {
        "title": "Trading Volume by Pair",
        "targets": [
          {
            "expr": "rate(swap_volume_usd_total[1h])",
            "legendFormat": "{{token_pair}}"
          }
        ]
      },
      {
        "title": "Unique Daily Users",
        "targets": [
          {
            "expr": "swap_unique_users_daily",
            "legendFormat": "{{user_type}}"
          }
        ]
      },
      {
        "title": "Popular Trading Pairs",
        "targets": [
          {
            "expr": "swap_trading_pair_popularity",
            "legendFormat": "{{pair}}"
          }
        ]
      },
      {
        "title": "Conversion Funnel",
        "targets": [
          {
            "expr": "swap_conversion_funnel_percent",
            "legendFormat": "{{stage}}"
          }
        ]
      },
      {
        "title": "Profit Margins",
        "targets": [
          {
            "expr": "swap_profit_margin_percent",
            "legendFormat": "{{token_pair}}"
          }
        ]
      }
    ]
  }
}
```

## 9. Logging Strategy

### 9.1 Structured Logging

```go
// Logger configuration
type SwapLogger struct {
    logger *logrus.Logger
}

func NewSwapLogger() *SwapLogger {
    logger := logrus.New()
    logger.SetFormatter(&logrus.JSONFormatter{
        TimestampFormat: time.RFC3339Nano,
        FieldMap: logrus.FieldMap{
            logrus.FieldKeyTime: "@timestamp",
            logrus.FieldKeyMsg:  "message",
        },
    })
    
    return &SwapLogger{logger: logger}
}

// Log swap transaction
func (sl *SwapLogger) LogTransaction(tx *SwapTransaction, result *SwapResult) {
    fields := logrus.Fields{
        "transaction_id":  tx.OrderID,
        "user_id":         tx.UserID,
        "from_token":      tx.FromTokenSymbol,
        "to_token":        tx.ToTokenSymbol,
        "from_amount":     tx.FromAmount.String(),
        "to_amount":       tx.ToAmount.String(),
        "fee_amount":      result.FeeAmount.String(),
        "status":          result.Status,
        "duration_ms":     result.Duration.Milliseconds(),
        "slippage":        result.Slippage.String(),
        "risk_score":      tx.RiskScore,
        "price_source":    tx.PriceSource,
        "timestamp":       tx.Timestamp,
    }
    
    if result.Status == "completed" {
        sl.logger.WithFields(fields).Info("Swap transaction completed")
    } else {
        fields["error"] = result.ErrorMessage
        sl.logger.WithFields(fields).Error("Swap transaction failed")
    }
}

// Log performance metrics
func (sl *SwapLogger) LogPerformance(operation string, duration time.Duration, err error) {
    fields := logrus.Fields{
        "operation":    operation,
        "duration_ms":  duration.Milliseconds(),
        "success":      err == nil,
    }
    
    if err != nil {
        fields["error"] = err.Error()
        sl.logger.WithFields(fields).Warn("Operation completed with error")
    } else {
        sl.logger.WithFields(fields).Debug("Operation completed successfully")
    }
}
```

### 9.2 Audit Logging

```go
// Audit logger for compliance
type AuditLogger struct {
    logger *logrus.Logger
    output io.Writer
}

func (al *AuditLogger) LogAuditEvent(event *AuditEvent) {
    entry := al.logger.WithFields(logrus.Fields{
        "event_id":       event.ID,
        "event_type":     event.Type,
        "user_id":        event.UserID,
        "action":         event.Action,
        "resource":       event.Resource,
        "result":         event.Result,
        "ip_address":     event.IPAddress,
        "user_agent":     event.UserAgent,
        "timestamp":      event.Timestamp,
        "correlation_id": event.CorrelationID,
    })
    
    // Add additional context
    for k, v := range event.Context {
        entry = entry.WithField(k, v)
    }
    
    entry.Info("Audit event")
}
```

## 10. Monitoring Best Practices

### 10.1 Metric Guidelines

```go
// Metric naming conventions
/*
1. Use consistent prefixes: swap_*
2. Include units in metric names: *_seconds, *_bytes, *_total
3. Use labels for dimensions, not metric names
4. Keep cardinality under control
*/

// Good practice
swapTransactionTotal.WithLabelValues("completed", "ETH", "USDT").Inc()

// Bad practice - high cardinality
swapTransactionByUser.WithLabelValues(userID).Inc() // Don't use user IDs as labels
```

### 10.2 Dashboard Design Principles

```yaml
principles:
  - name: "Overview First"
    description: "Start with high-level metrics, drill down to details"
    
  - name: "Clear Visualizations"
    description: "Use appropriate chart types for each metric"
    
  - name: "Actionable Insights"
    description: "Include thresholds and annotations"
    
  - name: "Performance Impact"
    description: "Avoid expensive queries in frequently viewed dashboards"
    
  - name: "Mobile Friendly"
    description: "Ensure dashboards are readable on mobile devices"
```

### 10.3 Alert Fatigue Prevention

```go
// Alert deduplication
type AlertManager struct {
    recentAlerts map[string]time.Time
    mu           sync.Mutex
}

func (am *AlertManager) ShouldAlert(alertKey string, cooldown time.Duration) bool {
    am.mu.Lock()
    defer am.mu.Unlock()
    
    if lastAlert, exists := am.recentAlerts[alertKey]; exists {
        if time.Since(lastAlert) < cooldown {
            return false
        }
    }
    
    am.recentAlerts[alertKey] = time.Now()
    return true
}

// Smart alerting with context
func (am *AlertManager) SendContextualAlert(alert Alert) {
    // Add historical context
    alert.Context["previous_occurrences"] = am.getOccurrenceCount(alert.Key, 24*time.Hour)
    alert.Context["trend"] = am.calculateTrend(alert.Metric, 1*time.Hour)
    
    // Add suggested actions
    alert.SuggestedActions = am.getSuggestedActions(alert.Type)
    
    // Send alert
    am.send(alert)
}
```