# Price Service Integration Summary for Swap Feature

## Overview

The price service has already been implemented in the system at `/internal/service/price_client.go`. The swap feature should directly use this existing service instead of creating a new one.

## Key Integration Points

### 1. Getting the Price Client

```go
import "telegram-bot-api/internal/service"

// Use singleton instance
priceClient := service.PriceClientInstance()

// Or create with custom config
config := &service.ClientConfig{
    RedisConfigName:     "default",
    DefaultStaleTimeout: 45 * time.Second,
    RetryAttempts:       5,
    RetryDelay:          200 * time.Millisecond,
}
priceClient := service.NewPriceClient(config)
```

### 2. Available Methods

- **`GetRealTimePrice(ctx, symbol)`**: Get single crypto price with automatic freshness check
- **`GetMultiplePrices(ctx, symbols)`**: Batch get crypto prices
- **`GetFiatPrice(ctx, asset, currency)`**: Get fiat exchange rates (buy/sell)
- **`GetMultipleFiatPrices(ctx, pairs)`**: Batch get fiat prices
- **`GetPriceHistory(ctx, symbol, from, to)`**: Get historical crypto prices
- **`GetFiatPriceHistory(ctx, asset, currency, from, to)`**: Get historical fiat prices

### 3. Data Structures

```go
// Crypto price data
type PriceData struct {
    Symbol      string          
    Price       decimal.Decimal 
    Volume24h   decimal.Decimal 
    Change24h   decimal.Decimal 
    High24h     decimal.Decimal 
    Low24h      decimal.Decimal 
    Provider    string          
    Timestamp   time.Time       
    LastUpdated int64           
}

// Fiat price data
type FiatPriceData struct {
    Asset          string          
    Currency       string          
    CurrencySymbol string          
    BuyPrice       decimal.Decimal 
    SellPrice      decimal.Decimal 
    MidPrice       decimal.Decimal 
    Spread         decimal.Decimal 
    SpreadPercent  decimal.Decimal 
    Provider       string          
    Timestamp      time.Time       
    LastUpdated    int64           
}
```

### 4. Default Configuration

- **Redis Config**: Uses "default" Redis configuration
- **Crypto Stale Timeout**: 30 seconds
- **Fiat Stale Timeout**: 300 seconds (5 minutes)
- **Retry Attempts**: 3 times
- **Retry Delay**: 100ms between retries

### 5. Integration in Swap Service

```go
type SwapService struct {
    priceClient service.IPriceClient
    // other fields...
}

func NewSwapService() *SwapService {
    return &SwapService{
        priceClient: service.PriceClientInstance(),
        // other initializations...
    }
}
```

### 6. Price Freshness

The price client automatically checks data freshness:
- Returns error if price data is older than configured timeout
- No need to manually check timestamps in swap service
- Automatic retry mechanism for stale data

### 7. Error Handling

The price client may return these errors:
- Price data stale error - when data is older than timeout
- No data available error - when price not found in Redis
- Redis connection errors

## Redis Key Structure

The existing price service uses these Redis keys:
- Crypto prices: `price:live:{symbol}` (e.g., `price:live:ETHUSDT`)
- Fiat prices: `fiat:price:{asset}_{currency}` (e.g., `fiat:price:USDT_CNY`)
- History: `price:history:{symbol}` and `fiat:history:{asset}_{currency}`

## Important Notes

1. **DO NOT** create a new price service interface
2. **DO NOT** implement direct Redis access for prices
3. **USE** the existing `service.IPriceClient` interface
4. **USE** `service.PriceClientInstance()` for singleton access
5. The price monitor service must be running to populate Redis with price data

## Example Usage

See `/docs/swap/swap_service_price_integration_example.go` for a complete example of integrating the price service into the swap service implementation.