# 闪兑功能关键代码示例

## 1. 报价生成核心代码

### 创建报价请求
```go
// internal/bot/swap/message.go:97-103
quoteRequest := &interfaces.SwapQuoteRequest{
    UserID:      user.Id,
    FromTokenID: fromTokenID,    // 支付代币ID
    ToTokenID:   toTokenID,      // 收到代币ID
    Amount:      amount,         // 用户输入金额
    AmountType:  amountType,     // "from" 或 "to"
}
```

### 报价生成逻辑
```go
// internal/service/v2/impl/swap_service.go:176-527
func (s *SwapService) CreateQuote(ctx context.Context, req *SwapQuoteRequest) (*SwapQuote, error) {
    // 1. 检查维护模式
    if inMaintenance {
        return nil, fmt.Errorf("swap service unavailable: %s", message)
    }
    
    // 2. 确定交易方向
    if req.AmountType == "from" {
        product, err = s.GetProductByTokenPair(ctx, req.FromTokenID, req.ToTokenID)
        if err == nil {
            tradeType = "sell" // 卖出基础代币
        }
    }
    
    // 3. 获取实时价格
    if product.BaseToken == "CNY" || product.QuoteToken == "CNY" {
        // CNY 交易对使用法币价格API
        fiatPriceData, err := s.priceClient.GetFiatPrice(ctx, asset, currency)
        price = fiatPriceData.MidPrice
    } else {
        // 加密货币使用实时价格
        priceData, err := s.priceClient.GetRealTimePrice(ctx, priceSymbol)
        price = priceData.Price
    }
    
    // 4. 应用价差
    if tradeType == "buy" {
        price = price.Mul(decimal.NewFromInt(1).Add(spreadRate))
    } else {
        price = price.Mul(decimal.NewFromInt(1).Sub(spreadRate))
    }
    
    // 5. 计算手续费
    feeCalculator := NewOutputFeeCalculator(product)
    feeResult, err := feeCalculator.CalculateFee(ctx, feeReq)
    toAmount = feeResult.OutputAmountAfterFee
    
    // 6. 生成并缓存报价
    quote := &SwapQuote{
        ID:          guid.S(),
        ExpiresAt:   time.Now().Add(120 * time.Second),
        // ... 其他字段
    }
    
    s.redis.SetEX(ctx, cacheKey, quoteData, 125) // 120秒有效期 + 5秒缓冲
    return quote, nil
}
```

## 2. 手续费计算实现

### 输出代币扣费策略
```go
// internal/service/v2/impl/fee_calculator.go
type OutputFeeCalculator struct {
    product *entity.ExchangeProducts
}

func (c *OutputFeeCalculator) CalculateFee(ctx context.Context, req *FeeCalculationRequest) (*FeeResult, error) {
    var outputAmount, feeAmount, outputAfterFee decimal.Decimal
    var feeTokenID uint
    var feeTokenSymbol string
    
    if req.TradeType == "buy" {
        // 买入：从基础代币（输出）扣费
        outputAmount = req.BaseAmount
        feeAmount = outputAmount.Mul(c.product.OutputFeeRate)
        outputAfterFee = outputAmount.Sub(feeAmount)
        feeTokenID = req.BaseTokenID
        feeTokenSymbol = req.BaseTokenSymbol
    } else {
        // 卖出：从报价代币（输出）扣费
        outputAmount = req.QuoteAmount
        feeAmount = outputAmount.Mul(c.product.OutputFeeRate)
        outputAfterFee = outputAmount.Sub(feeAmount)
        feeTokenID = req.QuoteTokenID
        feeTokenSymbol = req.QuoteTokenSymbol
    }
    
    return &FeeResult{
        OutputAmountBeforeFee: outputAmount,
        OutputAmountAfterFee:  outputAfterFee,
        FeeAmount:            feeAmount,
        FeeTokenID:           feeTokenID,
        FeeTokenSymbol:       feeTokenSymbol,
        FeeRate:              c.product.OutputFeeRate,
    }, nil
}
```

## 3. 订单执行核心代码

### 使用分布式锁确保原子性
```go
// internal/service/v2/impl/swap_service.go:830-863
func (s *SwapService) ExecuteOrder(ctx context.Context, orderID string) (*SwapResult, error) {
    // 使用分布式锁防止并发执行
    lockKey := fmt.Sprintf("swap:order:execute:%s", orderID)
    lockErr := utils.WithLock(ctx, s.redis, lockKey, 30*time.Second, func() error {
        result, err := s.executeOrderLegacy(ctx, orderID)
        if err != nil {
            return err
        }
        s.lastLegacyResult = result
        return nil
    })
    
    if lockErr != nil {
        return nil, model.NewSwapError(model.ErrCodeInternalError, "failed to execute order")
    }
    
    return s.lastLegacyResult, nil
}
```

### 资金操作实现
```go
// internal/service/v2/impl/swap_service.go:1100-1221
// 1. 扣除用户支付的代币
debitReq := &constants.FundOperationRequest{
    UserID:      uint64(order.UserId),
    TokenSymbol: debitTokenSymbol,
    Amount:      debitAmount,
    BusinessID:  fmt.Sprintf("swap_%s_debit", order.OrderSn),
    FundType:    constants.FundTypeExchangeOut,
    Description: fmt.Sprintf("Swap %s to %s - Debit", debitTokenSymbol, creditTokenSymbol),
}

debitResult, err := wallet.Manager().ProcessFundOperationInTx(ctx, tx, debitReq)
if !debitResult.Successful {
    // 余额不足，标记订单失败
    tx.Model(&entity.ExchangeOrders{}).
        Where("order_sn = ?", orderID).
        Data(g.Map{"status": "failed", "error_message": "Insufficient balance"}).
        Update()
    return nil, fmt.Errorf("insufficient balance for swap")
}

// 2. 增加用户收到的代币
creditReq := &constants.FundOperationRequest{
    UserID:      uint64(order.UserId),
    TokenSymbol: creditTokenSymbol,
    Amount:      creditAmount,
    BusinessID:  fmt.Sprintf("swap_%s_credit", order.OrderSn),
    FundType:    constants.FundTypeExchangeIn,
    Description: fmt.Sprintf("Swap %s to %s - Credit", debitTokenSymbol, creditTokenSymbol),
}

creditResult, err := wallet.Manager().ProcessFundOperationInTx(ctx, tx, creditReq)
```

## 4. 用户界面交互代码

### 处理产品选择
```go
// internal/bot/swap/handler.go:25-101
func HandleSwapCallback(ctx context.Context, query *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
    // 检查支付密码
    passwordCheckResult := shared.CheckPaymentPassword(ctx, query)
    if !passwordCheckResult.HasPassword {
        return passwordCheckResult.Response, nil
    }
    
    // 获取可用产品
    products, err := swapService.GetActiveProducts(ctx)
    if len(products) == 0 {
        text := service.I18n().T(ctx, "{#SwapNoProductsAvailable}")
        // 显示无产品可用信息
    }
    
    // 构建产品选择键盘
    keyboard := buildProductSelectionKeyboard(ctx, products)
    
    // 设置用户状态
    userState := &model.UserState{
        State:   consts.StateSwapSelectingProduct,
        Context: make(map[string]string),
    }
}
```

### 处理金额输入
```go
// internal/bot/swap/message.go:40-284
func handleAmountInput(ctx context.Context, message *tgbotapi.Message, userState *model.UserState) (bool, error) {
    // 解析金额
    amount, err := decimal.NewFromString(amountStr)
    
    // 验证金额
    isValid, errorKey := utility.ValidateAmountDecimal(ctx, amount, inputSymbol)
    if !isValid {
        reply := tgbotapi.NewMessage(message.Chat.ID, errorKey)
        bot.Send(reply)
        return true, nil
    }
    
    // 创建报价
    quote, err := swapService.CreateQuote(ctx, quoteRequest)
    if err != nil {
        // 处理特定错误（如金额过小/过大）
        if strings.Contains(errStr, "amount below minimum:") {
            errorMsg = service.I18n().Tf(ctx, "{#SwapAmountBelowMinimum}", minAmount, inputSymbol)
        }
    }
    
    // 显示报价确认
    text := service.I18n().Tf(ctx, "{#SwapQuoteDetails}",
        fromAmountStr, fromSymbol,
        toAmountStr, toSymbol,
        baseSymbol, priceStr, quoteSymbol,
        feeAmountStr, feeTokenSymbol,
    )
}
```

## 5. 价格获取实现

### CNY 法币价格处理
```go
// internal/bot/swap/handler.go:432-546
func getProductPrice(ctx context.Context, product *entity.ExchangeProducts) (string, error) {
    if product.BaseToken == "CNY" || product.QuoteToken == "CNY" {
        // CNY 交易对
        fiatPrice, err := priceClient.GetFiatPrice(ctx, asset, currency)
        
        if isCNYBase {
            // CNY/USDT: 1 CNY = X USDT
            buyPrice = decimal.NewFromInt(1).Div(fiatPrice.SellPrice)
            sellPrice = decimal.NewFromInt(1).Div(fiatPrice.BuyPrice)
        } else {
            // USDT/CNY: 1 USDT = X CNY
            buyPrice = fiatPrice.BuyPrice
            sellPrice = fiatPrice.SellPrice
        }
        
        // 格式化价格显示
        buyText := service.I18n().Tf(ctx, "{#SwapCNYBuyPrice}",
            baseSymbol, buyPrice.StringFixed(4), quoteSymbol)
        sellText := service.I18n().Tf(ctx, "{#SwapCNYSellPrice}",
            baseSymbol, sellPrice.StringFixed(4), quoteSymbol)
    }
}
```

### 加密货币价格处理
```go
// internal/bot/swap/handler.go:517-546
// 转换符号格式：ETH/USDT → ETHUSDT
priceSymbol := strings.ReplaceAll(product.Symbol, "/", "")
priceData, err := priceClient.GetRealTimePrice(ctx, priceSymbol)

// 根据代币确定小数位数
var decimals int32
switch product.BaseToken {
case "ETH", "BTC":
    decimals = 2
case "TRX":
    decimals = 4
default:
    decimals = 4
}

priceText := service.I18n().Tf(ctx, "{#SwapCurrentPrice}",
    product.BaseToken,
    priceData.Price.StringFixed(decimals),
    product.QuoteToken)
```

## 6. 滑点保护实现

```go
// internal/service/v2/impl/swap_service.go:952-999
// 计算滑点
priceSlippage := currentPrice.Sub(order.Price).Div(order.Price).Abs()

// 获取最大允许滑点（默认1%）
maxSlippage := decimal.NewFromFloat(0.01)
if s.configMgr != nil {
    config, err := s.configMgr.GetConfig(ctx)
    if err == nil && config.MaxSlippage.GreaterThan(decimal.Zero) {
        maxSlippage = config.MaxSlippage
    }
}

// 检查滑点
if priceSlippage.GreaterThan(maxSlippage) {
    // 更新订单状态为失败
    tx.Model(&entity.ExchangeOrders{}).
        Where("order_sn = ?", orderID).
        Data(g.Map{
            "status":        "failed",
            "error_message": fmt.Sprintf("Price slippage too high: %s%%", 
                           priceSlippage.Mul(decimal.NewFromInt(100)).String()),
        }).Update()
    
    return nil, fmt.Errorf("price slippage too high: %s%%", 
                          priceSlippage.Mul(decimal.NewFromInt(100)).String())
}
```

## 7. 状态管理示例

```go
// 用户状态定义
type UserState struct {
    State   string            `json:"state"`
    Context map[string]string `json:"context"`
}

// 状态常量
const (
    StateSwapSelectingProduct = "swap_selecting_product"
    StateSwapInputAmount      = "swap_input_amount"
    StateSwapConfirmQuote     = "swap_confirm_quote"
    StateSwapInputPassword    = "swap_input_password"
)

// 状态转换示例
userState.State = consts.StateSwapInputAmount
userState.Context[consts.SwapParamProductID] = gconv.String(productID)
userState.Context[consts.SwapParamDirection] = direction
userState.Context[consts.SwapParamFromTokenID] = gconv.String(fromTokenID)
userState.Context[consts.SwapParamToTokenID] = gconv.String(toTokenID)

err = service.UserState().SetUserState(ctx, query.From.ID, userState)
```