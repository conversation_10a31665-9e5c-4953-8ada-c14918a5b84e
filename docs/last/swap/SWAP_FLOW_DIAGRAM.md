# 闪兑功能流程图

## 用户操作流程

```mermaid
graph TD
    A[用户点击闪兑] --> B{检查支付密码}
    B -->|未设置| C[提示设置密码]
    B -->|已设置| D[显示可用交易对]
    
    D --> E[选择交易对<br/>如: ETH/USDT]
    E --> F[选择交易方向]
    
    F --> G[买入 ETH<br/>支付 USDT]
    F --> H[卖出 ETH<br/>获得 USDT]
    
    G --> I[输入USDT金额]
    H --> J[输入ETH金额]
    
    I --> K[生成报价]
    J --> K
    
    K --> L{用户确认报价}
    L -->|取消| M[返回主菜单]
    L -->|确认| N[输入支付密码]
    
    N --> O{密码验证}
    O -->|失败| P[提示密码错误]
    O -->|成功| Q[创建订单]
    
    Q --> R[执行兑换]
    R --> S{兑换结果}
    S -->|成功| T[显示成功信息]
    S -->|失败| U[显示失败原因]
    
    T --> V[查看订单详情/再次兑换/返回主菜单]
```

## 报价生成流程

```mermaid
graph LR
    A[接收用户请求] --> B[确定交易方向和产品]
    B --> C{是否为CNY交易对}
    
    C -->|是| D[调用法币价格API]
    C -->|否| E[调用加密货币价格API]
    
    D --> F[获取买入/卖出价]
    E --> G[获取实时市场价]
    
    F --> H[应用价差]
    G --> H
    
    H --> I[计算兑换金额]
    I --> J[验证金额限制]
    J --> K[计算手续费]
    K --> L[生成报价对象]
    L --> M[缓存报价120秒]
    M --> N[返回报价给用户]
```

## 订单执行流程

```mermaid
graph TD
    A[接收执行请求] --> B[获取分布式锁]
    B --> C[开启数据库事务]
    C --> D[获取订单并加锁]
    
    D --> E[验证订单状态]
    E --> F[获取最新价格]
    F --> G{检查滑点}
    
    G -->|超过限制| H[标记订单失败]
    G -->|正常| I[更新订单为处理中]
    
    I --> J[扣除用户支付代币]
    J --> K{扣款是否成功}
    
    K -->|失败| L[回滚事务<br/>标记订单失败]
    K -->|成功| M[增加用户收到代币]
    
    M --> N[更新订单为已完成]
    N --> O[提交事务]
    O --> P[释放分布式锁]
    P --> Q[记录交易指标]
    Q --> R[返回执行结果]
```

## 价格计算示例

### 买入场景（Buy）
用户想用 USDT 买入 ETH：

```
市场价格: 1 ETH = 2,450 USDT
价差率: 0.2%
买入价格 = 2,450 × (1 + 0.002) = 2,454.90 USDT

用户输入: 想花费 245.49 USDT
可买入 ETH = 245.49 ÷ 2,454.90 = 0.1 ETH

手续费率: 0.2%（从输出扣除）
手续费 = 0.1 × 0.002 = 0.0002 ETH
实际收到 = 0.1 - 0.0002 = 0.0998 ETH
```

### 卖出场景（Sell）
用户想卖出 ETH 换取 USDT：

```
市场价格: 1 ETH = 2,450 USDT
价差率: 0.2%
卖出价格 = 2,450 × (1 - 0.002) = 2,445.10 USDT

用户输入: 想卖出 0.1 ETH
可获得 USDT = 0.1 × 2,445.10 = 244.51 USDT

手续费率: 0.2%（从输出扣除）
手续费 = 244.51 × 0.002 = 0.49 USDT
实际收到 = 244.51 - 0.49 = 244.02 USDT
```

## 状态转换图

```mermaid
stateDiagram-v2
    [*] --> 选择产品: 用户点击闪兑
    选择产品 --> 选择方向: 选择交易对
    选择方向 --> 输入金额: 选择买入/卖出
    输入金额 --> 确认报价: 输入有效金额
    确认报价 --> 输入密码: 用户确认
    确认报价 --> [*]: 用户取消
    输入密码 --> 执行中: 密码正确
    输入密码 --> 输入密码: 密码错误
    执行中 --> 完成: 兑换成功
    执行中 --> 失败: 兑换失败
    完成 --> [*]
    失败 --> [*]
```

## 错误处理流程

```mermaid
graph TD
    A[捕获错误] --> B{错误类型}
    
    B -->|金额错误| C[无效金额<br/>金额过小<br/>金额过大]
    B -->|余额不足| D[检查用户余额<br/>考虑手续费]
    B -->|报价过期| E[报价超过120秒<br/>需重新生成]
    B -->|滑点超限| F[价格变动过大<br/>超过1%限制]
    B -->|系统错误| G[服务不可用<br/>网络错误]
    
    C --> H[显示具体限额要求]
    D --> I[显示余额和所需金额]
    E --> J[引导用户重新操作]
    F --> K[显示价格变动幅度]
    G --> L[显示通用错误信息]
    
    H --> M[返回用户界面]
    I --> M
    J --> M
    K --> M
    L --> M
```