# Swap Feature Deployment Guide

## Pre-Deployment Checklist

### 1. Code Quality Verification
- [ ] All unit tests passing
- [ ] Integration tests completed
- [ ] Code coverage > 80%
- [ ] No critical linting issues
- [ ] Security scan completed
- [ ] Code review approved by 2+ reviewers

### 2. Database Preparation
```bash
# Run all migration scripts in order
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME < docs/swap/sql/exchange_tables.sql
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME < docs/swap/sql/security_tables.sql
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME < docs/swap/sql/metrics_tables.sql

# Verify tables created
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "
SELECT table_name FROM information_schema.tables 
WHERE table_schema = '$DB_NAME' 
AND table_name LIKE 'exchange_%' OR table_name LIKE 'swap_%'
ORDER BY table_name;"

# Create indexes for performance
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "
-- Add composite indexes for common queries
CREATE INDEX idx_orders_user_status_created ON exchange_orders(user_id, status, created_at);
CREATE INDEX idx_orders_product_created ON exchange_orders(product_id, created_at);
CREATE INDEX idx_metrics_user_created ON swap_metrics(user_id, created_at);
"
```

### 3. Initial Product Configuration
```sql
-- Insert initial swap products (example)
INSERT INTO exchange_products (
    symbol, name, base_symbol, quote_symbol, 
    base_token_id, quote_token_id,
    min_base_amount_per_tx, max_base_amount_per_tx,
    fee_rate, spread_rate, fee_charged_in,
    is_active, allow_buy, allow_sell,
    display_order, status
) VALUES 
    ('ETH/USDT', 'Ethereum to Tether', 'ETH', 'USDT', 2, 3, 
     '0.01', '10', '0.002', '0.001', 'quote',
     1, 1, 1, 1, 1),
    ('BTC/USDT', 'Bitcoin to Tether', 'BTC', 'USDT', 1, 3,
     '0.0001', '1', '0.002', '0.001', 'quote',
     1, 1, 1, 2, 1),
    ('USDT/TON', 'Tether to Toncoin', 'USDT', 'TON', 3, 4,
     '10', '10000', '0.002', '0.001', 'base',
     1, 1, 1, 3, 1);
```

### 4. Configuration Files Update
```yaml
# config.yaml additions
swap:
  enabled: false  # Start with feature disabled
  maintenance_mode: false
  global_daily_limit_usd: 1000000
  user_daily_limit_usd: 10000
  min_order_usd: 10
  max_order_usd: 50000
  quote_expiry_seconds: 30
  max_slippage: 0.02  # 2%
  
security:
  risk_threshold: 70
  max_velocity_per_hour: 10
  require_kyc_above_usd: 5000
  anomaly_detection_enabled: true
  
metrics:
  retention_days: 90
  aggregation_interval: 300  # 5 minutes
```

## Deployment Steps

### Stage 1: Staging Environment

#### 1.1 Deploy Code
```bash
# Tag the release
git tag -a v1.0.0-swap -m "Swap feature release"
git push origin v1.0.0-swap

# Deploy to staging
./deploy.sh staging v1.0.0-swap
```

#### 1.2 Staging Tests
```bash
# Run smoke tests
go test ./test/integration/swap/... -tags=staging

# Test bot commands
/swap - Verify swap menu appears
/metrics - Verify metrics display (admin only)

# Test with real price feeds
# Monitor for 30 minutes
```

#### 1.3 Load Testing
```bash
# Run load test simulation
go run ./test/load/swap_load_test.go \
  -users=100 \
  -duration=10m \
  -rps=10
```

### Stage 2: Production Deployment

#### 2.1 Pre-Production Steps
```bash
# Backup database
mysqldump -h $PROD_DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME > backup_$(date +%Y%m%d_%H%M%S).sql

# Verify rollback scripts
./scripts/swap_rollback.sh --dry-run
```

#### 2.2 Gradual Rollout

##### Phase 1: Internal Testing (Day 1)
```yaml
# Enable for internal users only
swap:
  enabled: true
  whitelist_users: [123456789, 987654321]  # Admin Telegram IDs
```

##### Phase 2: Limited Release (Day 3)
```yaml
# Enable for 10% of users
swap:
  enabled: true
  rollout_percentage: 10
  whitelist_users: []
```

##### Phase 3: Expanded Release (Day 5)
```yaml
# Enable for 50% of users
swap:
  enabled: true
  rollout_percentage: 50
```

##### Phase 4: Full Release (Day 7)
```yaml
# Enable for all users
swap:
  enabled: true
  rollout_percentage: 100
```

### Stage 3: Monitoring

#### 3.1 Key Metrics to Monitor
- Success rate (target: >98%)
- Average processing time (<500ms)
- Error rate (<1%)
- Security events count
- Volume trends

#### 3.2 Alerts Configuration
```yaml
alerts:
  - name: swap_success_rate_low
    condition: success_rate < 95
    duration: 5m
    channel: "#ops-critical"
    
  - name: swap_high_error_rate
    condition: error_rate > 5
    duration: 2m
    channel: "#ops-critical"
    
  - name: swap_service_down
    condition: health_check_failed
    duration: 1m
    channel: "#ops-critical"
```

## Rollback Plan

### Immediate Rollback (Feature Flag)
```yaml
# Disable swap feature immediately
swap:
  enabled: false
  maintenance_mode: true
  maintenance_message: "Swap service is temporarily unavailable"
```

### Database Rollback
```bash
# Only if necessary - removes all swap data
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME < scripts/swap_rollback.sql
```

### Code Rollback
```bash
# Revert to previous version
git checkout v0.9.0  # Previous stable version
./deploy.sh production v0.9.0
```

## Post-Deployment Tasks

### 1. Verification
- [ ] All swap products visible in bot
- [ ] Quote generation working
- [ ] Order execution successful
- [ ] Metrics collecting properly
- [ ] Alerts functioning
- [ ] Security checks active

### 2. Performance Baseline
```sql
-- Capture baseline metrics
INSERT INTO deployment_metrics (
    deployment_id,
    version,
    avg_response_time,
    success_rate,
    error_count,
    timestamp
)
SELECT 
    'swap-v1.0.0',
    'v1.0.0-swap',
    AVG(execution_latency),
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*),
    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END),
    NOW()
FROM exchange_orders
WHERE created_at > NOW() - INTERVAL 1 HOUR;
```

### 3. Documentation Updates
- [ ] Update user documentation
- [ ] Update API documentation
- [ ] Update operations runbook
- [ ] Create troubleshooting guide
- [ ] Update monitoring dashboards

### 4. Communication
```
📢 Swap Feature Launch Communication Plan

Internal Team (Day 0):
- Engineering: Technical details and monitoring
- Support: Feature overview and FAQ
- Operations: Alerts and escalation

Users (Day 1):
- Announcement in bot news channel
- Feature tutorial video/guide
- Support documentation link
```

## Emergency Procedures

### Service Degradation
```yaml
# Reduce limits during high load
swap:
  user_daily_limit_usd: 1000  # Reduced from 10000
  max_orders_per_minute: 10   # Rate limiting
```

### Emergency Stop
```bash
# Complete feature shutdown
curl -X POST https://admin.api/swap/emergency-stop \
  -H "Authorization: Bearer $ADMIN_TOKEN"
```

### Incident Response
1. Acknowledge alert within 5 minutes
2. Assess impact and severity
3. Implement immediate mitigation
4. Communicate status to stakeholders
5. Root cause analysis within 24 hours

## Success Criteria

### Technical Metrics
- Uptime: >99.9%
- Response time: p95 < 500ms
- Error rate: <0.1%
- Success rate: >98%

### Business Metrics
- Daily active swappers: >100
- Daily volume: >$50,000
- User satisfaction: >4.5/5
- Support tickets: <5% of transactions

## Appendix: Useful Commands

### Health Check
```bash
curl https://api.bot/health/swap
```

### Metrics Summary
```bash
mysql -e "
SELECT 
    COUNT(*) as total_orders,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful,
    SUM(amount_quote) as volume_usd,
    AVG(processing_time) as avg_time_ms
FROM exchange_orders
WHERE created_at > NOW() - INTERVAL 1 DAY;
"
```

### Quick Diagnostics
```bash
# Check service logs
tail -f /var/log/bot/swap.log | grep ERROR

# Check Redis connectivity
redis-cli ping

# Check price service
curl https://price.api/health
```