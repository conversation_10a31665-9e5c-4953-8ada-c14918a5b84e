# Deployment Guide for Swap Feature

## 1. Overview

This guide provides step-by-step instructions for deploying the swap feature to production, including pre-deployment checks, deployment procedures, rollback strategies, and post-deployment verification.

## 2. Pre-Deployment Checklist

### 2.1 Code Readiness

```bash
#!/bin/bash
# Pre-deployment validation script

echo "=== Swap Feature Pre-Deployment Checklist ==="

# 1. Code compilation
echo "✓ Checking code compilation..."
go build -o /tmp/swap-test ./cmd/main.go || exit 1

# 2. Run tests
echo "✓ Running unit tests..."
go test ./internal/logic/swap/... -v || exit 1
go test ./internal/service/swap/... -v || exit 1
go test ./internal/bot/swap/... -v || exit 1

# 3. Lint check
echo "✓ Running linter..."
golangci-lint run ./... || exit 1

# 4. Security scan
echo "✓ Running security scan..."
gosec ./... || exit 1

# 5. Check dependencies
echo "✓ Checking dependencies..."
go mod verify || exit 1

# 6. Database migrations ready
echo "✓ Checking database migrations..."
ls -la migrations/swap/*.sql || exit 1

echo "=== All checks passed ✓ ==="
```

### 2.2 Configuration Checklist

- [ ] Database configuration reviewed
- [ ] Redis configuration set
- [ ] Price service endpoints configured
- [ ] Feature flags configured
- [ ] Environment variables documented
- [ ] Secrets management configured
- [ ] Monitoring endpoints configured
- [ ] Alert channels configured

### 2.3 Infrastructure Readiness

```yaml
# Infrastructure requirements
infrastructure:
  database:
    - MySQL 8.0+ with swap tables created
    - Read replicas configured
    - Backup strategy in place
    
  cache:
    - Redis 6.0+ cluster
    - Persistence configured
    - Memory allocation sufficient
    
  monitoring:
    - Prometheus configured
    - Grafana dashboards imported
    - Alert Manager rules deployed
    
  services:
    - Price Monitor Service running
    - Wallet Service updated
    - User Service compatible
```

## 3. Database Deployment

### 3.1 Migration Scripts

```sql
-- migrations/001_create_exchange_products.sql
CREATE TABLE IF NOT EXISTS `exchange_products` (
  `product_id` int unsigned NOT NULL AUTO_INCREMENT,
  `base_token_id` int unsigned NOT NULL,
  `quote_token_id` int unsigned NOT NULL,
  `symbol` varchar(45) NOT NULL,
  `product_type` varchar(20) NOT NULL DEFAULT 'swap',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `allow_buy` tinyint(1) NOT NULL DEFAULT '1',
  `allow_sell` tinyint(1) NOT NULL DEFAULT '1',
  `min_base_amount_per_tx` decimal(40,18) NOT NULL DEFAULT '0.000000000000000000',
  `max_base_amount_per_tx` decimal(40,18) DEFAULT NULL,
  `fee_rate` decimal(10,8) NOT NULL DEFAULT '0.00200000',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`product_id`),
  UNIQUE KEY `uq_symbol` (`symbol`),
  UNIQUE KEY `uq_base_quote` (`base_token_id`,`quote_token_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- migrations/002_create_exchange_orders.sql
CREATE TABLE IF NOT EXISTS `exchange_orders` (
  `order_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(64) NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `product_id` int unsigned NOT NULL,
  -- ... (full schema)
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uq_order_sn` (`order_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.2 Migration Execution

```bash
#!/bin/bash
# Database migration script

DB_HOST=${DB_HOST:-localhost}
DB_USER=${DB_USER:-root}
DB_NAME=${DB_NAME:-telegram_bot}

echo "Executing swap feature database migrations..."

# Run migrations
for migration in migrations/swap/*.sql; do
    echo "Applying $migration..."
    mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME < $migration
    if [ $? -eq 0 ]; then
        echo "✓ $migration applied successfully"
    else
        echo "✗ Failed to apply $migration"
        exit 1
    fi
done

# Verify tables created
echo "Verifying tables..."
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
    SHOW TABLES LIKE 'exchange%';
    SELECT COUNT(*) as product_count FROM exchange_products;
    SELECT COUNT(*) as order_count FROM exchange_orders;
"

echo "Database migrations completed successfully"
```

### 3.3 Initial Data Seeding

```bash
#!/bin/bash
# Seed initial swap products

mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME << EOF
-- Insert default swap products
INSERT INTO exchange_products 
(base_token_id, quote_token_id, symbol, fee_rate, min_base_amount_per_tx, max_base_amount_per_tx) 
VALUES
(2, 1, 'ETH/USDT', 0.002, '0.001', '100'),
(1, 2, 'USDT/ETH', 0.002, '10', '100000'),
(3, 1, 'BTC/USDT', 0.002, '0.0001', '10'),
(1, 3, 'USDT/BTC', 0.002, '100', '500000');

-- Verify products
SELECT * FROM exchange_products;
EOF
```

## 4. Application Deployment

### 4.1 Deployment Strategy

```yaml
# Kubernetes deployment strategy
apiVersion: apps/v1
kind: Deployment
metadata:
  name: telegram-bot-swap
  labels:
    app: telegram-bot
    feature: swap
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: telegram-bot
      feature: swap
  template:
    metadata:
      labels:
        app: telegram-bot
        feature: swap
    spec:
      containers:
      - name: bot
        image: telegram-bot:v2.0.0-swap
        env:
        - name: SWAP_ENABLED
          value: "true"
        - name: SWAP_PRICE_SERVICE_URL
          value: "redis://price-monitor:6379"
        ports:
        - containerPort: 8080
        - containerPort: 9090  # Metrics
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

### 4.2 Feature Flag Deployment

```bash
#!/bin/bash
# Enable swap feature progressively

# Stage 1: Enable for internal testing (1% of users)
curl -X POST http://admin-api/features \
  -H "Content-Type: application/json" \
  -d '{
    "feature": "swap",
    "enabled": true,
    "rollout_percentage": 1,
    "whitelist_users": ["admin1", "admin2", "tester1"]
  }'

# Stage 2: Increase to 10% after validation
curl -X PATCH http://admin-api/features/swap \
  -H "Content-Type: application/json" \
  -d '{"rollout_percentage": 10}'

# Stage 3: Full rollout
curl -X PATCH http://admin-api/features/swap \
  -H "Content-Type: application/json" \
  -d '{"rollout_percentage": 100}'
```

### 4.3 Configuration Deployment

```bash
#!/bin/bash
# Deploy configuration to ConfigMap

kubectl create configmap swap-config --from-file=config/swap.yaml

# Apply configuration
kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: swap-config
data:
  swap.yaml: |
    swap:
      enabled: true
      quote:
        default_ttl: "30s"
        max_ttl: "60s"
      price:
        max_stale_time: "45s"
        default_source: "binance_feed"
      limits:
        min_amount_usd: 1.0
        max_amount_usd: 50000.0
      fees:
        default_rate: 0.002
EOF
```

## 5. Deployment Procedures

### 5.1 Blue-Green Deployment

```bash
#!/bin/bash
# Blue-Green deployment script

# Current version (Blue)
BLUE_VERSION="v1.9.0"
# New version (Green)
GREEN_VERSION="v2.0.0-swap"

echo "Starting Blue-Green deployment..."

# 1. Deploy Green environment
kubectl apply -f deployment-green.yaml
kubectl wait --for=condition=ready pod -l version=$GREEN_VERSION

# 2. Run smoke tests on Green
./scripts/smoke-test.sh $GREEN_VERSION
if [ $? -ne 0 ]; then
    echo "Smoke tests failed, aborting deployment"
    kubectl delete -f deployment-green.yaml
    exit 1
fi

# 3. Switch traffic to Green
kubectl patch service telegram-bot -p '{"spec":{"selector":{"version":"'$GREEN_VERSION'"}}}'

# 4. Monitor for 5 minutes
echo "Monitoring new deployment..."
sleep 300

# 5. Check error rates
ERROR_RATE=$(curl -s http://prometheus:9090/api/v1/query?query=rate(swap_error_total[5m]) | jq '.data.result[0].value[1]')
if (( $(echo "$ERROR_RATE > 0.05" | bc -l) )); then
    echo "High error rate detected, rolling back..."
    kubectl patch service telegram-bot -p '{"spec":{"selector":{"version":"'$BLUE_VERSION'"}}}'
    exit 1
fi

# 6. Remove Blue deployment
echo "Deployment successful, removing old version..."
kubectl delete deployment telegram-bot-blue

echo "Blue-Green deployment completed successfully"
```

### 5.2 Canary Deployment

```yaml
# Canary deployment with Istio
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: telegram-bot-swap
spec:
  hosts:
  - telegram-bot
  http:
  - match:
    - headers:
        canary:
          exact: "true"
    route:
    - destination:
        host: telegram-bot
        subset: v2-swap
      weight: 100
  - route:
    - destination:
        host: telegram-bot
        subset: v1-stable
      weight: 90
    - destination:
        host: telegram-bot
        subset: v2-swap
      weight: 10  # 10% canary traffic
```

## 6. Health Checks

### 6.1 Application Health Endpoints

```go
// Health check endpoints
func RegisterHealthChecks(router *gin.Engine) {
    router.GET("/health", func(c *gin.Context) {
        health := checkHealth()
        if health.Status == "healthy" {
            c.JSON(200, health)
        } else {
            c.JSON(503, health)
        }
    })
    
    router.GET("/health/swap", func(c *gin.Context) {
        swapHealth := checkSwapHealth()
        c.JSON(200, swapHealth)
    })
}

func checkSwapHealth() HealthStatus {
    checks := []HealthCheck{
        {Name: "database", Check: checkDatabaseConnection},
        {Name: "redis", Check: checkRedisConnection},
        {Name: "price_service", Check: checkPriceService},
        {Name: "swap_products", Check: checkSwapProducts},
    }
    
    status := HealthStatus{
        Service: "swap",
        Status:  "healthy",
        Checks:  make(map[string]string),
    }
    
    for _, check := range checks {
        if err := check.Check(); err != nil {
            status.Status = "unhealthy"
            status.Checks[check.Name] = err.Error()
        } else {
            status.Checks[check.Name] = "ok"
        }
    }
    
    return status
}
```

### 6.2 Deployment Verification

```bash
#!/bin/bash
# Post-deployment verification script

echo "=== Swap Feature Deployment Verification ==="

# 1. Check service health
echo "Checking service health..."
curl -s http://localhost:8080/health/swap | jq '.'

# 2. Verify database
echo "Verifying database..."
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
    SELECT COUNT(*) as products FROM exchange_products WHERE is_active = 1;
    SELECT COUNT(*) as recent_orders FROM exchange_orders 
    WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR);
"

# 3. Check Redis connectivity
echo "Checking Redis..."
redis-cli -h $REDIS_HOST ping

# 4. Verify price data
echo "Checking price data..."
redis-cli -h $REDIS_HOST GET "price:realtime:ETHUSDT" | jq '.'

# 5. Test swap endpoint
echo "Testing swap API..."
curl -X POST http://localhost:8080/api/v1/swap/quote \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1,
    "from_token_id": 2,
    "to_token_id": 1,
    "amount": "0.1"
  }' | jq '.'

# 6. Check metrics
echo "Checking metrics..."
curl -s http://localhost:9090/metrics | grep swap_

echo "=== Verification completed ==="
```

## 7. Rollback Procedures

### 7.1 Quick Rollback

```bash
#!/bin/bash
# Emergency rollback script

PREVIOUS_VERSION=${1:-v1.9.0}

echo "!!! EMERGENCY ROLLBACK TO $PREVIOUS_VERSION !!!"

# 1. Switch traffic immediately
kubectl set image deployment/telegram-bot bot=telegram-bot:$PREVIOUS_VERSION

# 2. Disable swap feature
curl -X PATCH http://admin-api/features/swap \
  -H "Content-Type: application/json" \
  -d '{"enabled": false}'

# 3. Scale down new version
kubectl scale deployment telegram-bot-v2 --replicas=0

# 4. Clear cache
redis-cli -h $REDIS_HOST --scan --pattern "swap:*" | xargs redis-cli -h $REDIS_HOST DEL

# 5. Notify team
./scripts/notify-rollback.sh "Swap feature rolled back to $PREVIOUS_VERSION"

echo "Rollback completed"
```

### 7.2 Database Rollback

```sql
-- Rollback script for database changes
-- CAUTION: This will lose data!

-- Backup tables first
CREATE TABLE exchange_products_backup AS SELECT * FROM exchange_products;
CREATE TABLE exchange_orders_backup AS SELECT * FROM exchange_orders;

-- If needed to rollback:
-- DROP TABLE exchange_products;
-- DROP TABLE exchange_orders;

-- Or disable without dropping:
UPDATE exchange_products SET is_active = 0;
```

## 8. Monitoring Setup

### 8.1 Deploy Monitoring

```bash
#!/bin/bash
# Deploy monitoring configuration

# 1. Deploy Prometheus rules
kubectl apply -f monitoring/prometheus-rules-swap.yaml

# 2. Import Grafana dashboards
curl -X POST http://grafana:3000/api/dashboards/db \
  -H "Authorization: Bearer $GRAFANA_TOKEN" \
  -H "Content-Type: application/json" \
  -d @monitoring/swap-dashboard.json

# 3. Configure alerts
kubectl apply -f monitoring/alertmanager-config-swap.yaml

# 4. Test alert routing
./scripts/test-alert.sh swap_high_error_rate
```

### 8.2 Initial Monitoring

```bash
# Monitor deployment for first hour
watch -n 10 'curl -s http://prometheus:9090/api/v1/query?query=up{job="telegram-bot"} | jq ".data.result[].value[1]"'

# Check error rates
watch -n 30 'curl -s http://prometheus:9090/api/v1/query?query=rate(swap_error_total[5m]) | jq ".data.result[].value[1]"'

# Monitor transaction volume
watch -n 60 'curl -s http://prometheus:9090/api/v1/query?query=rate(swap_transaction_total[5m]) | jq ".data.result[].value[1]"'
```

## 9. Post-Deployment Tasks

### 9.1 Performance Tuning

```bash
#!/bin/bash
# Performance tuning after deployment

# 1. Analyze slow queries
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -e "
    SELECT * FROM performance_schema.events_statements_summary_by_digest 
    WHERE SCHEMA_NAME = 'telegram_bot' 
    AND DIGEST_TEXT LIKE '%exchange%' 
    ORDER BY SUM_TIMER_WAIT DESC 
    LIMIT 10;
"

# 2. Optimize Redis memory
redis-cli -h $REDIS_HOST INFO memory

# 3. Adjust connection pools based on load
kubectl set env deployment/telegram-bot \
  DB_MAX_CONNECTIONS=100 \
  REDIS_POOL_SIZE=50
```

### 9.2 Security Hardening

```bash
#!/bin/bash
# Post-deployment security tasks

# 1. Rotate secrets
kubectl create secret generic swap-secrets \
  --from-literal=db-password=$(openssl rand -base64 32) \
  --from-literal=redis-password=$(openssl rand -base64 32) \
  --dry-run=client -o yaml | kubectl apply -f -

# 2. Enable audit logging
kubectl patch deployment telegram-bot -p '
{
  "spec": {
    "template": {
      "spec": {
        "containers": [{
          "name": "bot",
          "env": [{
            "name": "AUDIT_LOG_ENABLED",
            "value": "true"
          }]
        }]
      }
    }
  }
}'

# 3. Configure network policies
kubectl apply -f security/network-policy-swap.yaml
```

## 10. Deployment Checklist

### 10.1 Pre-Deployment
- [ ] Code review completed
- [ ] Tests passing (unit, integration, e2e)
- [ ] Security scan completed
- [ ] Documentation updated
- [ ] Database migrations tested
- [ ] Configuration reviewed
- [ ] Rollback plan prepared
- [ ] Team notified

### 10.2 Deployment
- [ ] Database migrations executed
- [ ] Configuration deployed
- [ ] Application deployed (canary/blue-green)
- [ ] Health checks passing
- [ ] Smoke tests completed
- [ ] Monitoring active
- [ ] Feature flags configured

### 10.3 Post-Deployment
- [ ] Error rates normal
- [ ] Performance metrics acceptable
- [ ] User feedback monitored
- [ ] Security checks completed
- [ ] Documentation published
- [ ] Retrospective scheduled

## 11. Troubleshooting

### 11.1 Common Issues

```bash
# Issue: High error rate after deployment
# Solution: Check logs and rollback if necessary
kubectl logs -l app=telegram-bot,feature=swap --tail=100 | grep ERROR

# Issue: Price data not updating
# Solution: Check price service connection
redis-cli -h $REDIS_HOST MONITOR | grep price

# Issue: Database connection errors
# Solution: Check connection pool settings
kubectl describe pod -l app=telegram-bot | grep -A 10 "Environment"

# Issue: Memory issues
# Solution: Increase resource limits
kubectl top pods -l app=telegram-bot
kubectl set resources deployment telegram-bot -c bot --limits=memory=1Gi
```

### 11.2 Emergency Contacts

```yaml
oncall:
  primary: "******-0100"
  secondary: "******-0101"
  escalation: "******-0102"
  
slack_channels:
  - "#Swap-deployment"
  - "#platform-oncall"
  - "#security-alerts"
  
documentation:
  - "https://wiki.company.com/swap-runbook"
  - "https://wiki.company.com/incident-response"
```