# Bot Integration for Swap Feature

## 1. Overview

This document describes how the swap feature integrates with the Telegram bot, including message handlers, callback handlers, keyboards, and user interaction flows.

## 2. File Structure

```
internal/bot/swap/
├── init.go                   # Router registration and initialization
├── handler.go               # Main entry point handler
├── keyboards.go             # Keyboard definitions
├── responses.go             # Response message templates
├── callback_handlers.go     # Callback query handlers
├── message_handlers.go      # Text message handlers
├── validators.go            # Input validation
└── state_manager.go         # User state management
```

## 3. Router Registration

### 3.1 init.go

```go
package swap

import (
    "telegram-bot-api/internal/registry"
    "telegram-bot-api/internal/bot/shared"
)

func init() {
    // Register main handler
    registry.RegisterMessageHandler("💱 闪兑", HandleSwapMain)
    registry.RegisterMessageHandler("💱 Swap", HandleSwapMain)
    
    // Register callback handlers
    registry.RegisterCallbackHandler("swap_", HandleSwapCallback)
    
    // Register command handler
    registry.RegisterCommandHandler("swap", HandleSwapCommand)
}

// InitSwapHandlers initializes all swap-related handlers
func InitSwapHandlers() {
    // Initialize state manager
    stateManager = NewSwapStateManager()
    
    // Register sub-handlers
    registerCallbackHandlers()
    registerMessageHandlers()
    
    log.Info("Swap handlers initialized")
}
```

## 4. Main Handler

### 4.1 handler.go

```go
package swap

import (
    "context"
    "fmt"
    
    tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
    "telegram-bot-api/internal/service"
    "telegram-bot-api/internal/model/callback"
)

// HandleSwapMain handles the main swap menu
func HandleSwapMain(ctx context.Context, update *tgbotapi.Update) {
    userID := getUserID(update)
    lang := getUserLanguage(ctx, userID)
    
    // Get active swap products
    swapService := service.SwapService()
    products, err := swapService.GetActiveProducts(ctx)
    if err != nil {
        sendErrorMessage(ctx, update, lang, err)
        return
    }
    
    // Get current prices for display
    priceDisplay := buildPriceDisplay(ctx, products, lang)
    
    // Build and send main menu
    message := formatSwapWelcomeMessage(lang, priceDisplay)
    keyboard := createSwapMainKeyboard(products, lang)
    
    response := &callback.EditResponse{
        MessageID: update.Message.MessageID,
        ChatID:    update.Message.Chat.ID,
        Text:      message,
        Keyboard:  keyboard,
        ParseMode: "HTML",
    }
    
    callback.SendResponse(ctx, response)
}

// HandleSwapCommand handles /swap command
func HandleSwapCommand(ctx context.Context, update *tgbotapi.Update) {
    HandleSwapMain(ctx, update)
}
```

## 5. Callback Handlers

### 5.1 callback_handlers.go

```go
package swap

import (
    "context"
    "strings"
    
    tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
)

// HandleSwapCallback routes swap-related callbacks
func HandleSwapCallback(ctx context.Context, update *tgbotapi.Update) {
    data := update.CallbackQuery.Data
    parts := strings.Split(data, "_")
    
    if len(parts) < 2 {
        return
    }
    
    action := parts[1]
    
    switch action {
    case "pair":
        handleSelectPair(ctx, update)
    case "amount":
        handleEnterAmount(ctx, update)
    case "confirm":
        handleConfirmSwap(ctx, update)
    case "cancel":
        handleCancelSwap(ctx, update)
    case "history":
        handleSwapHistory(ctx, update)
    case "help":
        handleSwapHelp(ctx, update)
    case "back":
        handleBack(ctx, update)
    default:
        log.Warnf("Unknown swap callback action: %s", action)
    }
}

// handleSelectPair handles trading pair selection
func handleSelectPair(ctx context.Context, update *tgbotapi.Update) {
    userID := getUserID(update)
    data := update.CallbackQuery.Data
    
    // Parse selected pair (e.g., "swap_pair_ETH_USDT")
    parts := strings.Split(data, "_")
    if len(parts) != 4 {
        sendAlertResponse(ctx, update, "Invalid selection")
        return
    }
    
    fromSymbol := parts[2]
    toSymbol := parts[3]
    
    // Store user's selection in state
    state := &SwapState{
        UserID:     userID,
        FromSymbol: fromSymbol,
        ToSymbol:   toSymbol,
        Step:       StepEnterAmount,
    }
    stateManager.SetState(userID, state)
    
    // Show amount input prompt
    showAmountInputPrompt(ctx, update, fromSymbol, toSymbol)
}

// handleConfirmSwap handles swap confirmation
func handleConfirmSwap(ctx context.Context, update *tgbotapi.Update) {
    userID := getUserID(update)
    
    // Get user state
    state := stateManager.GetState(userID)
    if state == nil || state.QuoteID == "" {
        sendAlertResponse(ctx, update, "Quote expired, please try again")
        return
    }
    
    // Update state to password verification
    state.Step = StepPasswordVerification
    stateManager.SetState(userID, state)
    
    // Show password prompt
    showPasswordPrompt(ctx, update, state)
}
```

## 6. Message Handlers

### 6.1 message_handlers.go

```go
package swap

import (
    "context"
    "fmt"
    "strings"
    
    "github.com/shopspring/decimal"
    tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
)

// HandleSwapMessage handles text messages during swap flow
func HandleSwapMessage(ctx context.Context, update *tgbotapi.Update) {
    userID := getUserID(update)
    state := stateManager.GetState(userID)
    
    if state == nil {
        return // No active swap session
    }
    
    switch state.Step {
    case StepEnterAmount:
        handleAmountInput(ctx, update, state)
    case StepPasswordVerification:
        handlePasswordInput(ctx, update, state)
    default:
        // Invalid state, clear it
        stateManager.ClearState(userID)
    }
}

// handleAmountInput processes amount input
func handleAmountInput(ctx context.Context, update *tgbotapi.Update) {
    userID := getUserID(update)
    text := strings.TrimSpace(update.Message.Text)
    
    // Validate amount
    amount, err := decimal.NewFromString(text)
    if err != nil || amount.IsNegative() || amount.IsZero() {
        sendErrorMessage(ctx, update, "Invalid amount. Please enter a valid number.")
        return
    }
    
    // Get user state
    state := stateManager.GetState(userID)
    if state == nil {
        return
    }
    
    // Create quote request
    swapService := service.SwapService()
    quoteReq := &service.SwapQuoteRequest{
        UserID:      userID,
        FromSymbol:  state.FromSymbol,
        ToSymbol:    state.ToSymbol,
        Amount:      amount,
        AmountType:  "from", // User specifies from amount
    }
    
    // Get quote
    quote, err := swapService.CreateQuote(ctx, quoteReq)
    if err != nil {
        handleQuoteError(ctx, update, err)
        return
    }
    
    // Update state with quote
    state.Amount = amount
    state.QuoteID = quote.QuoteID
    state.Quote = quote
    state.Step = StepConfirmation
    stateManager.SetState(userID, state)
    
    // Show confirmation screen
    showSwapConfirmation(ctx, update, state, quote)
}

// handlePasswordInput processes payment password
func handlePasswordInput(ctx context.Context, update *tgbotapi.Update) {
    userID := getUserID(update)
    password := update.Message.Text
    
    // Delete the password message immediately for security
    deleteMsg := tgbotapi.NewDeleteMessage(update.Message.Chat.ID, update.Message.MessageID)
    bot.Send(deleteMsg)
    
    // Verify password
    userService := service.UserService()
    if err := userService.VerifyPaymentPassword(ctx, userID, password); err != nil {
        handlePasswordError(ctx, update, err)
        return
    }
    
    // Get state and execute swap
    state := stateManager.GetState(userID)
    if state == nil || state.QuoteID == "" {
        sendErrorMessage(ctx, update, "Session expired, please try again")
        return
    }
    
    // Show processing message
    showProcessingMessage(ctx, update, state)
    
    // Execute swap
    swapService := service.SwapService()
    orderReq := &service.SwapOrderRequest{
        UserID:          userID,
        QuoteID:         state.QuoteID,
        PaymentPassword: password,
    }
    
    result, err := swapService.CreateAndExecuteOrder(ctx, orderReq)
    if err != nil {
        handleSwapError(ctx, update, err, state)
        return
    }
    
    // Clear state
    stateManager.ClearState(userID)
    
    // Show success message
    showSwapSuccess(ctx, update, result)
}
```

## 7. Keyboards

### 7.1 keyboards.go

```go
package swap

import (
    "fmt"
    tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
    "telegram-bot-api/internal/bot/shared"
)

// createSwapMainKeyboard creates the main swap menu keyboard
func createSwapMainKeyboard(products []*SwapProduct, lang string) tgbotapi.InlineKeyboardMarkup {
    var rows [][]tgbotapi.InlineKeyboardButton
    
    // Create trading pair buttons
    for i := 0; i < len(products); i += 2 {
        var row []tgbotapi.InlineKeyboardButton
        
        // First button
        btn1Text := formatPairButton(products[i], lang)
        btn1Data := fmt.Sprintf("swap_pair_%s_%s", 
            products[i].BaseTokenSymbol, 
            products[i].QuoteTokenSymbol)
        row = append(row, tgbotapi.NewInlineKeyboardButtonData(btn1Text, btn1Data))
        
        // Second button (if exists)
        if i+1 < len(products) {
            btn2Text := formatPairButton(products[i+1], lang)
            btn2Data := fmt.Sprintf("swap_pair_%s_%s", 
                products[i+1].BaseTokenSymbol, 
                products[i+1].QuoteTokenSymbol)
            row = append(row, tgbotapi.NewInlineKeyboardButtonData(btn2Text, btn2Data))
        }
        
        rows = append(rows, row)
    }
    
    // Add function buttons
    rows = append(rows, []tgbotapi.InlineKeyboardButton{
        tgbotapi.NewInlineKeyboardButtonData("📊 History", "swap_history"),
        tgbotapi.NewInlineKeyboardButtonData("❓ Help", "swap_help"),
    })
    
    // Add back button
    rows = append(rows, []tgbotapi.InlineKeyboardButton{
        tgbotapi.NewInlineKeyboardButtonData("◀️ Back", "main_menu"),
    })
    
    return tgbotapi.NewInlineKeyboardMarkup(rows...)
}

// createSwapConfirmationKeyboard creates confirmation keyboard
func createSwapConfirmationKeyboard(lang string) tgbotapi.InlineKeyboardMarkup {
    return tgbotapi.NewInlineKeyboardMarkup(
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData("✅ Confirm", "swap_confirm"),
            tgbotapi.NewInlineKeyboardButtonData("❌ Cancel", "swap_cancel"),
        ),
    )
}

// createSwapHistoryKeyboard creates history pagination keyboard
func createSwapHistoryKeyboard(page, totalPages int, lang string) tgbotapi.InlineKeyboardMarkup {
    var rows [][]tgbotapi.InlineKeyboardButton
    
    // Pagination row
    if totalPages > 1 {
        var pageRow []tgbotapi.InlineKeyboardButton
        
        if page > 1 {
            pageRow = append(pageRow, 
                tgbotapi.NewInlineKeyboardButtonData("◀️ Previous", 
                    fmt.Sprintf("swap_history_page_%d", page-1)))
        }
        
        pageRow = append(pageRow, 
            tgbotapi.NewInlineKeyboardButtonData(
                fmt.Sprintf("%d/%d", page, totalPages), 
                "swap_history_current"))
        
        if page < totalPages {
            pageRow = append(pageRow, 
                tgbotapi.NewInlineKeyboardButtonData("▶️ Next", 
                    fmt.Sprintf("swap_history_page_%d", page+1)))
        }
        
        rows = append(rows, pageRow)
    }
    
    // Action buttons
    rows = append(rows, []tgbotapi.InlineKeyboardButton{
        tgbotapi.NewInlineKeyboardButtonData("🆕 New Swap", "swap_main"),
        tgbotapi.NewInlineKeyboardButtonData("◀️ Back", "swap_back"),
    })
    
    return tgbotapi.NewInlineKeyboardMarkup(rows...)
}

// Helper functions
func formatPairButton(product *SwapProduct, lang string) string {
    emoji1 := getTokenEmoji(product.BaseTokenSymbol)
    emoji2 := getTokenEmoji(product.QuoteTokenSymbol)
    return fmt.Sprintf("%s %s → %s %s", 
        emoji1, product.BaseTokenSymbol, 
        emoji2, product.QuoteTokenSymbol)
}

func getTokenEmoji(symbol string) string {
    emojiMap := map[string]string{
        "ETH":  "💎",
        "USDT": "💰",
        "BTC":  "₿",
        "BNB":  "🟡",
    }
    if emoji, ok := emojiMap[symbol]; ok {
        return emoji
    }
    return "🪙"
}
```

## 8. Response Messages

### 8.1 responses.go

```go
package swap

import (
    "fmt"
    "time"
    
    "github.com/shopspring/decimal"
    "telegram-bot-api/internal/service"
)

// Message templates
const (
    SwapWelcomeMessage = `💱 <b>Swap Center</b>

🚀 Instantly exchange your digital assets! Real-time rates, safe and convenient.

<b>📊 Live Prices (USDT)</b>
%s

<b>Select a trading pair to start:</b>`

    SwapAmountPromptMessage = `💰 <b>Enter Swap Amount</b>

<b>Trading Pair:</b> %s → %s
<b>Current Rate:</b> 1 %s = %s %s
<b>Your Balance:</b> %s %s
<b>Min Amount:</b> %s %s
<b>Max Amount:</b> %s %s

Please enter the amount of <b>%s</b> you want to swap:

<i>💡 Tip: Just enter a number, e.g., 0.1</i>`

    SwapConfirmationMessage = `📊 <b>Swap Preview</b>

┌─────────────────────────
│ <b>Swap Details</b>
├─────────────────────────
│ <b>From:</b> %s %s
│ <b>To:</b> ~%s %s
│ <b>Rate:</b> 1 %s ≈ %s %s
│ <b>Fee:</b> %s %s (%s%%)
│ <b>You Receive:</b> <b>%s %s</b>
└─────────────────────────

<b>💡 Important:</b>
⏰ Quote valid for: <code>%d</code> seconds
📈 Slippage protection: ±%s%%
🔒 Funds will be locked during swap

<b>Confirm this swap?</b>`

    SwapPasswordPromptMessage = `🔐 <b>Security Verification</b>

About to swap:
<b>%s %s</b> → <b>%s %s</b>

Please enter your payment password:

<i>🛡️ For your security, ensure your surroundings are safe</i>`

    SwapProcessingMessage = `⏳ <b>Processing Swap...</b>

Executing your swap, please wait...

<b>Order Details:</b>
• Order ID: <code>%s</code>
• Swapping: %s %s → %s %s
• Expected rate: 1 %s ≈ %s %s

<i>📡 Fetching latest rates and executing...</i>`

    SwapSuccessMessage = `✅ <b>Swap Successful!</b>

┌─────────────────────────
│ <b>Transaction Complete</b>
├─────────────────────────
│ <b>Order ID:</b> <code>%s</code>
│ <b>Time:</b> %s
├─────────────────────────
│ <b>Swapped:</b> %s %s
│ <b>Received:</b> %s %s
│ <b>Rate:</b> 1 %s = %s %s
│ <b>Fee:</b> %s %s
│ <b>Slippage:</b> %s%%
└─────────────────────────

<b>💰 Updated Balances:</b>
• %s: %s
• %s: %s

Thank you for using our swap service!`
)

// formatSwapWelcomeMessage formats the welcome message with prices
func formatSwapWelcomeMessage(lang string, priceDisplay string) string {
    return fmt.Sprintf(SwapWelcomeMessage, priceDisplay)
}

// formatSwapConfirmation formats the confirmation message
func formatSwapConfirmation(quote *service.SwapQuote, product *service.SwapProduct) string {
    feePercent := quote.FeeAmount.Div(quote.FromAmount).Mul(decimal.NewFromInt(100))
    
    return fmt.Sprintf(SwapConfirmationMessage,
        quote.FromAmount.String(), quote.FromTokenSymbol,
        quote.ToAmount.String(), quote.ToTokenSymbol,
        quote.FromTokenSymbol, quote.Rate.String(), quote.ToTokenSymbol,
        quote.FeeAmount.String(), quote.FeeTokenSymbol, feePercent.StringFixed(2),
        quote.NetReceiveAmount.String(), quote.ToTokenSymbol,
        quote.ValidSeconds,
        product.AllowedSlippagePercent.Mul(decimal.NewFromInt(100)).StringFixed(2),
    )
}

// buildPriceDisplay builds the price display string
func buildPriceDisplay(ctx context.Context, products []*service.SwapProduct, lang string) string {
    var display string
    
    // Group by base token and fetch prices
    priceMap := make(map[string]*service.PriceData)
    symbols := []string{"ETHUSDT", "BTCUSDT", "BNBUSDT"}
    
    priceService := service.PriceService()
    prices, _ := priceService.GetMultiplePrices(ctx, symbols)
    
    for symbol, price := range prices {
        token := symbol[:len(symbol)-4] // Remove "USDT"
        emoji := getTokenEmoji(token)
        change := formatPriceChange(price.Change24h)
        
        display += fmt.Sprintf("%s <b>%s</b> %s USDT %s\n",
            emoji, token, price.Price.StringFixed(2), change)
    }
    
    return display
}

// formatPriceChange formats 24h price change
func formatPriceChange(change decimal.Decimal) string {
    changePercent := change.Mul(decimal.NewFromInt(100))
    emoji := "📈"
    if change.IsNegative() {
        emoji = "📉"
    }
    return fmt.Sprintf("%s %s%%", emoji, changePercent.StringFixed(2))
}
```

## 9. State Management

### 9.1 state_manager.go

```go
package swap

import (
    "sync"
    "time"
    
    "github.com/shopspring/decimal"
    "telegram-bot-api/internal/service"
)

// SwapStep represents the current step in swap flow
type SwapStep int

const (
    StepSelectPair SwapStep = iota
    StepEnterAmount
    StepConfirmation
    StepPasswordVerification
    StepProcessing
)

// SwapState represents user's swap session state
type SwapState struct {
    UserID     uint64
    FromSymbol string
    ToSymbol   string
    Amount     decimal.Decimal
    QuoteID    string
    Quote      *service.SwapQuote
    Step       SwapStep
    CreatedAt  time.Time
    UpdatedAt  time.Time
}

// SwapStateManager manages user swap states
type SwapStateManager struct {
    states map[uint64]*SwapState
    mu     sync.RWMutex
    ttl    time.Duration
}

var stateManager *SwapStateManager

// NewSwapStateManager creates a new state manager
func NewSwapStateManager() *SwapStateManager {
    sm := &SwapStateManager{
        states: make(map[uint64]*SwapState),
        ttl:    5 * time.Minute,
    }
    
    // Start cleanup routine
    go sm.cleanupExpiredStates()
    
    return sm
}

// SetState sets user's swap state
func (sm *SwapStateManager) SetState(userID uint64, state *SwapState) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    
    state.UpdatedAt = time.Now()
    if state.CreatedAt.IsZero() {
        state.CreatedAt = time.Now()
    }
    
    sm.states[userID] = state
}

// GetState gets user's swap state
func (sm *SwapStateManager) GetState(userID uint64) *SwapState {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    
    state, exists := sm.states[userID]
    if !exists {
        return nil
    }
    
    // Check if state is expired
    if time.Since(state.UpdatedAt) > sm.ttl {
        return nil
    }
    
    return state
}

// ClearState clears user's swap state
func (sm *SwapStateManager) ClearState(userID uint64) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    
    delete(sm.states, userID)
}

// cleanupExpiredStates periodically cleans up expired states
func (sm *SwapStateManager) cleanupExpiredStates() {
    ticker := time.NewTicker(1 * time.Minute)
    defer ticker.Stop()
    
    for range ticker.C {
        sm.mu.Lock()
        now := time.Now()
        
        for userID, state := range sm.states {
            if now.Sub(state.UpdatedAt) > sm.ttl {
                delete(sm.states, userID)
            }
        }
        
        sm.mu.Unlock()
    }
}
```

## 10. Error Handling

### 10.1 Error Response Handlers

```go
// handleQuoteError handles quote creation errors
func handleQuoteError(ctx context.Context, update *tgbotapi.Update, err error) {
    lang := getUserLanguage(ctx, getUserID(update))
    
    var message string
    switch {
    case errors.Is(err, service.ErrAmountTooSmall):
        message = getLocalizedMessage(lang, "swap.error.amount_too_small")
    case errors.Is(err, service.ErrAmountTooLarge):
        message = getLocalizedMessage(lang, "swap.error.amount_too_large")
    case errors.Is(err, service.ErrInsufficientBalance):
        message = getLocalizedMessage(lang, "swap.error.insufficient_balance")
    case errors.Is(err, service.ErrPriceStale):
        message = getLocalizedMessage(lang, "swap.error.price_stale")
    default:
        message = getLocalizedMessage(lang, "swap.error.generic")
    }
    
    sendAlertResponse(ctx, update, message)
}

// handleSwapError handles swap execution errors
func handleSwapError(ctx context.Context, update *tgbotapi.Update, err error, state *SwapState) {
    lang := getUserLanguage(ctx, getUserID(update))
    
    // Clear user state
    stateManager.ClearState(state.UserID)
    
    // Build error message
    errorMsg := formatSwapErrorMessage(lang, err, state)
    
    // Send error response
    response := &callback.EditResponse{
        MessageID: update.CallbackQuery.Message.MessageID,
        ChatID:    update.CallbackQuery.Message.Chat.ID,
        Text:      errorMsg,
        Keyboard:  createSwapErrorKeyboard(lang),
        ParseMode: "HTML",
    }
    
    callback.SendResponse(ctx, response)
}
```

## 11. Integration Testing

### 11.1 Bot Handler Tests

```go
func TestSwapBotFlow(t *testing.T) {
    // Setup test bot
    bot := setupTestBot()
    
    // Test main menu
    update := createTestUpdate("/swap")
    HandleSwapCommand(context.Background(), update)
    
    // Verify response
    response := bot.GetLastResponse()
    assert.Contains(t, response.Text, "Swap Center")
    assert.NotEmpty(t, response.Keyboard)
    
    // Test pair selection
    callbackUpdate := createTestCallback("swap_pair_ETH_USDT")
    HandleSwapCallback(context.Background(), callbackUpdate)
    
    // Verify amount prompt
    response = bot.GetLastResponse()
    assert.Contains(t, response.Text, "Enter Swap Amount")
}
```