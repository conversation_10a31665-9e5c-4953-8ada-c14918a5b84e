-- Swap Security Tables

-- Table for recording all swap attempts for audit and analysis
CREATE TABLE IF NOT EXISTS `swap_attempts` (
    `id` VARCHAR(64) NOT NULL PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `product_id` INT UNSIGNED NOT NULL,
    `quote_id` VARCHAR(64) DEFAULT NULL,
    `order_id` VARCHAR(64) DEFAULT NULL,
    `amount` DECIMAL(36,18) NOT NULL,
    `amount_usd` DECIMAL(36,18) NOT NULL,
    `status` ENUM('initiated', 'validated', 'executed', 'failed', 'blocked') NOT NULL,
    `risk_score` INT NOT NULL DEFAULT 0,
    `security_checks` JSON DEFAULT NULL COMMENT 'Array of security check results',
    `client_ip` VARCHAR(45) DEFAULT NULL,
    `user_agent` TEXT DEFAULT NULL,
    `device_id` VARCHAR(128) DEFAULT NULL,
    `failure_reason` TEXT DEFAULT NULL,
    `blocked_by` VARCHAR(50) DEFAULT NULL COMMENT 'Which security check blocked the attempt',
    `processing_time` INT DEFAULT NULL COMMENT 'Processing time in milliseconds',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_risk_score` (`risk_score`),
    INDEX `idx_quote_id` (`quote_id`),
    INDEX `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for user blacklist
CREATE TABLE IF NOT EXISTS `user_blacklist` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `reason` VARCHAR(255) NOT NULL,
    `blocked_by` VARCHAR(100) DEFAULT NULL COMMENT 'Admin or system that blocked',
    `evidence` TEXT DEFAULT NULL COMMENT 'Supporting evidence or references',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1,
    `expires_at` DATETIME DEFAULT NULL COMMENT 'Optional expiration time',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY `uk_user_active` (`user_id`, `is_active`),
    INDEX `idx_expires_at` (`expires_at`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for user flags (various security and restriction flags)
CREATE TABLE IF NOT EXISTS `user_flags` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `flag_type` VARCHAR(50) NOT NULL COMMENT 'e.g., swap_blocked, suspicious_activity, high_risk',
    `flag_value` VARCHAR(255) DEFAULT NULL,
    `reason` TEXT DEFAULT NULL,
    `metadata` JSON DEFAULT NULL,
    `is_active` TINYINT(1) NOT NULL DEFAULT 1,
    `expires_at` DATETIME DEFAULT NULL,
    `created_by` VARCHAR(100) DEFAULT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_user_flags` (`user_id`, `flag_type`, `is_active`),
    INDEX `idx_flag_type` (`flag_type`),
    INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for anomaly patterns detection
CREATE TABLE IF NOT EXISTS `swap_anomalies` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `anomaly_type` ENUM('velocity', 'pattern', 'amount', 'frequency', 'device', 'location') NOT NULL,
    `severity` ENUM('low', 'medium', 'high', 'critical') NOT NULL,
    `description` TEXT NOT NULL,
    `details` JSON DEFAULT NULL,
    `detected_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `resolved_at` DATETIME DEFAULT NULL,
    `resolution_notes` TEXT DEFAULT NULL,
    
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_anomaly_type` (`anomaly_type`),
    INDEX `idx_severity` (`severity`),
    INDEX `idx_detected_at` (`detected_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for security rules configuration
CREATE TABLE IF NOT EXISTS `swap_security_rules` (
    `id` VARCHAR(50) NOT NULL PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `rule_type` ENUM('limit', 'pattern', 'blacklist', 'whitelist', 'threshold') NOT NULL,
    `enabled` TINYINT(1) NOT NULL DEFAULT 1,
    `priority` INT NOT NULL DEFAULT 50,
    `conditions` JSON NOT NULL COMMENT 'Rule conditions in JSON format',
    `actions` JSON NOT NULL COMMENT 'Actions to take: block, alert, review, limit',
    `description` TEXT DEFAULT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_enabled_priority` (`enabled`, `priority`),
    INDEX `idx_rule_type` (`rule_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for IP address reputation
CREATE TABLE IF NOT EXISTS `ip_reputation` (
    `ip_address` VARCHAR(45) NOT NULL PRIMARY KEY,
    `reputation_score` INT NOT NULL DEFAULT 50 COMMENT 'Score 0-100, higher is better',
    `country_code` CHAR(2) DEFAULT NULL,
    `is_vpn` TINYINT(1) DEFAULT 0,
    `is_proxy` TINYINT(1) DEFAULT 0,
    `is_tor` TINYINT(1) DEFAULT 0,
    `risk_level` ENUM('low', 'medium', 'high', 'blocked') NOT NULL DEFAULT 'low',
    `last_seen` DATETIME NOT NULL,
    `total_attempts` INT NOT NULL DEFAULT 0,
    `blocked_attempts` INT NOT NULL DEFAULT 0,
    `metadata` JSON DEFAULT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_reputation_score` (`reputation_score`),
    INDEX `idx_risk_level` (`risk_level`),
    INDEX `idx_country_code` (`country_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for device fingerprints
CREATE TABLE IF NOT EXISTS `user_devices` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `device_id` VARCHAR(128) NOT NULL,
    `device_type` VARCHAR(50) DEFAULT NULL,
    `device_name` VARCHAR(255) DEFAULT NULL,
    `first_seen` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_seen` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `total_swaps` INT NOT NULL DEFAULT 0,
    `is_trusted` TINYINT(1) NOT NULL DEFAULT 0,
    `risk_score` INT NOT NULL DEFAULT 50,
    `metadata` JSON DEFAULT NULL,
    
    UNIQUE KEY `uk_user_device` (`user_id`, `device_id`),
    INDEX `idx_last_seen` (`last_seen`),
    INDEX `idx_is_trusted` (`is_trusted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default security rules
INSERT INTO `swap_security_rules` (`id`, `name`, `rule_type`, `enabled`, `priority`, `conditions`, `actions`, `description`) VALUES
('max_daily_amount', 'Maximum Daily Amount', 'limit', 1, 100, '{"amount_usd": {"max": 10000}, "period": "24h"}', '["block", "alert"]', 'Block swaps exceeding daily limit'),
('high_risk_score', 'High Risk Score Block', 'threshold', 1, 90, '{"risk_score": {"min": 80}}', '["block", "alert"]', 'Block transactions with high risk score'),
('new_user_limit', 'New User Restriction', 'limit', 1, 80, '{"account_age_days": {"max": 7}, "amount_usd": {"max": 100}}', '["limit"]', 'Limit new users to small amounts'),
('velocity_check', 'Transaction Velocity', 'pattern', 1, 70, '{"max_transactions": 10, "time_window": "1h"}', '["block", "alert"]', 'Block rapid transaction attempts'),
('vpn_restriction', 'VPN/Proxy Detection', 'pattern', 1, 60, '{"ip_type": ["vpn", "proxy", "tor"]}', '["review", "alert"]', 'Flag transactions from VPN/Proxy')
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;