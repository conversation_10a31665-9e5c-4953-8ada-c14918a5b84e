-- Swap Metrics Tables

-- Table for swap metrics data
CREATE TABLE IF NOT EXISTS `swap_metrics` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `order_id` VARCHAR(64) NOT NULL,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `product_id` INT UNSIGNED NOT NULL,
    `amount_usd` DECIMAL(36,18) NOT NULL,
    `fee_usd` DECIMAL(36,18) NOT NULL,
    `risk_score` INT NOT NULL DEFAULT 0,
    `quote_latency` INT DEFAULT NULL COMMENT 'Quote generation time in ms',
    `execution_latency` INT DEFAULT NULL COMMENT 'Order execution time in ms',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX `idx_order_id` (`order_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_product_id` (`product_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for security event metrics
CREATE TABLE IF NOT EXISTS `swap_security_events` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `event_type` VARCHAR(50) NOT NULL COMMENT 'blocked, flagged, anomaly',
    `user_id` BIGINT UNSIGNED NOT NULL,
    `severity` ENUM('low', 'medium', 'high', 'critical') NOT NULL,
    `risk_score` INT NOT NULL DEFAULT 0,
    `blocked_by` VARCHAR(50) DEFAULT NULL,
    `description` TEXT DEFAULT NULL,
    `metadata` JSON DEFAULT NULL,
    `timestamp` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX `idx_event_type` (`event_type`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_severity` (`severity`),
    INDEX `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for aggregated hourly metrics
CREATE TABLE IF NOT EXISTS `swap_metrics_hourly` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `hour` DATETIME NOT NULL,
    `total_quotes` INT NOT NULL DEFAULT 0,
    `total_orders` INT NOT NULL DEFAULT 0,
    `successful_orders` INT NOT NULL DEFAULT 0,
    `failed_orders` INT NOT NULL DEFAULT 0,
    `total_volume_usd` DECIMAL(36,18) NOT NULL DEFAULT 0,
    `total_fees_usd` DECIMAL(36,18) NOT NULL DEFAULT 0,
    `unique_users` INT NOT NULL DEFAULT 0,
    `average_quote_latency` INT DEFAULT NULL,
    `average_execution_latency` INT DEFAULT NULL,
    `p95_quote_latency` INT DEFAULT NULL,
    `p95_execution_latency` INT DEFAULT NULL,
    `error_count` INT NOT NULL DEFAULT 0,
    `security_events` INT NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY `uk_hour` (`hour`),
    INDEX `idx_hour` (`hour`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for aggregated daily metrics
CREATE TABLE IF NOT EXISTS `swap_metrics_daily` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `date` DATE NOT NULL,
    `total_quotes` INT NOT NULL DEFAULT 0,
    `total_orders` INT NOT NULL DEFAULT 0,
    `successful_orders` INT NOT NULL DEFAULT 0,
    `failed_orders` INT NOT NULL DEFAULT 0,
    `total_volume_usd` DECIMAL(36,18) NOT NULL DEFAULT 0,
    `total_fees_usd` DECIMAL(36,18) NOT NULL DEFAULT 0,
    `unique_users` INT NOT NULL DEFAULT 0,
    `new_users` INT NOT NULL DEFAULT 0,
    `product_metrics` JSON DEFAULT NULL COMMENT 'Product-wise breakdown',
    `top_users` JSON DEFAULT NULL COMMENT 'Top users by volume',
    `failure_reasons` JSON DEFAULT NULL COMMENT 'Failure reason breakdown',
    `peak_hour` INT DEFAULT NULL COMMENT 'Hour with most volume',
    `average_order_size_usd` DECIMAL(36,18) DEFAULT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY `uk_date` (`date`),
    INDEX `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for product performance metrics
CREATE TABLE IF NOT EXISTS `swap_product_metrics` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `product_id` INT UNSIGNED NOT NULL,
    `date` DATE NOT NULL,
    `total_orders` INT NOT NULL DEFAULT 0,
    `successful_orders` INT NOT NULL DEFAULT 0,
    `total_volume_base` DECIMAL(36,18) NOT NULL DEFAULT 0,
    `total_volume_usd` DECIMAL(36,18) NOT NULL DEFAULT 0,
    `total_fees_usd` DECIMAL(36,18) NOT NULL DEFAULT 0,
    `unique_users` INT NOT NULL DEFAULT 0,
    `average_spread` DECIMAL(10,6) DEFAULT NULL,
    `average_price` DECIMAL(36,18) DEFAULT NULL,
    `price_volatility` DECIMAL(10,6) DEFAULT NULL,
    `success_rate` DECIMAL(5,2) DEFAULT NULL,
    `average_latency` INT DEFAULT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY `uk_product_date` (`product_id`, `date`),
    INDEX `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for user metrics
CREATE TABLE IF NOT EXISTS `swap_user_metrics` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `date` DATE NOT NULL,
    `total_swaps` INT NOT NULL DEFAULT 0,
    `successful_swaps` INT NOT NULL DEFAULT 0,
    `failed_swaps` INT NOT NULL DEFAULT 0,
    `total_volume_usd` DECIMAL(36,18) NOT NULL DEFAULT 0,
    `total_fees_usd` DECIMAL(36,18) NOT NULL DEFAULT 0,
    `average_order_size_usd` DECIMAL(36,18) DEFAULT NULL,
    `favorite_products` JSON DEFAULT NULL,
    `average_risk_score` DECIMAL(5,2) DEFAULT NULL,
    `devices_used` INT NOT NULL DEFAULT 1,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY `uk_user_date` (`user_id`, `date`),
    INDEX `idx_date` (`date`),
    INDEX `idx_total_volume` (`total_volume_usd`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for system health metrics
CREATE TABLE IF NOT EXISTS `swap_health_metrics` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `timestamp` DATETIME NOT NULL,
    `swap_service_up` BOOLEAN NOT NULL DEFAULT 1,
    `price_service_up` BOOLEAN NOT NULL DEFAULT 1,
    `database_up` BOOLEAN NOT NULL DEFAULT 1,
    `redis_up` BOOLEAN NOT NULL DEFAULT 1,
    `average_response_time` INT DEFAULT NULL COMMENT 'Average response time in ms',
    `error_rate` DECIMAL(5,2) DEFAULT NULL COMMENT 'Error rate percentage',
    `queue_depth` INT DEFAULT NULL,
    `database_connections` INT DEFAULT NULL,
    `redis_memory_usage` BIGINT DEFAULT NULL COMMENT 'Redis memory in bytes',
    `cpu_usage` DECIMAL(5,2) DEFAULT NULL COMMENT 'CPU usage percentage',
    `memory_usage` DECIMAL(5,2) DEFAULT NULL COMMENT 'Memory usage percentage',
    `active_alerts` JSON DEFAULT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for metrics reports
CREATE TABLE IF NOT EXISTS `swap_metrics_reports` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `report_id` VARCHAR(64) NOT NULL,
    `period` VARCHAR(20) NOT NULL COMMENT 'daily, weekly, monthly',
    `start_date` DATE NOT NULL,
    `end_date` DATE NOT NULL,
    `summary` JSON NOT NULL,
    `volume_analysis` JSON DEFAULT NULL,
    `performance_metrics` JSON DEFAULT NULL,
    `security_analysis` JSON DEFAULT NULL,
    `user_analytics` JSON DEFAULT NULL,
    `product_analytics` JSON DEFAULT NULL,
    `recommendations` JSON DEFAULT NULL,
    `generated_at` DATETIME NOT NULL,
    `generated_by` VARCHAR(100) DEFAULT NULL,
    
    UNIQUE KEY `uk_report_id` (`report_id`),
    INDEX `idx_period` (`period`),
    INDEX `idx_generated_at` (`generated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;