# Swap Error Conversion Implementation Plan

## Phase 1: Add New Error Codes

Add the following error codes to `/home/<USER>/bot-api/internal/model/swap_errors.go`:

```go
const (
    // ... existing codes ...
    
    // Product and token errors
    ErrCodeProductNotFound         SwapErrorCode = "PRODUCT_NOT_FOUND"
    ErrCodeTradingPairNotFound     SwapErrorCode = "TRADING_PAIR_NOT_FOUND" 
    ErrCodeTokenNotFound           SwapErrorCode = "TOKEN_NOT_FOUND"
    
    // Configuration errors
    ErrCodeInvalidTradeConfiguration SwapErrorCode = "INVALID_TRADE_CONFIGURATION"
    
    // Authorization errors
    ErrCodeUnauthorizedQuote       SwapErrorCode = "UNAUTHORIZED_QUOTE"
)
```

## Phase 2: Update i18n Files

Add translations to both `en.toml` and `zh-CN.toml`:

### en.toml
```toml
# New swap error messages
SwapErrorProductNotFound = "Trading product not found"
SwapErrorTradingPairNotFound = "This trading pair is not available"
SwapErrorTokenNotFound = "Token not found"
SwapErrorInvalidTradeConfiguration = "Invalid trade configuration"
SwapErrorUnauthorizedQuote = "This quote does not belong to you"
```

### zh-CN.toml
```toml
# New swap error messages
SwapErrorProductNotFound = "未找到交易产品"
SwapErrorTradingPairNotFound = "该交易对不可用"
SwapErrorTokenNotFound = "未找到代币"
SwapErrorInvalidTradeConfiguration = "无效的交易配置"
SwapErrorUnauthorizedQuote = "此报价不属于您"
```

## Phase 3: High Priority Conversions

### 1. Insufficient Balance (swap_service.go:1207)
```go
// Before:
return nil, fmt.Errorf("insufficient balance for swap")

// After:
return nil, model.NewSwapError(model.ErrCodeInsufficientBalance, "insufficient balance for swap").
    WithDetail("required_amount", debitAmount.String()).
    WithDetail("token", debitTokenSymbol).
    WithDetail("order_id", order.OrderSn)
```

### 2. Quote Expired (Multiple locations)
```go
// swap_service.go:568
// Before:
return nil, fmt.Errorf("quote not found or expired")

// After:
if result.IsNil() {
    return nil, model.NewSwapError(model.ErrCodeQuoteNotFound, "quote not found or expired").
        WithDetail("quote_id", quoteID)
}

// swap_service.go:601, 625
// Before:
return fmt.Errorf("quote has expired")

// After:
return model.NewSwapError(model.ErrCodeQuoteExpired, "quote has expired").
    WithDetail("quote_id", quoteID).
    WithDetail("expired_at", quote.ExpiresAt.Format(time.RFC3339)).
    WithDetail("expired_by_seconds", int64(now.Sub(quote.ExpiresAt).Seconds()))
```

### 3. Amount Validation (Already partially done, ensure consistency)
```go
// swap_service.go:191
// Before:
return nil, fmt.Errorf("invalid amount")

// After:
return nil, model.NewSwapError(model.ErrCodeInvalidAmount, "invalid amount").
    WithDetail("amount", req.Amount.String())
```

### 4. Rate Limit (swap_config.go:183)
```go
// Before:
return fmt.Errorf("too many orders, please wait a moment")

// After:
return model.NewSwapError(model.ErrCodeRateLimitExceeded, "too many orders, please wait a moment").
    WithDetail("user_id", userID).
    WithDetail("retry_after_seconds", 60) // Add appropriate cooldown
```

### 5. Trading Pair Not Found (swap_service.go:228)
```go
// Before:
return nil, fmt.Errorf("no trading pair available for this token combination")

// After:
return nil, model.NewSwapError(model.ErrCodeTradingPairNotFound, "no trading pair available for this token combination").
    WithDetail("from_token_id", req.FromTokenID).
    WithDetail("to_token_id", req.ToTokenID)
```

## Phase 4: Medium Priority Conversions

### 1. Service Unavailable (swap_service.go:68, 184)
```go
// Before:
return nil, fmt.Errorf("swap service unavailable: %s", message)

// After:
return nil, model.NewSwapError(model.ErrCodeServiceUnavailable, message)
```

### 2. Product/Token Not Found
```go
// swap_service.go:113
// Before:
return nil, fmt.Errorf("product not found: %s", symbol)

// After:
return nil, model.NewSwapError(model.ErrCodeProductNotFound, "product not found").
    WithDetail("symbol", symbol)

// swap_service.go:157
// Before:
return "", fmt.Errorf("token not found for ID: %d", tokenID)

// After:
return "", model.NewSwapError(model.ErrCodeTokenNotFound, "token not found").
    WithDetail("token_id", tokenID)

// swap_service.go:170
// Before:
return 0, fmt.Errorf("token not found for symbol: %s", symbol)

// After:
return 0, model.NewSwapError(model.ErrCodeTokenNotFound, "token not found").
    WithDetail("symbol", symbol)
```

### 3. Unauthorized Quote (swap_service.go:630)
```go
// Before:
return nil, fmt.Errorf("quote does not belong to user")

// After:
return nil, model.NewSwapError(model.ErrCodeUnauthorizedQuote, "quote does not belong to user").
    WithDetail("quote_id", req.QuoteID).
    WithDetail("quote_user_id", quote.UserID).
    WithDetail("request_user_id", req.UserID)
```

## Phase 5: Update Error Handler

Ensure the swap error handler properly formats these new error codes in:
- `/home/<USER>/bot-api/internal/service/swap_error_handler.go`

Add cases for new error codes:
```go
case model.ErrCodeProductNotFound:
    return i18n.T(ctx, "{#SwapErrorProductNotFound}")
case model.ErrCodeTradingPairNotFound:
    return i18n.T(ctx, "{#SwapErrorTradingPairNotFound}")
case model.ErrCodeTokenNotFound:
    return i18n.T(ctx, "{#SwapErrorTokenNotFound}")
case model.ErrCodeInvalidTradeConfiguration:
    return i18n.T(ctx, "{#SwapErrorInvalidTradeConfiguration}")
case model.ErrCodeUnauthorizedQuote:
    return i18n.T(ctx, "{#SwapErrorUnauthorizedQuote}")
```

## Testing Checklist

1. [ ] Test insufficient balance error displays correctly in both languages
2. [ ] Test quote expiration shows user-friendly message
3. [ ] Test invalid amounts show proper validation messages
4. [ ] Test rate limiting shows appropriate cooldown message
5. [ ] Test trading pair not found shows helpful message
6. [ ] Test service unavailable during maintenance mode
7. [ ] Test product/token not found errors
8. [ ] Test unauthorized quote access

## Notes

- Keep all error wrapping with `%w` for technical errors that need stack traces
- SwapError should be used for user-facing errors that need i18n
- Include relevant details in error objects for better debugging
- Ensure error messages are consistent across similar error types