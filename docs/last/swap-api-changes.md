# 闪兑API变更文档

## 概述

本文档描述了闪兑手续费策略重构后的API接口变更，包括新增字段、接口行为变化和使用示例。

## 接口变更

### 1. 创建报价接口

#### 接口路径
`POST /api/v2/swap/quote`

#### 请求参数（无变化）
```json
{
  "user_id": 1001,
  "from_token_id": 2,
  "to_token_id": 1,
  "amount": "5000",
  "amount_type": "from"
}
```

#### 响应参数（新增字段）
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "quote_123456",
    "user_id": 1001,
    "from_token_id": 2,
    "to_token_id": 1,
    "from_token_symbol": "USDT",
    "to_token_symbol": "BTC",
    "from_amount": "5000",
    "to_amount": "0.0998",
    "price": "50000",
    "trade_type": "buy",
    
    // 新增：详细的手续费信息
    "output_amount_before_fee": "0.1",      // 扣费前输出金额
    "output_amount_after_fee": "0.0998",    // 扣费后输出金额
    "fee_amount": "0.0002",                 // 手续费金额
    "fee_token_id": 1,                      // 手续费代币ID
    "fee_token_symbol": "BTC",              // 手续费代币符号
    "fee_rate": "0.002",                    // 手续费率
    "fee_calculation_method": "output_percentage", // 手续费计算方法
    "min_fee_applied": false,               // 是否应用了最小手续费
    
    "spread_rate": "0.001",
    "expires_at": "2024-01-01T12:05:00Z",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 2. 创建订单接口

#### 接口路径
`POST /api/v2/swap/order`

#### 请求参数（无变化）
```json
{
  "user_id": 1001,
  "quote_id": "quote_123456",
  "payment_password": "123456"
}
```

#### 响应参数（新增字段）
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "order_sn": "SWAP20240101120000123456",
    "user_id": 1001,
    "product_id": 1,
    "base_token": "BTC",
    "quote_token": "USDT",
    "symbol": "BTC/USDT",
    "trade_type": "buy",
    "amount_base": "0.0998",
    "amount_quote": "5000",
    "price": "50000",
    "status": "pending",
    
    // 新增：详细的手续费信息
    "output_amount_before_fee": "0.1",
    "output_amount_after_fee": "0.0998",
    "fee_amount": "0.0002",
    "fee_token_id": 1,
    "fee_rate": "0.002",
    "fee_calculation_method": "output_percentage",
    
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 3. 执行订单接口

#### 接口路径
`POST /api/v2/swap/execute/{order_sn}`

#### 请求参数（无变化）
```json
{
  "user_id": 1001
}
```

#### 响应参数（新增字段）
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "order_id": "SWAP20240101120000123456",
    "status": "completed",
    "from_token_id": 2,
    "to_token_id": 1,
    "from_amount": "5000",
    "to_amount": "0.0998",
    
    // 新增：详细的手续费和金额信息
    "input_amount": "5000",                 // 用户实际支付金额
    "output_amount_before_fee": "0.1",      // 扣费前输出金额
    "output_amount_after_fee": "0.0998",    // 扣费后输出金额（用户实际收到）
    "fee_amount": "0.0002",                 // 手续费金额
    "fee_token_id": 1,                      // 手续费代币ID
    
    "actual_price": "50000",
    "transaction_hash": "0x1234567890abcdef",
    "executed_at": "2024-01-01T12:01:00Z",
    "completed_at": "2024-01-01T12:01:00Z",
    "slippage_rate": "0.001"
  }
}
```

### 4. 查询订单接口

#### 接口路径
`GET /api/v2/swap/order/{order_sn}`

#### 响应参数（新增字段）
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "order_sn": "SWAP20240101120000123456",
    "user_id": 1001,
    "status": "completed",
    "trade_type": "buy",
    "base_token": "BTC",
    "quote_token": "USDT",
    "amount_base": "0.0998",
    "amount_quote": "5000",
    "price": "50000",
    
    // 新增：详细的手续费信息
    "output_amount_before_fee": "0.1",
    "output_amount_after_fee": "0.0998",
    "fee_amount": "0.0002",
    "fee_token_id": 1,
    "fee_calculation_method": "output_percentage",
    
    "actual_price": "50000",
    "transaction_hash": "0x1234567890abcdef",
    "created_at": "2024-01-01T12:00:00Z",
    "completed_at": "2024-01-01T12:01:00Z"
  }
}
```

## 字段说明

### 新增字段详解

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `output_amount_before_fee` | string | 扣除手续费前的输出金额 |
| `output_amount_after_fee` | string | 扣除手续费后的输出金额（用户实际收到） |
| `fee_calculation_method` | string | 手续费计算方法，固定为 "output_percentage" |
| `min_fee_applied` | boolean | 是否应用了最小手续费（仅在报价接口返回） |
| `input_amount` | string | 用户实际支付的金额（仅在执行结果返回） |

### 手续费相关字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `fee_amount` | string | 手续费金额 |
| `fee_token_id` | integer | 手续费代币ID |
| `fee_token_symbol` | string | 手续费代币符号 |
| `fee_rate` | string | 手续费率（小数形式，如 0.002 表示 0.2%） |

## 使用示例

### 示例1：BTC/USDT 买入交易

#### 1. 创建报价
```bash
curl -X POST /api/v2/swap/quote \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1001,
    "from_token_id": 2,
    "to_token_id": 1,
    "amount": "5000",
    "amount_type": "from"
  }'
```

#### 响应解读
- 用户支付：5000 USDT
- 扣费前收到：0.1 BTC
- 手续费：0.0002 BTC（从收到的BTC中扣除）
- 实际收到：0.0998 BTC

#### 2. 创建并执行订单
用户最终收到 0.0998 BTC，手续费 0.0002 BTC 已从中扣除。

### 示例2：BTC/USDT 卖出交易

#### 1. 创建报价
```bash
curl -X POST /api/v2/swap/quote \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1001,
    "from_token_id": 1,
    "to_token_id": 2,
    "amount": "0.1",
    "amount_type": "from"
  }'
```

#### 响应解读
- 用户支付：0.1 BTC
- 扣费前收到：5000 USDT
- 手续费：10 USDT（从收到的USDT中扣除）
- 实际收到：4990 USDT

## 错误处理

### 新增错误码

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| `SWAP_FEE_CALCULATION_FAILED` | 手续费计算失败 | 检查产品配置和输入参数 |
| `SWAP_MIN_FEE_NOT_MET` | 未达到最小手续费要求 | 增加交易金额或检查产品配置 |
| `SWAP_OUTPUT_AMOUNT_TOO_SMALL` | 扣费后输出金额过小 | 增加交易金额 |

### 错误响应示例
```json
{
  "code": 40001,
  "message": "SWAP_FEE_CALCULATION_FAILED",
  "data": {
    "error_details": {
      "reason": "Invalid fee rate configuration",
      "product_id": 1,
      "fee_rate": "invalid"
    }
  }
}
```

## 向后兼容性

### 兼容性保证
1. **现有字段**：所有现有字段保持不变
2. **API路径**：接口路径和请求方法不变
3. **核心逻辑**：用户交互流程保持一致

### 客户端适配建议
1. **渐进式升级**：客户端可以逐步适配新字段
2. **向后兼容**：不使用新字段的客户端仍可正常工作
3. **字段检查**：建议检查新字段是否存在再使用

### 适配示例
```javascript
// 兼容性处理示例
function getActualReceiveAmount(response) {
  // 优先使用新字段
  if (response.output_amount_after_fee) {
    return response.output_amount_after_fee;
  }
  // 降级到旧字段
  return response.to_amount;
}
```

## 测试建议

### 集成测试要点
1. **字段完整性**：验证所有新字段都正确返回
2. **计算准确性**：验证手续费计算的准确性
3. **边界条件**：测试最小手续费、零费率等边界情况
4. **向后兼容**：确保旧客户端仍能正常工作

### 测试用例示例
```javascript
describe('Swap API with new fee strategy', () => {
  it('should return detailed fee information in quote', async () => {
    const response = await createQuote({
      user_id: 1001,
      from_token_id: 2,
      to_token_id: 1,
      amount: "5000",
      amount_type: "from"
    });
    
    expect(response.output_amount_before_fee).toBeDefined();
    expect(response.output_amount_after_fee).toBeDefined();
    expect(response.fee_amount).toBeDefined();
    expect(response.fee_calculation_method).toBe('output_percentage');
  });
});
```

## 总结

新的API设计在保持向后兼容的基础上，提供了更详细的手续费信息，帮助用户更好地理解交易成本和实际收益。通过渐进式的字段增加，确保了系统的平滑升级。
