# 闪兑手续费策略重构文档

## 概述

本文档描述了闪兑系统手续费策略的重构，从原来的"从用户输入代币中扣除手续费"改为"从用户接收代币中扣除手续费"的新策略。

## 背景

### 原有策略的问题

1. **用户体验差**：用户需要额外准备手续费，增加了交易复杂度
2. **余额计算复杂**：需要考虑输入金额 + 手续费的总余额
3. **逻辑不一致**：不同交易对的手续费扣除方式不统一

### 新策略的优势

1. **简化用户操作**：用户只需要准备输入金额，手续费从收到的代币中扣除
2. **统一的逻辑**：所有交易对都采用相同的手续费策略
3. **更好的用户体验**：用户明确知道最终能收到多少代币

## 新手续费策略详解

### 核心原则

- **买入交易**：手续费从收到的基础代币中扣除
- **卖出交易**：手续费从收到的计价代币中扣除
- **费率统一**：使用 `OutputFeeRate` 作为统一的手续费率
- **最小手续费**：支持 `MinOutputFeeAmount` 最小手续费保护

### 计算公式

#### 买入交易 (用USDT买BTC)
```
输入金额 = 用户支付的USDT数量
输出金额(扣费前) = 输入金额 / 价格
手续费 = max(输出金额 * 费率, 最小手续费)
输出金额(扣费后) = 输出金额(扣费前) - 手续费
```

#### 卖出交易 (卖BTC换USDT)
```
输入金额 = 用户支付的BTC数量
输出金额(扣费前) = 输入金额 * 价格
手续费 = max(输出金额 * 费率, 最小手续费)
输出金额(扣费后) = 输出金额(扣费前) - 手续费
```

## 技术实现

### 核心组件

#### 1. OutputFeeCalculator
负责计算新策略下的手续费：

```go
type OutputFeeCalculator struct {
    product *entity.ExchangeProducts
}

func (c *OutputFeeCalculator) CalculateFee(ctx context.Context, req *FeeCalculationRequest) (*FeeCalculationResult, error)
```

**主要功能**：
- 根据交易类型确定输出代币
- 计算扣费前的输出金额
- 应用费率和最小手续费规则
- 返回详细的手续费计算结果

#### 2. FeeValidator
验证手续费相关的业务规则：

```go
type FeeValidator struct{}

func (v *FeeValidator) ValidateUserBalance(ctx context.Context, req *BalanceValidationRequest) (*BalanceValidationResult, error)
func (v *FeeValidator) ValidateProductLimits(ctx context.Context, product *entity.ExchangeProducts, req *ProductValidationRequest) error
```

**主要功能**：
- 验证用户余额是否充足（新策略下只需验证输入金额）
- 验证产品交易限制
- 验证报价有效性和所有权

#### 3. 新增接口方法
为支持闪兑操作，在WalletService中新增了专门的方法：

```go
type WalletService interface {
    // 资金锁定/解锁
    LockFunds(ctx context.Context, req *LockFundsRequest) (*LockFundsResult, error)
    UnlockFunds(ctx context.Context, lockID string) error
    
    // 闪兑专用转账
    TransferForSwap(ctx context.Context, req *SwapTransferRequest) (*SwapTransferResult, error)
}
```

### 数据结构更新

#### SwapQuote 增强
```go
type SwapQuote struct {
    // 原有字段...
    
    // 新增：详细的手续费信息
    OutputAmountBeforeFee decimal.Decimal `json:"output_amount_before_fee"`
    OutputAmountAfterFee  decimal.Decimal `json:"output_amount_after_fee"`
    FeeCalculationMethod  string          `json:"fee_calculation_method"`
}
```

#### ExchangeOrders 增强
```go
type ExchangeOrders struct {
    // 原有字段...
    
    // 新增：手续费详细信息
    OutputAmountBeforeFee decimal.Decimal `json:"output_amount_before_fee"`
    OutputAmountAfterFee  decimal.Decimal `json:"output_amount_after_fee"`
    FeeCalculationMethod  string          `json:"fee_calculation_method"`
}
```

## 业务流程

### 1. 报价生成流程
```mermaid
graph TD
    A[用户请求报价] --> B[获取实时价格]
    B --> C[计算输出金额]
    C --> D[使用OutputFeeCalculator计算手续费]
    D --> E[生成报价]
    E --> F[返回给用户]
```

### 2. 订单执行流程
```mermaid
graph TD
    A[用户创建订单] --> B[验证报价有效性]
    B --> C[验证用户余额]
    C --> D[锁定用户资金]
    D --> E[执行转账]
    E --> F[更新订单状态]
    F --> G[释放资金锁定]
```

## 配置参数

### 产品配置
```sql
-- exchange_products 表新增字段
ALTER TABLE exchange_products ADD COLUMN output_fee_rate DECIMAL(10,8) DEFAULT 0.002;
ALTER TABLE exchange_products ADD COLUMN min_output_fee_amount DECIMAL(20,8) DEFAULT 0;
```

### 示例配置
```json
{
  "product_id": 1,
  "symbol": "BTC/USDT",
  "base_token": "BTC",
  "quote_token": "USDT",
  "output_fee_rate": "0.002",        // 0.2% 手续费率
  "min_output_fee_amount": "0.0001", // 最小手续费 0.0001 BTC 或等值
  "is_active": 1,
  "allow_buy": 1,
  "allow_sell": 1
}
```

## 测试覆盖

### 单元测试
- ✅ OutputFeeCalculator 各种场景测试
- ✅ FeeValidator 验证逻辑测试
- ✅ 边界条件和异常情况测试

### 集成测试
- ✅ 完整交易流程测试
- ✅ 手续费计算一致性测试
- ✅ 不同交易对的测试覆盖

### 测试用例示例
```go
// BTC/USDT 买入测试
{
    name: "BTC买入-从BTC扣手续费",
    tradeType: "buy",
    inputAmount: 5000 USDT,
    outputBeforeFee: 0.1 BTC,
    feeRate: 0.002,
    expectedFee: 0.0002 BTC,
    expectedOutput: 0.0998 BTC,
}
```

## 向后兼容性

### 兼容策略
1. **渐进式迁移**：新订单使用新策略，旧订单保持原有逻辑
2. **数据库兼容**：新增字段有默认值，不影响现有数据
3. **API兼容**：保持现有API接口不变，内部逻辑升级

### 迁移计划
1. **阶段1**：部署新代码，默认使用旧策略
2. **阶段2**：逐步切换产品到新策略
3. **阶段3**：全面启用新策略
4. **阶段4**：清理旧代码和数据

## 常见问题

### Q: 新策略下用户需要准备多少余额？
A: 用户只需要准备输入金额的余额，手续费从输出代币中扣除。

### Q: 最小手续费如何应用？
A: 当计算出的手续费小于最小手续费时，使用最小手续费。

### Q: 如何处理价格滑点？
A: 在订单执行时重新验证价格，超过允许滑点则拒绝执行。

### Q: 旧订单如何处理？
A: 旧订单继续使用原有逻辑执行，确保向后兼容。

## 总结

新的手续费策略显著改善了用户体验，简化了交易流程，提高了系统的一致性和可维护性。通过完善的测试覆盖和渐进式迁移策略，确保了系统的稳定性和可靠性。
