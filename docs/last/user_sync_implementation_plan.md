# 🎯 用户信息同步 Kafka 消息队列实施方案

## 📊 核心数据结构设计

### 1. 用户同步消息结构

```go
// UserSyncMessage 用户同步消息
type UserSyncMessage struct {
    MessageID     string                 `json:"message_id"`     // 消息唯一ID
    TelegramID    int64                  `json:"telegram_id"`    // Telegram用户ID  
    UpdateType    UserSyncUpdateType     `json:"update_type"`    // 更新类型
    Changes       map[string]interface{} `json:"changes"`        // 变更字段
    OriginalData  map[string]interface{} `json:"original_data"`  // 原始数据
    NewData       map[string]interface{} `json:"new_data"`       // 新数据
    Timestamp     time.Time              `json:"timestamp"`      // 时间戳
    RetryCount    int                    `json:"retry_count"`    // 重试次数
    Priority      int                    `json:"priority"`       // 优先级
}

// UserSyncUpdateType 更新类型枚举
type UserSyncUpdateType string

const (
    UpdateTypeProfile     UserSyncUpdateType = "profile"        // 个人信息更新
    UpdateTypeLoginTime   UserSyncUpdateType = "login_time"     // 登录时间更新
    UpdateTypeBackupAccount UserSyncUpdateType = "backup_account" // 备份账户更新
    UpdateTypePermissions UserSyncUpdateType = "permissions"    // 权限更新
)

// BatchLoginTimeUpdate 批量登录时间更新结构
type BatchLoginTimeUpdate struct {
    TelegramID    int64     `json:"telegram_id"`
    LoginTime     time.Time `json:"login_time"`
    UpdatedAt     time.Time `json:"updated_at"`
}

// UserCacheData 用户缓存数据结构
type UserCacheData struct {
    UserData       *entity.Users               `json:"user_data"`
    BackupAccounts []*entity.UserBackupAccounts `json:"backup_accounts"`
    CachedAt       time.Time                   `json:"cached_at"`
    Version        int64                       `json:"version"`    // 数据版本号
}
```

## 🔧 核心服务实现

### 1. 用户同步服务 (`internal/service/user_sync.go`)

```go
package service

import (
    "context"
    "encoding/json"
    "fmt"
    "sync"
    "time"
    
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/errors/gerror"
    tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
)

// IUserSync 用户同步服务接口
type IUserSync interface {
    // 检查用户数据变更
    CheckUserChanges(ctx context.Context, tgUser *tgbotapi.User, chatID int64) error
    // 发送同步消息到Kafka
    SendSyncMessage(ctx context.Context, msg *UserSyncMessage) error
    // 批量更新登录时间
    BatchUpdateLoginTime(ctx context.Context, updates []BatchLoginTimeUpdate) error
}

type userSyncService struct {
    loginTimeBatch     []BatchLoginTimeUpdate
    loginTimeMutex     sync.RWMutex
    batchSize          int
    flushInterval      time.Duration
    lastFlushTime      time.Time
    batchTimer         *time.Timer
}

var insUserSync = &userSyncService{
    loginTimeBatch: make([]BatchLoginTimeUpdate, 0),
    batchSize:      100,
    flushInterval:  30 * time.Second,
    lastFlushTime:  time.Now(),
}

// UserSync 返回用户同步服务实例
func UserSync() IUserSync {
    return insUserSync
}

// Init 初始化用户同步服务
func (s *userSyncService) Init(ctx context.Context) error {
    // 从配置读取参数
    batchSizeVar, _ := g.Cfg().Get(ctx, "userSync.batchUpdate.loginTimeBatchSize", 100)
    s.batchSize = batchSizeVar.Int()
    
    flushIntervalVar, _ := g.Cfg().Get(ctx, "userSync.batchUpdate.batchFlushInterval", "30s")
    s.flushInterval = flushIntervalVar.Duration()
    
    // 启动定时刷新协程
    go s.startBatchFlushTimer(ctx)
    
    g.Log().Infof(ctx, "UserSync service initialized with batch size: %d, flush interval: %v", 
        s.batchSize, s.flushInterval)
    return nil
}

// CheckUserChanges 检查用户数据变更
func (s *userSyncService) CheckUserChanges(ctx context.Context, tgUser *tgbotapi.User, chatID int64) error {
    if tgUser == nil {
        return nil
    }
    
    telegramID := tgUser.ID
    
    // 1. 获取缓存中的用户数据
    cachedData, err := s.getCachedUserData(ctx, telegramID)
    if err != nil {
        g.Log().Debugf(ctx, "Failed to get cached user data for %d: %v", telegramID, err)
    }
    
    // 2. 获取数据库中的最新数据
    currentUser, err := User().GetUserByTelegramId(ctx, telegramID)
    if err != nil {
        return gerror.Wrapf(err, "Failed to get user data for telegram ID %d", telegramID)
    }
    
    currentBackupAccounts, err := BackupAccounts().GetBackupAccountByTelegramId(ctx, telegramID)
    if err != nil && !gerror.Is(err, ErrUserNotFound) {
        g.Log().Warningf(ctx, "Failed to get backup account for %d: %v", telegramID, err)
    }
    
    // 3. 比较数据变更
    changes := s.detectChanges(cachedData, currentUser, currentBackupAccounts, tgUser, chatID)
    
    // 4. 如果有变更，发送同步消息
    if len(changes) > 0 {
        syncMsg := &UserSyncMessage{
            MessageID:  fmt.Sprintf("sync_%d_%d", telegramID, time.Now().UnixNano()),
            TelegramID: telegramID,
            Changes:    changes,
            Timestamp:  time.Now(),
            Priority:   s.calculatePriority(changes),
        }
        
        // 根据变更类型设置更新类型
        if _, hasLoginTime := changes["last_login_time"]; hasLoginTime {
            syncMsg.UpdateType = UpdateTypeLoginTime
        } else {
            syncMsg.UpdateType = UpdateTypeProfile
        }
        
        err = s.SendSyncMessage(ctx, syncMsg)
        if err != nil {
            return gerror.Wrap(err, "Failed to send sync message")
        }
        
        // 5. 更新缓存
        err = s.updateUserCache(ctx, telegramID, currentUser, []*entity.UserBackupAccounts{currentBackupAccounts})
        if err != nil {
            g.Log().Warningf(ctx, "Failed to update user cache for %d: %v", telegramID, err)
        }
    }
    
    return nil
}

// detectChanges 检测数据变更
func (s *userSyncService) detectChanges(cachedData *UserCacheData, currentUser *entity.Users, 
    backupAccount *entity.UserBackupAccounts, tgUser *tgbotapi.User, chatID int64) map[string]interface{} {
    
    changes := make(map[string]interface{})
    
    // 如果没有缓存数据，表示是新用户或缓存失效，记录为变更
    if cachedData == nil || cachedData.UserData == nil {
        changes["reason"] = "no_cache"
        changes["last_login_time"] = time.Now()
        if currentUser != nil {
            changes["nickname"] = currentUser.Nickname
            changes["name"] = currentUser.Name
            changes["language"] = currentUser.Language
        }
        return changes
    }
    
    cached := cachedData.UserData
    
    // 比较用户基本信息
    if currentUser != nil {
        if cached.Nickname != currentUser.Nickname {
            changes["nickname"] = currentUser.Nickname
        }
        if cached.Name != currentUser.Name {
            changes["name"] = currentUser.Name  
        }
        if cached.Language != currentUser.Language {
            changes["language"] = currentUser.Language
        }
    }
    
    // 检查 Telegram 用户信息变更
    if tgUser.UserName != "" {
        expectedUsername := "@" + tgUser.UserName
        if backupAccount != nil && backupAccount.TelegramUsername != expectedUsername {
            changes["telegram_username"] = expectedUsername
        }
    }
    
    if tgUser.FirstName != "" && backupAccount != nil && backupAccount.FirstName != tgUser.FirstName {
        changes["first_name"] = tgUser.FirstName
    }
    
    // 登录时间更新（每次都更新）
    changes["last_login_time"] = time.Now()
    
    return changes
}

// SendSyncMessage 发送同步消息到Kafka
func (s *userSyncService) SendSyncMessage(ctx context.Context, msg *UserSyncMessage) error {
    // 获取主题名称
    topicVar, err := g.Cfg().Get(ctx, "kafka.topics.userSyncUpdates")
    if err != nil {
        return gerror.Wrap(err, "Failed to get user sync topic")
    }
    topic := topicVar.String()
    
    // 序列化消息
    msgBytes, err := json.Marshal(msg)
    if err != nil {
        return gerror.Wrap(err, "Failed to marshal sync message")
    }
    
    // 发送到Kafka
    key := fmt.Sprintf("user_%d", msg.TelegramID)
    err = Kafka().SendMessage(ctx, topic, key, msgBytes)
    if err != nil {
        return gerror.Wrap(err, "Failed to send message to Kafka")
    }
    
    g.Log().Debugf(ctx, "Sent user sync message for telegram ID %d, type: %s, changes: %v", 
        msg.TelegramID, msg.UpdateType, msg.Changes)
    
    return nil
}
```

### 2. 用户同步消费者实现

```go
// 用户同步消费者处理逻辑
func (s *userSyncService) startSyncConsumer(ctx context.Context) error {
    topicVar, err := g.Cfg().Get(ctx, "kafka.topics.userSyncUpdates")
    if err != nil {
        return err
    }
    topic := topicVar.String()
    
    groupIDVar, err := g.Cfg().Get(ctx, "kafka.consumerConfig.userSyncGroupId")
    if err != nil {
        return err
    }
    groupID := groupIDVar.String()
    
    return Kafka().NewConsumer(ctx, topic, groupID, s.handleSyncMessage)
}

// handleSyncMessage 处理同步消息
func (s *userSyncService) handleSyncMessage(ctx context.Context, msg *sarama.ConsumerMessage) error {
    var syncMsg UserSyncMessage
    err := json.Unmarshal(msg.Value, &syncMsg)
    if err != nil {
        g.Log().Errorf(ctx, "Failed to unmarshal sync message: %v", err)
        return nil // 跳过无法解析的消息
    }
    
    g.Log().Debugf(ctx, "Processing sync message for telegram ID %d, type: %s", 
        syncMsg.TelegramID, syncMsg.UpdateType)
    
    switch syncMsg.UpdateType {
    case UpdateTypeLoginTime:
        return s.handleLoginTimeUpdate(ctx, &syncMsg)
    case UpdateTypeProfile:
        return s.handleProfileUpdate(ctx, &syncMsg)
    case UpdateTypeBackupAccount:
        return s.handleBackupAccountUpdate(ctx, &syncMsg)
    default:
        g.Log().Warningf(ctx, "Unknown sync message type: %s", syncMsg.UpdateType)
        return nil
    }
}

// handleLoginTimeUpdate 处理登录时间更新（批量处理）
func (s *userSyncService) handleLoginTimeUpdate(ctx context.Context, msg *UserSyncMessage) error {
    if loginTime, ok := msg.Changes["last_login_time"]; ok {
        if loginTimeVal, ok := loginTime.(string); ok {
            parsedTime, err := time.Parse(time.RFC3339, loginTimeVal)
            if err != nil {
                parsedTime = time.Now()
            }
            
            update := BatchLoginTimeUpdate{
                TelegramID: msg.TelegramID,
                LoginTime:  parsedTime,
                UpdatedAt:  time.Now(),
            }
            
            s.addToLoginTimeBatch(ctx, update)
        }
    }
    
    // 处理其他非登录时间字段
    otherChanges := make(map[string]interface{})
    for k, v := range msg.Changes {
        if k != "last_login_time" {
            otherChanges[k] = v
        }
    }
    
    if len(otherChanges) > 0 {
        return s.updateUserFields(ctx, msg.TelegramID, otherChanges)
    }
    
    return nil
}
```

## 🚀 实施步骤

### 阶段一：基础设施准备 (1-2天)
1. ✅ 更新 Kafka 配置
2. ⏳ 创建用户同步数据结构
3. ⏳ 更新数据库操作接口
4. ⏳ 扩展缓存服务

### 阶段二：核心服务开发 (3-4天)
1. ⏳ 实现用户同步服务
2. ⏳ 实现数据变更检测逻辑
3. ⏳ 实现批量更新管理器
4. ⏳ 集成 Kafka 生产者和消费者

### 阶段三：消息处理器集成 (2-3天)
1. ⏳ 修改现有消息拦截器
2. ⏳ 集成用户数据检查逻辑
3. ⏳ 优化缓存策略
4. ⏳ 添加性能监控

### 阶段四：测试和优化 (2-3天)
1. ⏳ 单元测试和集成测试
2. ⏳ 性能测试和调优
3. ⏳ 错误处理和重试机制
4. ⏳ 监控和告警设置

## 📈 性能优化策略

### 1. 缓存优化
- 用户数据缓存 TTL: 10分钟
- 备份账户缓存 TTL: 30分钟
- 使用 Redis Pipeline 批量操作

### 2. 批量处理优化
- 登录时间更新：100个用户/批次
- 批量刷新间隔：30秒
- 最大等待时间：5分钟

### 3. Kafka 优化
- 分区策略：按用户ID哈希
- 批量大小：100条消息
- 压缩：gzip
- 副本数：2

### 4. 数据库优化
- 使用事务确保数据一致性
- 索引优化（telegram_id, updated_at）
- 连接池优化

## 🔍 监控指标

### 关键性能指标
- 消息处理延迟
- 批量更新成功率
- 缓存命中率
- 数据库连接池使用率
- Kafka 消费滞后

### 告警设置
- 消息处理失败率 > 5%
- 批量更新延迟 > 2分钟
- 缓存命中率 < 80%
- Kafka 消费滞后 > 1000条