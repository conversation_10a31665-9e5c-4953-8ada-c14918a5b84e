# Bot API Project Guidelines

## Code Quality Commands

### Format Go Code
```bash
go fmt ./...
```

### Build Check
```bash
go build -o /tmp/bot-api ./internal/cmd/main.go
```

## Recent Improvements

### Swap Error Handling Enhancement
- Created `internal/service/swap_error_handler.go` for centralized error formatting
- Added user-friendly error messages in i18n files (en.toml and zh-CN.toml)
- Updated error handling in:
  - `internal/bot/swap/callbacks.go`
  - `internal/bot/swap/message.go`
- All swap errors now display localized, user-friendly messages instead of raw technical errors