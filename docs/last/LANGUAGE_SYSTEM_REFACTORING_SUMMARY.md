# Language System Refactoring Summary

## Overview
This document summarizes the comprehensive refactoring of the language system to meet the following requirements:

1. **Complete Independence from Telegram Language**: The system no longer syncs with or monitors Telegram's language field
2. **Default Language**: New users default to `zh-CN` (Chinese) upon registration
3. **Supported Languages**: Only `zh-CN` and `en` are supported
4. **Data Persistence**: Language preferences are stored in both Redis cache and MySQL database
5. **Data Retrieval**: Redis cache is checked first, with MySQL as fallback

## Changes Made

### 1. Core Constants and Configuration (`internal/consts/language.go`)

**Before:**
- Default language: `"en"` (English)
- Complex Telegram language mapping with 20+ language codes
- Missing `UserLanguageKeyPrefix` constant
- Missing local language type definitions

**After:**
- Default language: `"zh-CN"` (Chinese)
- Simplified Telegram language mapping (only English variants map to English, everything else to Chinese)
- Added `UserLanguageKeyPrefix = "user_language:"` constant
- Added local `Language`, `LanguageType`, `TypeList`, and `IsLanguageType` definitions
- Removed dependency on external package constants

### 2. User Registration Logic

**Files Modified:**
- `internal/logic/user/get_or_create_user_by_tg_data.go`
- `internal/service/v2/impl/user_service.go`
- `internal/dao/user_sync.go`

**Changes:**
- New users always default to `"zh-CN"` regardless of Telegram language
- Removed Telegram language mapping from user creation
- Added logging to indicate Telegram language is ignored

### 3. Language Handler (`internal/bot/language/handler.go`)

**Changes:**
- Removed dependency on `github.com/a19ba14d/tg-bot-common/consts`
- Now uses local `internal/consts` package
- Language buttons now use local `TypeList` and `LanguageType` definitions

### 4. I18n Service (`internal/service/v2/impl/i18n_service.go`)

**Changes:**
- Default language changed from `"en"` to `"zh-CN"`
- Supported languages reordered: `["zh-CN", "en"]` (Chinese first)

### 5. Test Updates

**Files Modified:**
- `internal/service/language_manager_test.go`
- Created `internal/service/language_integration_test.go`

**Changes:**
- Updated test expectations to reflect Chinese as default
- Added comprehensive integration tests
- Verified all language system components work together

## Redis Key Structure

The Redis key structure remains the same but is now properly defined:
```
Key: user_language:{telegram_id}
Value: "zh-CN" or "en"
TTL: No expiration (permanent cache)
```

## Database Schema

No database schema changes were required. The existing `users.language` field continues to store language preferences.

## Language Mapping Logic

**New Simplified Mapping:**
```go
func MapTelegramLanguageToSystem(telegramLang string) string {
    switch strings.ToLower(telegramLang) {
    case "en", "en-us", "en-gb":
        return "en"
    default:
        return "zh-CN" // Default to Chinese for all other languages
    }
}
```

## UI Language Options

The language selection UI now shows:
- 🇨🇳 中文 (`zh-CN`) - First option, default
- 🇺🇸 English (`en`) - Second option

## Data Flow

### User Registration:
1. New user registers via Telegram
2. System ignores Telegram language code
3. User is created with `language = "zh-CN"`
4. Language is cached in Redis with key `user_language:{telegram_id}`

### Language Retrieval:
1. Check Redis cache first: `user_language:{telegram_id}`
2. If not found, query MySQL: `users.language`
3. If empty in DB, use default: `"zh-CN"`
4. Cache the result in Redis

### Language Update:
1. User selects new language via bot interface
2. Update MySQL: `users.language`
3. Update Redis cache: `user_language:{telegram_id}`

## Testing

All tests pass successfully:
- `TestLanguageManager_MapTelegramLanguage`
- `TestLanguageManager_ValidateLanguage`
- `TestLanguageManager_ContextOperations`
- `TestLanguageSystemIntegration`
- `TestUserLanguageKeyPrefix`

## Backward Compatibility

- Existing users with `language = "en"` will continue to use English
- Existing users with `language = "zh-CN"` will continue to use Chinese
- Existing users with other language codes will be migrated to Chinese on next language retrieval
- Redis cache keys remain the same format

## Benefits Achieved

1. ✅ **Complete Independence**: No dependency on Telegram language system
2. ✅ **Chinese Default**: All new users default to Chinese
3. ✅ **Simplified Logic**: Removed complex language mapping
4. ✅ **Self-Contained**: No external package dependencies for language types
5. ✅ **Consistent Caching**: Proper Redis key structure defined
6. ✅ **Comprehensive Testing**: Full test coverage for new behavior

## Files Modified Summary

1. `internal/consts/language.go` - Core language constants and types
2. `internal/logic/user/get_or_create_user_by_tg_data.go` - User creation logic
3. `internal/service/v2/impl/user_service.go` - V2 user service
4. `internal/dao/user_sync.go` - User sync logic
5. `internal/bot/language/handler.go` - Language selection handler
6. `internal/service/v2/impl/i18n_service.go` - I18n service
7. `internal/service/language_manager_test.go` - Updated tests
8. `internal/cmd/processor/text_handler.go` - Text processor
9. `internal/service/language_integration_test.go` - New integration tests

The refactoring is complete and all requirements have been successfully implemented.
