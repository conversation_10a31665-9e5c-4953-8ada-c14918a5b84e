# Fund Transfer Concurrency Control

## Overview

This document describes the concurrency control mechanism implemented for the backup account fund transfer feature to prevent race conditions and ensure data integrity.

## Problem Statement

When multiple backup accounts attempt to transfer funds from the same main account simultaneously, it could lead to:
- Race conditions where multiple transfers execute at the same time
- Potential data inconsistency
- Duplicate transfers or incorrect balance calculations

## Solution Architecture

The implementation uses a multi-layered approach to ensure safe concurrent operations:

### 1. Redis Distributed Lock

**Implementation Location**: `/internal/service/redis.go`

Key functions added:
- `AcquireLock()`: Attempts to acquire a distributed lock using Redis SETNX
- `ReleaseLock()`: Releases a lock only if the holder matches
- `ExtendLock()`: Extends the TTL of an existing lock
- `GetFundTransferLockKey()`: Generates a unique lock key for each main account

**Lock Mechanism**:
- Uses Redis SET with NX (Not eXists) flag for atomic lock acquisition
- Each lock has a unique identifier (UUID-like) to ensure only the lock holder can release it
- Implements automatic expiration (TTL) to prevent deadlocks
- Uses Lua scripts for atomic operations (check-and-delete pattern)

### 2. Transfer Handler Integration

**Implementation Location**: `/internal/bot/profile/backup_accounts_transfer_funds.go`

The fund transfer handler now:
1. Generates a unique lock ID for each transfer attempt
2. Attempts to acquire the Redis lock before proceeding
3. Returns an error message if another transfer is in progress
4. Ensures lock release even if the transfer fails (using defer)
5. Sets a 30-second timeout for the entire transfer operation

### 3. Financial Operation Service

**Implementation Location**: `/internal/service/financial_operation.go`

Improvements made:
- Collects all non-zero balances before starting transfers
- Handles the case where no funds need to be transferred
- Each token transfer is executed within its own database transaction
- Proper error handling and logging throughout the process

## Error Handling

### User-Facing Messages

New translation keys added:
- `TransferFundsSystemError`: Shown when lock acquisition fails due to system error
- `TransferFundsAlreadyInProgress`: Shown when another transfer is already in progress

### Lock Failure Scenarios

1. **Redis Connection Error**: Returns system error to user
2. **Lock Already Held**: Returns "already in progress" message
3. **Lock Release Failure**: Logged but doesn't affect user experience

## Testing

A comprehensive test script is provided at `/scripts/test_fund_transfer_concurrency.go` that tests:

1. **Concurrent Transfer Attempts**: Simulates multiple backup accounts trying to transfer simultaneously
2. **Lock Expiration**: Verifies that locks expire properly and can be reacquired
3. **Redis Connection**: Tests basic Redis connectivity and operations

## Configuration

### Lock Parameters

- **Lock Key Format**: `fund_transfer_lock:{mainUserID}`
- **Default TTL**: 30 seconds (configurable)
- **Lock ID Format**: `{currentUserID}_{mainUserID}_{timestamp_nano}`

### Redis Requirements

- Redis server must be available and configured
- Lua scripting must be enabled for atomic operations
- Recommended: Redis 2.6.12 or higher for EVAL support

## Monitoring and Debugging

### Log Points

The implementation includes detailed logging at key points:
- Lock acquisition attempts and results
- Lock release operations
- Transfer progress for each token
- Error conditions with full context

### Debug Commands

To manually check or clear locks:
```bash
# Check if a lock exists
redis-cli GET fund_transfer_lock:123

# Manually remove a stuck lock (use with caution)
redis-cli DEL fund_transfer_lock:123
```

## Future Enhancements

1. **Lock Extension**: For very long transfers, implement periodic lock extension
2. **Queue System**: Implement a queue for transfer requests instead of immediate rejection
3. **Metrics**: Add prometheus metrics for lock contention and transfer times
4. **Circuit Breaker**: Implement circuit breaker pattern for Redis failures
5. **Database Row Locking**: Add SELECT FOR UPDATE on wallet rows for additional safety

## Security Considerations

1. Lock IDs include user IDs and timestamps to prevent unauthorized lock manipulation
2. Only the lock holder can release their lock (enforced by Lua script)
3. Automatic expiration prevents permanent deadlocks
4. All operations are logged for audit purposes