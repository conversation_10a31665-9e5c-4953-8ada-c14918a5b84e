# 转账消息统一化方案文档

## 概述

本文档描述了转账消息统一化的实施方案，通过创建统一的 `TransferMessageService` 服务来整合分散在多个文件中的转账消息处理逻辑。

## 背景

### 问题分析

1. **代码重复**：转账消息构建和发送逻辑分散在多个文件中
2. **一致性问题**：不同场景下的消息格式不统一
3. **维护困难**：修改消息格式需要在多处同步更改
4. **错误处理分散**：缺乏统一的错误处理机制

### 解决方案

创建统一的 `TransferMessageService` 服务，提供标准化的接口和实现。

## 架构设计

### 核心服务接口

```go
type TransferMessageService interface {
    // 构建转账消息
    BuildTransferMessage(ctx context.Context, req *TransferMessageRequest) (*TransferMessageResponse, error)
    
    // 发送转账通知
    SendTransferNotification(ctx context.Context, req *TransferNotificationRequestV2) error
    
    // 格式化转账金额
    FormatTransferAmount(ctx context.Context, transfer *entity.Transfers) (string, error)
    
    // 获取用户显示名称
    GetUserDisplayName(ctx context.Context, userId uint64, username string) string
    
    // 构建转账成功消息
    BuildTransferSuccessMessage(ctx context.Context, transfer *entity.Transfers) (string, error)
    
    // 发送转账通知给接收方
    SendTransferNotificationToReceiver(ctx context.Context, transfer *entity.Transfers) error
    
    // 处理转账成功逻辑
    HandleTransferSuccess(ctx context.Context, successCtx *TransferSuccessContext) (*TransferSuccessResponse, error)
}
```

### 消息类型定义

```go
// 消息类型
type MessageType string

const (
    MessageTypeStatus       MessageType = "status"       // 状态消息
    MessageTypeSuccess      MessageType = "success"      // 成功消息
    MessageTypeNotification MessageType = "notification" // 通知消息
    MessageTypeConfirmation MessageType = "confirmation" // 确认消息
    MessageTypeInitial      MessageType = "initial"      // 初始消息
)

// 查看者类型
type ViewerType string

const (
    ViewerTypeSender   ViewerType = "sender"   // 发送方视角
    ViewerTypeReceiver ViewerType = "receiver" // 接收方视角
    ViewerTypePublic   ViewerType = "public"   // 公共视角
)
```

### 错误处理机制

```go
// 错误码定义
type TransferMessageErrorCode string

const (
    // 通用错误
    ErrCodeInvalidRequest      TransferMessageErrorCode = "INVALID_REQUEST"
    ErrCodeTransferNotFound    TransferMessageErrorCode = "TRANSFER_NOT_FOUND"
    ErrCodeInvalidAmount       TransferMessageErrorCode = "INVALID_AMOUNT"
    ErrCodeUserNotFound        TransferMessageErrorCode = "USER_NOT_FOUND"
    
    // 消息构建错误
    ErrCodeMessageBuildFailed  TransferMessageErrorCode = "MESSAGE_BUILD_FAILED"
    ErrCodeInvalidMessageType  TransferMessageErrorCode = "INVALID_MESSAGE_TYPE"
    ErrCodeTemplateNotFound    TransferMessageErrorCode = "TEMPLATE_NOT_FOUND"
    
    // 通知发送错误
    ErrCodeNotificationFailed  TransferMessageErrorCode = "NOTIFICATION_FAILED"
    ErrCodeUserBlocked         TransferMessageErrorCode = "USER_BLOCKED"
    ErrCodeBotNotFound         TransferMessageErrorCode = "BOT_NOT_FOUND"
    
    // 格式化错误
    ErrCodeFormatFailed        TransferMessageErrorCode = "FORMAT_FAILED"
    ErrCodeInvalidSymbol       TransferMessageErrorCode = "INVALID_SYMBOL"
)
```

## 实施内容

### 1. 整合 transfer_common.go

已将以下功能整合到 `TransferMessageService`：

- `BuildTransferSuccessMessage()` - 构建转账成功消息
- `SendTransferNotificationToReceiver()` - 发送通知给接收方  
- `HandleTransferSuccess()` - 统一处理转账成功逻辑

### 2. 结构化错误处理

实现了 `TransferMessageError` 结构体，提供：

- 标准化的错误码
- 详细的错误信息
- 原始错误链追踪

### 3. i18n 键映射

创建了 `transfer_message_i18n_mapping.go` 文件，提供：

- 新旧 i18n 键的映射关系
- 向后兼容支持
- 统一的键管理

### 4. 更新现有代码

已更新以下文件使用新的统一服务：

- `transfer_callback_handler.go` - 使用 `HandleTransferSuccess`
- `transfer_password_handler.go` - 使用 `BuildTransferMessage` 和 `HandleTransferSuccess`

## 使用示例

### 构建转账消息

```go
req := &TransferMessageRequest{
    Transfer:    transfer,
    MessageType: MessageTypeStatus,
    ViewerType:  ViewerTypeSender,
}

resp, err := service.TransferMessage().BuildTransferMessage(ctx, req)
if err != nil {
    // 处理错误
}
```

### 处理转账成功

```go
successCtx := &TransferSuccessContext{
    Transfer:         transfer,
    SenderTelegramID: senderID,
    ChatID:          chatID,
    MessageID:       messageID,
    CallbackQueryID: callbackID,
}

resp, err := service.TransferMessage().HandleTransferSuccess(ctx, successCtx)
```

### 发送转账通知

```go
req := &TransferNotificationRequestV2{
    Transfer:         transfer,
    ReceiverTgID:     receiverTelegramID,
    NotificationType: NotificationTypeCompleted,
}

err := service.TransferMessage().SendTransferNotification(ctx, req)
```

## 优势

### 1. 代码复用
- 消除重复代码
- 统一的业务逻辑实现
- 减少维护成本

### 2. 一致性保证
- 统一的消息格式
- 标准化的错误处理
- 一致的用户体验

### 3. 可扩展性
- 易于添加新的消息类型
- 支持多语言扩展
- 灵活的配置选项

### 4. 可测试性
- 独立的服务层便于单元测试
- 模拟依赖更容易
- 测试覆盖率提升

## 向后兼容

### 保留的功能

1. 所有现有的 i18n 键继续有效
2. 原有的函数签名通过包装层保持兼容
3. 业务逻辑保持不变

### 迁移建议

1. 新功能直接使用 `TransferMessageService`
2. 现有代码逐步迁移
3. 保留旧接口作为过渡

## 测试策略

### 单元测试

- 测试各种消息类型的构建
- 测试错误处理路径
- 测试金额格式化逻辑

### 集成测试

- 测试完整的转账流程
- 测试通知发送
- 测试多语言支持

### 回归测试

- 确保现有功能正常
- 验证向后兼容性
- 检查性能影响

## 未来优化

### 性能优化
- 实现消息模板缓存
- 优化数据库查询
- 减少重复计算

### 功能增强
- 支持更多消息格式（Markdown、富文本）
- 添加消息预览功能
- 实现消息版本管理

### 监控改进
- 添加消息发送成功率监控
- 实现错误率告警
- 记录性能指标

## 总结

通过实施转账消息统一化方案，我们成功地：

1. 整合了分散的代码，提高了代码复用性
2. 实现了结构化的错误处理机制
3. 保证了消息格式的一致性
4. 提供了良好的向后兼容性
5. 为未来的功能扩展奠定了基础

这个统一化方案不仅解决了当前的问题，还为系统的长期维护和发展提供了坚实的基础。