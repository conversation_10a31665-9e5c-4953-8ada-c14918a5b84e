# Red Packet Claims 用户ID修复文档

## 问题描述

`red_packet_claims` 表中存在两个关键问题：

1. **创建红包时**：`sender_user_id` 字段被错误地设置为 Telegram ID，而不是 `users` 表的 ID
2. **领取红包时**：`claimer_user_id` 和 `receiver_user_id` 字段被错误地设置为 Telegram ID，而不是 `users` 表的 ID

## 数据关系说明

- `users` 表：主用户表，主键为 `id`
- `user_backup_accounts` 表：存储 Telegram 账户信息，通过 `user_id` 字段关联到 `users.id`
- `red_packet_claims` 表：红包领取记录表，所有用户ID字段都应该引用 `users.id`

## 修复内容

### 1. 数据修复脚本

**文件**: `scripts/fix_red_packet_claims_user_ids.sql`

此脚本会：
- 将 `sender_user_id` 中的 Telegram ID 转换为正确的 `users.id`
- 将 `receiver_user_id` 中的 Telegram ID 转换为正确的 `users.id`
- 将 `claimer_user_id` 中的 Telegram ID 转换为正确的 `users.id`
- 提供验证查询来检查修复结果

### 2. 代码修复

#### 创建红包代码修复

**文件**: `internal/logic/red_packet/create_red_packet_v2.go`

**修改位置**: `createRedPacketClaims` 方法

```go
// 修复前
SenderUserId: uint64(input.CreatorUserId), // 错误：使用了 Telegram ID

// 修复后  
SenderUserId: uint64(input.UserId), // 正确：使用 users 表的 ID
```

#### 领取红包代码修复

**文件**: `internal/logic/red_packet/claim_red_packet.go`

**修改位置**: `ClaimRedPacket` 方法

```go
// 修复前
updateClaimData := g.Map{
    dao.RedPacketClaims.Columns().ClaimerUserId: userID, // 错误：使用了 Telegram ID
    // 缺少 ReceiverUserId 设置
}

// 修复后
updateClaimData := g.Map{
    dao.RedPacketClaims.Columns().ClaimerUserId:  claimerUser.Id, // 正确：使用 users 表的 ID
    dao.RedPacketClaims.Columns().ReceiverUserId: claimerUser.Id, // 新增：设置接收方用户 ID
}
```

## 使用方法

### 1. 执行数据修复

```bash
# 1. 备份数据库（强烈建议）
mysqldump -u username -p database_name red_packet_claims > red_packet_claims_backup.sql

# 2. 执行修复脚本
mysql -u username -p database_name < scripts/fix_red_packet_claims_user_ids.sql
```

### 2. 验证修复结果

```bash
# 执行验证脚本
mysql -u username -p database_name < scripts/verify_red_packet_claims_fix.sql
```

### 3. 测试代码修复

```bash
# 运行测试脚本
go run scripts/test_red_packet_user_id_fix.go
```

## 验证检查项

### 数据完整性检查

1. **外键关系验证**：确保所有用户ID都能在 `users` 表中找到对应记录
2. **数据一致性验证**：确保 `receiver_user_id` 和 `claimer_user_id` 在已领取的红包中相等
3. **Telegram ID 残留检查**：确保没有遗留的 Telegram ID 格式数据

### 功能测试

1. **创建红包测试**：验证新创建的红包分配记录中 `sender_user_id` 正确
2. **领取红包测试**：验证领取红包时 `claimer_user_id` 和 `receiver_user_id` 正确设置
3. **查询功能测试**：验证基于用户ID的查询功能正常工作

## 注意事项

### 执行前准备

1. **备份数据库**：在生产环境执行前务必备份相关表
2. **测试环境验证**：先在测试环境执行并验证结果
3. **停机维护**：建议在低峰期执行，避免数据不一致

### 潜在风险

1. **孤立记录**：可能存在用户已删除但红包记录仍存在的情况
2. **数据量大**：如果红包记录很多，修复可能需要较长时间
3. **并发问题**：修复期间避免新的红包操作

### 回滚方案

如果修复出现问题，可以：

1. 从备份恢复 `red_packet_claims` 表
2. 检查并修复导致问题的具体原因
3. 重新执行修复流程

## 修复后的数据结构

修复完成后，`red_packet_claims` 表的用户ID字段将正确引用 `users` 表：

- `sender_user_id` → `users.id`（发送方用户ID）
- `receiver_user_id` → `users.id`（接收方用户ID，仅已领取的红包）
- `claimer_user_id` → `users.id`（领取者用户ID，仅已领取的红包）

这样可以确保：
- 数据关系的完整性和一致性
- 查询性能的优化（正确的外键关系）
- 业务逻辑的正确性（用户权限、统计等）
