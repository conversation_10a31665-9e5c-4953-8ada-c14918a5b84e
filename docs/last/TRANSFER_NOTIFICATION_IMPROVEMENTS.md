# 转账通知消息改进

## 概述

根据用户需求，对转账通知消息进行了以下改进：

1. **来自字段改进**: 显示可点击的用户名，昵称用括号包裹
2. **订单号显示**: 在金额下方增加订单号展示
3. **取消重复通知**: 移除定时任务中的重复通知功能

## 修改内容

### 1. 国际化文件更新

#### 中文 (zh-CN.toml)
```toml
# 修改前
transferReceivedNotification = "💰 您收到了一笔转账！\n\n来自：%s\n金额：%s %s\n\n转账已到账，请查看余额。"

# 修改后
transferReceivedNotification = "💰 您收到了一笔转账！\n\n来自：%s (%s)\n金额：%s %s\n订单号：%s\n\n转账已到账，请查看余额。"
```

#### 英文 (en.toml)
```toml
# 修改前
transferReceivedNotification = "💰 You received a transfer!\n\nFrom: %s\nAmount: %s %s\n\nThe transfer has been credited to your account."

# 修改后
transferReceivedNotification = "💰 You received a transfer!\n\nFrom: %s (%s)\nAmount: %s %s\nOrder ID: %s\n\nThe transfer has been credited to your account."
```

### 2. 转账通知逻辑更新

#### transfer_common.go
- 增加了获取发送方 Telegram ID 的逻辑
- 构建可点击的用户名链接 `<a href="tg://user?id={telegram_id}">{username}</a>`
- 分离用户名和昵称的显示
- 更新消息参数以包含订单号

#### transfer_message.go
- 在 `buildNotificationMessage` 函数中实现了相同的逻辑
- 确保统一的转账消息服务也支持新格式

### 3. 定时任务优化

#### transfer_notification.go
- 移除了重复发送通知的逻辑
- 现在只标记通知为已发送状态，避免重复通知
- 减少了不必要的机器人实例获取

## 消息格式对比

### 修改前
```
💰 您收到了一笔转账！

来自：koll1
金额：1 USDT

转账已到账，请查看余额。
```

### 修改后
```
💰 您收到了一笔转账！

来自：@koll1 (koll1)
金额：1 USDT
订单号：8c93c422-3b3b-48df-8424-da9e373a911f

转账已到账，请查看余额。
```

## 技术实现细节

### 可点击用户名实现
```go
// 获取发送方 Telegram ID
senderTelegramId, err := service.BackupAccounts().GetTelegramIdByUserId(ctx, transfer.SenderUserId)

// 构建可点击链接
var clickableSenderName string
if senderTelegramId > 0 {
    clickableSenderName = fmt.Sprintf("<a href=\"tg://user?id=%d\">%s</a>", senderTelegramId, senderName)
} else {
    clickableSenderName = senderName
}
```

### 昵称获取逻辑
```go
// 获取发送方信息
sender, _ := service.User().GetUserByUserId(ctx, transfer.SenderUserId)
senderNickname := transfer.SenderUsername
if sender != nil && sender.Nickname != "" {
    senderNickname = sender.Nickname
}
```

## 影响范围

1. **转账通知消息**: 所有新的转账通知将使用新格式
2. **定时任务**: 不再发送重复通知，只标记状态
3. **多语言支持**: 中文和英文都已更新
4. **向后兼容**: 现有代码结构保持不变，只是消息格式改进

## 测试建议

1. 测试转账通知消息的显示格式
2. 验证可点击用户名链接功能
3. 确认订单号正确显示
4. 检查定时任务不再发送重复通知
5. 测试多语言环境下的消息格式

## 注意事项

- 消息使用 HTML 解析模式以支持可点击链接
- 如果无法获取 Telegram ID，将回退到普通文本显示
- 定时任务现在主要用于清理未标记的通知状态，而不是发送通知
