# 用户ID显示修复测试

## 问题描述
在多个地方，用户ID显示的是数据库ID而不是Telegram ID，用户名也可能显示为空：

### 转账相关
1. 转账成功消息中用户ID显示的是数据库ID (26) 而不是Telegram ID
2. 用户名显示为空

### 红包相关
1. 红包领取通知中用户链接使用的是数据库ID而不是Telegram ID

## 修复内容

### 修复的文件
1. `internal/bot/transfer/transfer_callback_handler.go` - 两处转账成功消息
2. `internal/bot/transfer/transfer_password_handler.go` - 一处转账成功消息
3. `internal/task/red_packet_claim_notification.go` - 红包领取通知中的用户链接

### 修复逻辑
在每个转账成功消息生成处，添加了以下逻辑：

```go
// Get receiver's Telegram ID and username from backup accounts
receiverTelegramId, err := service.BackupAccounts().GetTelegramIdByUserId(ctx, transfer.ReceiverUserId)
if err != nil {
    g.Log().Errorf(ctx, "Failed to get receiver Telegram ID for transfer success message: %v", err)
    receiverTelegramId = int64(transfer.ReceiverUserId) // Fallback to database ID
}

// Get receiver's Telegram username from backup accounts
receiverBackupAccount, err := service.BackupAccounts().GetBackupAccountByTelegramId(ctx, receiverTelegramId)
receiverTelegramUsername := transfer.ReceiverUsername // Fallback
if err == nil && receiverBackupAccount != nil && receiverBackupAccount.TelegramUsername != "" {
    receiverTelegramUsername = receiverBackupAccount.TelegramUsername
}

// Use the detailed success message format
successMsg := i18n.Tf(ctx, "{#transferSuccessNotifyDetailed}",
    receiverName,
    receiverTelegramId,        // 现在使用Telegram ID而不是数据库ID
    receiverName,
    receiverTelegramUsername,  // 现在使用正确的Telegram用户名
    amountStr,
    selectedSymbol,
    transfer.Key,
)
```

## 测试步骤

1. 创建一个转账
2. 完成转账
3. 检查转账成功消息中的用户ID和用户名是否正确显示

## 预期结果

转账成功消息应该显示：
- 用户ID: [Telegram ID] (而不是数据库ID)
- 用户名: [正确的Telegram用户名] (而不是空)

### 红包领取通知修复逻辑

```go
// Get claimer's Telegram ID from backup accounts
claimerTelegramId, err := service.BackupAccounts().GetTelegramIdByUserId(ctx, uint64(claim.ClaimerUserId))
if err != nil {
    g.Log().Errorf(ctx, "Failed to get claimer Telegram ID for red packet claim notification: %v", err)
    claimerTelegramId = claim.ClaimerUserId // Fallback to database ID
}

if claim.ReceiverUsername != "" {
    claimerDisplay = fmt.Sprintf(`<a href="tg://user?id=%d">@%s</a>`, claimerTelegramId, claim.ReceiverUsername)
} else {
    // 如果没有用户名，使用Telegram ID
    claimerDisplay = fmt.Sprintf(`<a href="tg://user?id=%d">User %d</a>`, claimerTelegramId, claimerTelegramId)
}
```

## 数据流

### 转账消息数据流
1. `transfer.ReceiverUserId` (数据库用户ID)
2. → `service.BackupAccounts().GetTelegramIdByUserId()`
3. → `receiverTelegramId` (Telegram ID)
4. → 显示在消息中

1. `transfer.ReceiverUsername` (可能为空的用户名)
2. → `service.BackupAccounts().GetBackupAccountByTelegramId()`
3. → `receiverBackupAccount.TelegramUsername` (正确的Telegram用户名)
4. → 显示在消息中

### 红包通知数据流
1. `claim.ClaimerUserId` (数据库用户ID)
2. → `service.BackupAccounts().GetTelegramIdByUserId()`
3. → `claimerTelegramId` (Telegram ID)
4. → 用于创建Telegram用户链接
