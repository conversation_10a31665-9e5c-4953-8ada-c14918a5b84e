# 闪兑执行时汇率修复

## 问题描述

用户反馈：闪兑的计算，当滑点在配置的范围内时需要按照执行时的汇率进行兑换，现在还是按照报价时的汇率进行兑换的。

### 原始问题
- **报价阶段**：系统生成报价时记录当时的汇率
- **执行阶段**：即使价格发生变化且滑点在允许范围内，系统仍使用报价时的汇率计算最终兑换金额
- **用户期望**：当滑点在允许范围内时，应该使用执行时的最新汇率重新计算兑换金额

## 修复方案

### 主要修改文件
- `internal/service/v2/impl/swap_execute_order.go`

### 核心修改逻辑

1. **价格获取增强** (第118-154行)
   - 支持法币对 (CNY) 和普通加密货币对的价格获取
   - 使用 `GetFiatPrice` 处理CNY相关交易对
   - 使用 `GetRealTimePrice` 处理常规加密货币对

2. **执行时汇率重新计算** (第178-248行)
   ```go
   // 滑点检查通过后，使用执行时汇率重新计算
   if order.TradeType == "buy" {
       // 买入：保持用户支付的计价代币数量不变，重新计算能获得的基础代币数量
       newQuoteAmount = order.AmountQuote  
       newBaseAmount = newQuoteAmount.Div(currentPrice)
   } else {
       // 卖出：保持用户支付的基础代币数量不变，重新计算能获得的计价代币数量
       newBaseAmount = order.AmountBase   
       newQuoteAmount = newBaseAmount.Mul(currentPrice)
   }
   ```

3. **手续费重新计算**
   - 使用执行时价格和重新计算的金额调用 `FeeCalculator`
   - 确保手续费也基于最新的执行时汇率

4. **数据库更新**
   - 保存执行时的价格、金额、手续费到订单记录
   - 更新 `price`、`amount_base`、`amount_quote`、`fee_amount` 等字段

## 测试验证

### 新增测试文件
- `internal/service/v2/impl/swap_execute_order_test.go`

### 测试覆盖场景

1. **价格重新计算逻辑测试** (`TestExecuteOrderPriceRecalculation`)
   - 买入场景：保持支付USDT不变，重算收到的ETH
   - 卖出场景：保持支付ETH不变，重算收到的USDT

2. **滑点计算测试** (`TestSlippageCalculation`)
   - 买入时价格上涨的滑点计算
   - 卖出时价格下跌的滑点计算

3. **手续费重新计算测试** (`TestFeeRecalculationWithExecutionPrice`)
   - 验证使用执行时价格重新计算手续费的准确性

4. **具体场景测试** (`TestExecutionTimePriceScenarios`)
   - **用户反馈场景**：买入ETH价格小幅变动，验证按执行时价格给用户更多代币
   - **价格上涨但在滑点范围内**：验证交易成功并按新价格计算
   - **价格暴涨超出滑点范围**：验证交易失败
   - **卖出价格下跌在范围内**：验证按新价格成交

### 测试结果
```bash
=== RUN   TestExecutionTimePriceScenarios
=== RUN   TestExecutionTimePriceScenarios/用户反馈场景-买入ETH价格小幅上涨
    Quote price: 2407.11471 -> Execute price: 2405.87347
    Price change: -0.0515654694329%, Slippage: 0.04838296509766%
    Recalculated amounts - From: 9000, To: 3.737107999702491
--- PASS: TestExecutionTimePriceScenarios (0.00s)
```

## 修复效果

### 修复前
- 滑点在范围内时，用户仍按报价时汇率获得兑换金额
- 即使执行时价格对用户更有利，用户也无法受益

### 修复后
- ✅ 滑点在范围内时，**按执行时汇率重新计算兑换金额**
- ✅ 价格变动对用户有利时，用户能获得更多代币
- ✅ 价格变动对用户不利但在滑点范围内时，按实际执行价格成交
- ✅ 滑点超出范围时，订单失败（原有保护机制保持不变）

### 具体示例
用户支付 9000 USDT 买入 ETH：
- **报价时汇率**：1 ETH = 2407.11471 USDT
- **执行时汇率**：1 ETH = 2405.87347 USDT（对用户更有利）
- **修复前**：用户收到 `9000 ÷ 2407.11471 = 3.7314 ETH`
- **修复后**：用户收到 `9000 ÷ 2405.87347 = 3.7371 ETH`（更多）

## 兼容性

- ✅ 保持所有现有API接口不变
- ✅ 现有滑点保护机制完全保留
- ✅ 手续费计算策略保持一致
- ✅ 所有现有测试通过

## 部署注意事项

1. 该修改只影响订单执行逻辑，不影响报价生成
2. 数据库字段会被更新为执行时的实际值
3. 建议在低峰期部署，避免影响用户交易
4. 部署后密切监控滑点相关的告警和指标

## 验证方法

部署后可通过以下方式验证修复效果：
1. 查看订单表中 `price` 字段是否更新为执行时价格
2. 对比用户实际收到的代币数量与报价时的预期数量
3. 监控滑点在允许范围内的交易是否按执行时汇率成交 