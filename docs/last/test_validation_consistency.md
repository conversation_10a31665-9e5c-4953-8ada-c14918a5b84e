# Validation Consistency Test Plan

## Problem Solved
The issue was that `inline_query` and `chosen_inline_result` handlers used different validation functions:
- `inline_query` used `ValidateTransferAmount()` (includes min/max limits)
- `chosen_inline_result` used `ValidateAmountDecimal()` (only format validation)

This inconsistency allowed users to:
1. See transfer options for amounts below minimum in inline query results
2. Click on those results and have them processed in chosen_inline_result
3. Get "claim" buttons for invalid transfer amounts

## Solution Implemented
Created `ValidateInlineTransferRequest()` unified validation function that:
1. Performs comprehensive validation (format + min/max limits + token + balance + password check)
2. Returns structured validation result with all necessary data
3. Used by both `inline_query` and `chosen_inline_result` handlers
4. Ensures consistent validation rules across the entire flow

## Test Cases to Verify

### 1. Amount Below Minimum
- Input: `0.001 USDT` (assuming minimum is 0.01 USDT)
- Expected: Both handlers reject with same error
- Verification: No claim buttons should appear

### 2. Invalid Format
- Input: `abc USDT`
- Expected: Both handlers reject with format error
- Verification: No claim buttons should appear

### 3. Zero/Negative Amount
- Input: `0 USDT` or `-1 USDT`
- Expected: Both handlers reject with format error
- Verification: No claim buttons should appear

### 4. Valid Amount
- Input: `1.0 USDT` (above minimum, sufficient balance)
- Expected: Both handlers accept and process correctly
- Verification: Appropriate buttons appear (password or claim)

## Manual Testing Steps

1. **Test Below Minimum Amount:**
   ```
   @botname search USDT:0.001
   ```
   - Should show error in inline query results
   - Should NOT show any clickable transfer options

2. **Test Invalid Format:**
   ```
   @botname search USDT:abc
   ```
   - Should show error in inline query results
   - Should NOT show any clickable transfer options

3. **Test Valid Amount:**
   ```
   @botname search USDT:1.0
   ```
   - Should show valid transfer option in inline query results
   - Clicking should create transfer and show appropriate button (password/claim)
   - Button should only appear AFTER successful validation

## Code Changes Made

### 1. Created Unified Validation (`internal/utility/validation.go`)
- Added `InlineTransferValidationResult` struct
- Added `ValidateInlineTransferRequest()` function
- Performs all validation steps in one place

### 2. Updated Inline Query Handler (`internal/bot/inline_query/transfer_confirmation.go`)
- Replaced separate validation calls with unified validation
- Uses validation result data instead of re-fetching
- Prevents showing options for invalid amounts

### 3. Updated Chosen Inline Result Handler (`internal/bot/chosen_inline_result/transfer_handler.go`)
- Replaced `ValidateAmountDecimal()` with unified validation
- Added balance check before processing
- Only updates buttons after successful validation
- Uses validation result data for efficiency

## Benefits

1. **Consistency:** Same validation logic in both handlers
2. **Security:** No claim buttons for invalid amounts
3. **Performance:** Single validation call with cached results
4. **Maintainability:** Centralized validation logic
5. **User Experience:** Clear error messages, no confusing states

## Verification Commands

To test the fix manually:

```bash
# Test with amount below minimum
@your_bot_name search USDT:0.001

# Test with invalid format  
@your_bot_name search USDT:invalid

# Test with valid amount
@your_bot_name search USDT:1.0
```

The first two should show error messages and no clickable options.
The third should show a valid transfer option that works correctly when clicked.
