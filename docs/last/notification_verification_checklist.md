# 统一通知服务验证检查清单

## 📋 快速验证指南

### 🚀 立即执行的验证步骤

#### 1. 运行测试脚本
```bash
# 在项目根目录执行
cd /Users/<USER>/Desktop/xpay/bot-api
go run scripts/test_notification_service.go
```

**预期结果**：
- [ ] 所有通知类型都显示"发送成功"
- [ ] 没有错误日志输出
- [ ] 每个通知都有唯一的TraceID

#### 2. 检查Kafka消息
```bash
# 检查unified_notifications topic是否收到消息
# 使用你的Kafka管理工具或命令行工具查看
kafka-console-consumer --bootstrap-server localhost:9092 \
  --topic unified_notifications --from-beginning
```

**预期结果**：
- [ ] 能看到JSON格式的通知消息
- [ ] 消息包含正确的notification_key
- [ ] 消息包含正确的user_id和payload

#### 3. 检查应用日志
```bash
# 查看应用日志中的通知相关信息
tail -f logs/app.log | grep -i notification
```

**预期结果**：
- [ ] 看到"Notification sent successfully"日志
- [ ] 没有"Failed to send notification"错误日志
- [ ] TraceID在日志中正确传递

## 🔍 详细功能验证

### 支付通知验证

#### 测试场景1：正常支付流程
- [ ] **创建支付请求**
  - 使用真实的用户ID创建支付请求
  - 完成支付流程
  
- [ ] **验证通知发送**
  - [ ] 付款方收到"支付已发送"通知
  - [ ] 收款方收到"支付已接收"通知
  - [ ] 通知内容包含正确的金额和用户名
  - [ ] 通知语言符合用户设置

#### 测试场景2：异常情况处理
- [ ] **无效用户ID测试**
  - 使用不存在的用户ID
  - 验证系统不会崩溃
  - 检查错误日志记录

### 红包通知验证

#### 测试场景1：红包过期处理
- [ ] **创建短期红包**
  ```bash
  # 创建一个1分钟后过期的红包用于测试
  # 可以通过修改数据库中的expires_at字段来模拟
  ```
  
- [ ] **等待过期处理**
  - [ ] 等待1-2分钟（定时任务每分钟执行一次）
  - [ ] 检查红包状态是否更新为"expired"
  - [ ] 验证创建者收到过期通知

#### 测试场景2：红包被领完
- [ ] **创建小额红包**
  - 创建只有1个红包的小额红包
  - 立即领取该红包
  
- [ ] **验证通知**
  - [ ] 红包状态更新为"empty"
  - [ ] 创建者收到"被领完"通知
  - [ ] 通知包含正确的总金额信息

### 提现通知验证

#### 测试场景1：提现处理流程
- [ ] **创建提现请求**
  - 通过正常流程创建提现请求
  - 记录提现请求ID
  
- [ ] **等待处理**
  - [ ] 等待5-10分钟（定时任务每5分钟执行一次）
  - [ ] 检查提现状态是否更新
  - [ ] 验证收到成功或失败通知

#### 测试场景2：提现失败退款
- [ ] **模拟提现失败**
  - 可以通过修改模拟逻辑来强制失败
  - 验证用户余额是否正确退还
  - 检查失败通知是否发送

## 🔧 系统集成验证

### Kafka集成验证

#### 消息格式验证
- [ ] **检查消息结构**
  ```json
  {
    "notification_key": "payment_sent",
    "user_id": 12345,
    "user_language": "",
    "timestamp": 1234567890,
    "trace_id": "payment_123_456",
    "payload": {...}
  }
  ```

#### 消费者验证
- [ ] **unified_notifier正常工作**
  - 检查unified_notifier进程是否运行
  - 验证消息被正确消费
  - 确认用户收到Telegram通知

### 定时任务验证

#### Cron任务状态
- [ ] **检查定时任务运行状态**
  ```bash
  # 查看cron相关日志
  tail -f logs/app.log | grep -i cron
  ```
  
- [ ] **验证任务执行**
  - [ ] "HandleExpiredRedPackets"每分钟执行
  - [ ] "BatchProcessPendingWithdraws"每5分钟执行
  - [ ] 任务执行无错误

## 📊 性能验证

### 响应时间验证
- [ ] **通知发送延迟**
  - 记录从业务操作到通知发送的时间
  - 验证延迟在可接受范围内（< 1秒）

### 并发性能验证
- [ ] **并发支付测试**
  - 同时创建多个支付请求
  - 验证所有通知都能正确发送
  - 检查系统资源使用情况

## 🛡️ 错误处理验证

### Kafka故障模拟
- [ ] **模拟Kafka不可用**
  - 临时停止Kafka服务
  - 验证业务流程不受影响
  - 检查错误日志记录

### 服务异常处理
- [ ] **无效参数测试**
  - 发送无效的通知请求
  - 验证错误处理正确
  - 确认系统稳定性

## 📱 用户体验验证

### Telegram通知验证
- [ ] **通知内容准确性**
  - 金额显示正确
  - 用户名显示正确
  - 时间显示正确

- [ ] **多语言支持**
  - 测试不同语言用户的通知
  - 验证语言切换正确

### 通知时效性
- [ ] **实时性验证**
  - 操作完成后立即收到通知
  - 通知顺序正确

## 🔄 兼容性验证

### 向后兼容性
- [ ] **现有功能不受影响**
  - 转账功能正常
  - 红包功能正常
  - 其他业务功能正常

### 数据一致性
- [ ] **数据库状态正确**
  - 支付状态正确更新
  - 红包状态正确更新
  - 提现状态正确更新

## 📝 验证报告模板

### 验证结果记录
```
验证日期：____年__月__日
验证人员：__________
验证环境：__________

功能验证结果：
□ 支付通知：通过/失败
□ 红包通知：通过/失败  
□ 提现通知：通过/失败
□ 充值通知：通过/失败

性能验证结果：
□ 响应时间：____ms
□ 并发处理：通过/失败
□ 资源使用：正常/异常

发现的问题：
1. ________________
2. ________________
3. ________________

建议改进：
1. ________________
2. ________________
3. ________________
```

## 🚨 问题排查指南

### 常见问题及解决方案

#### 问题1：通知发送失败
**症状**：日志显示"Failed to send notification"
**排查步骤**：
1. 检查Kafka服务是否正常
2. 检查topic配置是否正确
3. 检查网络连接
4. 检查消息格式是否正确

#### 问题2：用户收不到通知
**症状**：Kafka有消息但用户收不到Telegram通知
**排查步骤**：
1. 检查unified_notifier是否运行
2. 检查用户ID是否正确
3. 检查Telegram bot是否正常
4. 检查用户是否屏蔽了bot

#### 问题3：定时任务不执行
**症状**：红包不会过期或提现不会处理
**排查步骤**：
1. 检查cron服务是否启动
2. 检查定时任务配置
3. 检查数据库连接
4. 检查任务执行权限

## 📞 支持联系

如果验证过程中遇到问题，可以：
1. 查看详细日志：`tail -f logs/app.log`
2. 检查系统状态：`ps aux | grep bot-api`
3. 参考文档：`docs/` 目录下的相关文档
4. 查看示例：`examples/` 目录下的使用示例

---

**重要提醒**：请在生产环境部署前完成所有验证项目，确保系统稳定可靠。
