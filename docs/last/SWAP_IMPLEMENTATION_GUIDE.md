# Swap 功能实现指南

## 概述

本文档描述了改进后的 Swap（闪兑）功能实现，包括新增的安全机制、错误处理和并发控制。

## 核心改进

### 1. 结构化错误处理

引入了 `SwapError` 类型，提供了标准化的错误码和详细信息：

```go
// 错误码定义
- ErrCodeInsufficientBalance - 余额不足
- ErrCodePriceSlippage - 价格滑点过大
- ErrCodeQuoteExpired - 报价已过期
- ErrCodeOrderNotFound - 订单未找到
- ErrCodeSecurityCheckFailed - 安全检查失败
// ... 等等
```

使用示例：
```go
return model.NewSwapError(model.ErrCodeInsufficientBalance, "insufficient balance").
    WithDetail("required", fromAmount.String()).
    WithDetail("available", balance.AvailableAmount.String())
```

### 2. 订单状态机

实现了严格的订单状态流转控制：

```
pending → processing → completed
   ↓         ↓          ↓
cancelled   failed    refunding → refunded
```

状态转换验证：
```go
stateMachine := model.GetOrderStateMachine()
if err := stateMachine.ValidateTransition(currentStatus, newStatus); err != nil {
    return err
}
```

### 3. 分布式锁机制

防止并发执行同一订单：

```go
lockKey := fmt.Sprintf("swap:order:execute:%s", orderID)
err := utils.WithLock(ctx, redis, lockKey, 30*time.Second, func() error {
    // 执行订单逻辑
    return nil
})
```

### 4. 钱包服务集成

完整的资金锁定和转账流程：

```go
// 1. 锁定用户资金
fundLock, err := walletService.LockFunds(ctx, lockReq)

// 2. 执行转账
transferResult, err := walletService.Transfer(ctx, transferReq)

// 3. 释放锁定（成功或失败时）
err = walletService.UnlockFunds(ctx, fundLock.LockID)
```

### 5. 价格保护机制

- **滑点检查**：执行时重新验证价格，防止过大滑点
- **价格操纵检测**：监控价格历史，识别异常波动
- **多源价格验证**：支持从多个价格源获取并验证价格

## 使用指南

### 1. 创建报价

```go
quoteReq := &interfaces.SwapQuoteRequest{
    UserID:      userID,
    FromTokenID: ethTokenID,
    ToTokenID:   usdtTokenID,
    Amount:      decimal.NewFromFloat(1.0),
    AmountType:  "from", // 指定 from 金额
}

quote, err := swapService.CreateQuote(ctx, quoteReq)
if err != nil {
    if swapErr := model.GetSwapError(err); swapErr != nil {
        // 处理结构化错误
        switch swapErr.Code {
        case model.ErrCodeInsufficientBalance:
            // 余额不足处理
        case model.ErrCodeAmountBelowMinimum:
            // 金额过小处理
        }
    }
}
```

### 2. 创建订单

```go
orderReq := &interfaces.SwapOrderRequest{
    UserID:          userID,
    QuoteID:         quote.ID,
    PaymentPassword: encryptedPassword,
}

order, err := swapService.CreateOrder(ctx, orderReq)
```

### 3. 执行订单

```go
result, err := swapService.ExecuteOrder(ctx, order.OrderSn)
if err != nil {
    // 错误已经是结构化的 SwapError
    return handleSwapError(err)
}
```

## 配置说明

在 `config/swap_config.yaml` 中配置：

```yaml
swap:
  price:
    max_slippage: 0.01  # 最大滑点 1%
  quote:
    expiry_seconds: 30  # 报价有效期
  limits:
    user:
      daily_volume_usd: 50000  # 用户日限额
```

## 安全考虑

1. **并发控制**：使用分布式锁防止重复执行
2. **资金安全**：先锁定资金，失败时自动退还
3. **价格保护**：多重价格验证机制
4. **状态管理**：严格的状态机控制
5. **审计日志**：完整的操作记录

## 测试

运行测试：
```bash
go test ./internal/service/v2/impl -run TestSwap
```

## 监控指标

关键监控点：
- 订单成功率
- 平均滑点
- 价格偏差
- 执行时间
- 错误分布

## 故障处理

1. **价格服务不可用**：返回 `ErrCodePriceUnavailable`
2. **资金锁定失败**：自动回滚，返回 `ErrCodeFundsLocked`
3. **转账失败**：自动退款，返回详细错误
4. **超时处理**：分布式锁自动释放，资金锁定自动过期

## 未来改进

1. 实现多价格源聚合
2. 添加更多交易对支持
3. 优化大额交易的分批执行
4. 增强反欺诈检测
5. 实现更精细的限额控制