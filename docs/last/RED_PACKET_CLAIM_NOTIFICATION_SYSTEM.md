# Red Packet Claim Notification System

## Overview

The Red Packet Claim Notification System is a scheduled task that monitors red packet claims and sends notifications to creators when their red packets are claimed. It also triggers message updates to refresh the remaining balance and display a leaderboard of top claimers.

## Architecture

### Components

1. **Scheduled Task** (`red_packet_claim_notification.go`)
   - Runs every 30 seconds
   - Processes unnotified claims in batches
   - Sends notifications to creators
   - Triggers message updates via Kafka

2. **Database Tables**
   - `red_packet_claims`: Tracks all claims with notification status
   - `red_packets`: Main red packet information
   - `red_packets_infors`: Tracks inline messages for updates

3. **Integration Points**
   - **Notification Service**: Sends custom notifications to users
   - **Kafka Service**: Triggers message updates
   - **Task Coordinator**: Prevents concurrent executions

## Features

### 1. Batch Processing
- Processes claims in batches of 100 (configurable)
- Reduces database load
- Efficient memory usage

### 2. Sequential Notifications
- Notifications for the same red packet are sent sequentially
- Maintains correct timeline order
- Prevents race conditions

### 3. Concurrent Processing
- Different red packets are processed concurrently
- Default concurrency: 5 workers
- Configurable based on system capacity

### 4. Graceful Shutdown
- Responds to shutdown signals
- Completes current batch before stopping
- No data loss during shutdown

### 5. Comprehensive Monitoring
- Detailed metrics for each execution
- Performance warnings
- Error tracking and reporting

### 6. Retry Mechanism
- Automatic retry for failed notifications
- Exponential backoff
- Maximum 3 attempts (configurable)

## Configuration

```go
type RedPacketClaimNotificationConfig struct {
    BatchSize         int           // Default: 100
    Concurrency       int           // Default: 5
    ProcessTimeout    time.Duration // Default: 30s
    RetryAttempts     int           // Default: 3
    RetryDelay        time.Duration // Default: 1s
    NotificationDelay time.Duration // Default: 100ms
}
```

## Notification Format

### Chinese
```
🎉 红包被领取
👤 领取人: @username
💰 金额: 100 TRX
🧧 红包ID: abc123
⏰ 时间: 2025-01-06 15:04:05
```

### English
```
🎉 Red Packet Claimed
👤 Claimed by: @username
💰 Amount: 100 TRX
🧧 Red Packet ID: abc123
⏰ Time: 2025-01-06 15:04:05
```

## Message Updates

When claims are processed, the system:
1. Collects all affected red packet IDs
2. De-duplicates the list
3. Sends update requests to Kafka
4. Updates show:
   - Current remaining balance and quantity
   - Top 10 claimers with amounts
   - Emojis for top 3 (🥇🥈🥉)

## Performance Metrics

The system tracks and reports:
- **Total Duration**: Overall task execution time
- **Claims Processed**: Success/failure counts
- **Notifications**: Sent/failed counts
- **DB Query Time**: Time spent querying database
- **Max Batch Process Time**: Longest batch processing
- **Average Notification Time**: Per-notification average

### Performance Warnings
- Average notification time > 500ms
- Max batch process time > 10s
- Notification failure rate > 10%

## Database Schema

### red_packet_claims
```sql
notification_sent TINYINT(1) DEFAULT 0  -- 0: not sent, 1: sent
notification_sent_at DATETIME           -- When notification was sent
```

## Error Handling

1. **Database Errors**: Logged and task fails
2. **Notification Failures**: Retry with backoff
3. **Kafka Errors**: Logged but doesn't stop processing
4. **Context Cancellation**: Graceful shutdown

## Monitoring and Debugging

### Log Levels
- **Notice**: Task start/complete with metrics
- **Info**: Batch processing progress
- **Debug**: Individual claim processing
- **Warning**: Performance issues, non-critical errors
- **Error**: Critical failures

### Example Log Output
```
[RedPacketClaimNotification] Task completed with detailed metrics:
├─ Total Duration: 2.5s
├─ Claims Processed: 150 (success: 148, failure: 2)
├─ Notifications: sent=148, failed=2
├─ Red Packets Updated: 25 (messages queued: 25)
├─ DB Query Time: 250ms
├─ Max Batch Process Time: 1.2s
└─ Avg Notification Time: 15ms
```

## Testing

### Unit Tests
- Configuration validation
- Message building
- Retry mechanism
- Graceful shutdown
- Concurrent processing

### Integration Tests
- Full processing flow
- Performance benchmarks
- Memory allocation tests
- Synchronization tests

### Running Tests
```bash
# Unit tests
go test ./internal/task/

# Integration tests
go test -tags=integration ./internal/task/

# Benchmarks
go test -bench=. ./internal/task/
```

## Deployment Considerations

1. **Database Indexes**: Ensure index on `notification_sent` field
2. **Kafka Topics**: Configure `redPacketMessageUpdateRequests` topic
3. **Rate Limiting**: Adjust notification delay based on Telegram limits
4. **Monitoring**: Set up alerts for failure rates
5. **Scaling**: Adjust batch size and concurrency based on load

## Future Improvements

1. **Dynamic Configuration**: Allow runtime configuration changes
2. **Priority Queue**: Process high-value claims first
3. **Bulk Notifications**: Batch notifications to same user
4. **Circuit Breaker**: For external service failures
5. **Distributed Processing**: Support multiple instances

## Troubleshooting

### Common Issues

1. **High Failure Rate**
   - Check Telegram API limits
   - Verify user telegram IDs
   - Check notification service health

2. **Slow Processing**
   - Increase batch size
   - Add database indexes
   - Increase concurrency

3. **Message Updates Not Working**
   - Verify Kafka connectivity
   - Check topic configuration
   - Monitor Kafka consumer logs

4. **Memory Usage**
   - Reduce batch size
   - Check for memory leaks
   - Monitor goroutine count