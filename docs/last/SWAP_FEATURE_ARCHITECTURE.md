# 闪兑功能架构设计文档

## 项目概述

本文档详细描述了在当前Telegram机器人系统中实现"闪兑"功能的完整架构设计，包括数据库设计、业务逻辑、服务层和用户交互界面。

## 业务需求确认

- **交易模式**: 内部资金池模式
- **价格来源**: 币安API (支持工厂模式切换多渠道)
- **手续费**: 支持固定金额和百分比两种模式
- **滑点处理**: 允许配置滑点容忍度
- **核心交易对**: ETH ↔ USDT

## 技术架构

### 1. 数据库设计 (SQL)

#### 1.1 闪兑订单表
```sql
-- 闪兑订单主表
CREATE TABLE `swap_orders` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_uuid` VARCHAR(36) NOT NULL UNIQUE COMMENT '订单唯一标识符',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `telegram_id` BIGINT NOT NULL COMMENT 'Telegram用户ID',
  `from_token_id` INT UNSIGNED NOT NULL COMMENT '源代币ID',
  `from_symbol` VARCHAR(20) NOT NULL COMMENT '源币种符号',
  `from_amount` DECIMAL(36, 18) NOT NULL COMMENT '源币种数量',
  `to_token_id` INT UNSIGNED NOT NULL COMMENT '目标代币ID', 
  `to_symbol` VARCHAR(20) NOT NULL COMMENT '目标币种符号',
  `expected_to_amount` DECIMAL(36, 18) NOT NULL COMMENT '预期兑换到的目标币种数量',
  `actual_to_amount` DECIMAL(36, 18) NULL COMMENT '实际兑换到的目标币种数量',
  `quoted_rate` DECIMAL(36, 18) NOT NULL COMMENT '报价时的汇率',
  `executed_rate` DECIMAL(36, 18) NULL COMMENT '执行时的实际汇率',
  `price_source` VARCHAR(50) NOT NULL DEFAULT 'binance' COMMENT '价格来源: binance, kucoin等',
  `fee_type` TINYINT NOT NULL COMMENT '手续费类型: 1-固定金额, 2-百分比',
  `fee_rate` DECIMAL(10, 6) NOT NULL COMMENT '手续费率或固定金额',
  `fee_amount` DECIMAL(36, 18) NOT NULL COMMENT '实际手续费金额',
  `fee_symbol` VARCHAR(20) NOT NULL COMMENT '手续费币种',
  `slippage_tolerance` DECIMAL(5, 4) NOT NULL DEFAULT 0.005 COMMENT '滑点容忍度 (0.5%)',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '订单状态: 1-待处理, 2-成功, 3-失败, 4-已取消, 5-已过期',
  `error_code` VARCHAR(50) NULL COMMENT '错误代码',
  `error_message` VARCHAR(255) NULL COMMENT '失败时的错误信息',
  `quote_expires_at` TIMESTAMP NOT NULL COMMENT '报价过期时间',
  `executed_at` TIMESTAMP NULL COMMENT '执行时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id_status` (`user_id`, `status`),
  INDEX `idx_telegram_id_created` (`telegram_id`, `created_at` DESC),
  INDEX `idx_status_created` (`status`, `created_at`),
  INDEX `idx_order_uuid` (`order_uuid`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
  FOREIGN KEY (`from_token_id`) REFERENCES `tokens`(`id`),
  FOREIGN KEY (`to_token_id`) REFERENCES `tokens`(`id`)
) COMMENT='闪兑订单表';
```

#### 1.2 闪兑配置表
```sql
-- 闪兑全局配置表
CREATE TABLE `swap_configs` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `trading_pair` VARCHAR(20) NOT NULL COMMENT '交易对 如: ETH_USDT',
  `from_token_id` INT UNSIGNED NOT NULL COMMENT '源代币ID',
  `from_symbol` VARCHAR(20) NOT NULL COMMENT '源币种符号',
  `to_token_id` INT UNSIGNED NOT NULL COMMENT '目标代币ID', 
  `to_symbol` VARCHAR(20) NOT NULL COMMENT '目标币种符号',
  `is_enabled` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用交易',
  `is_displayed` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否在界面显示',
  `display_order` INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
  `min_amount` DECIMAL(36, 18) NOT NULL COMMENT '最小兑换金额',
  `max_amount` DECIMAL(36, 18) NOT NULL COMMENT '最大兑换金额',
  `default_fee_type` TINYINT NOT NULL DEFAULT 2 COMMENT '默认手续费类型: 1-固定, 2-百分比',
  `default_fee_rate` DECIMAL(10, 6) NOT NULL DEFAULT 0.001 COMMENT '默认手续费率 (0.1%)',
  `default_slippage` DECIMAL(5, 4) NOT NULL DEFAULT 0.005 COMMENT '默认滑点容忍度 (0.5%)',
  `quote_validity_seconds` INT NOT NULL DEFAULT 30 COMMENT '报价有效期(秒)',
  `price_source_priority` JSON NOT NULL COMMENT '价格源优先级配置',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_trading_pair` (`trading_pair`),
  UNIQUE KEY `uk_from_to_tokens` (`from_token_id`, `to_token_id`),
  INDEX `idx_display_order` (`is_displayed`, `display_order`),
  INDEX `idx_from_symbol` (`from_symbol`),
  INDEX `idx_to_symbol` (`to_symbol`),
  FOREIGN KEY (`from_token_id`) REFERENCES `tokens`(`id`),
  FOREIGN KEY (`to_token_id`) REFERENCES `tokens`(`id`)
) COMMENT='闪兑配置表';
```


### 2. 价格服务架构

#### 2.1 独立价格监控服务
闪兑系统的价格数据由独立的**价格监控服务**提供，该服务具有以下特点：

- **实时数据订阅**：通过WebSocket订阅Binance等交易所的实时价格数据
- **Redis存储**：将价格数据存储在Redis中，供其他服务高效访问
- **数据新鲜度检测**：自动检测价格数据的时效性，防止使用过期数据
- **多重容错机制**：支持多价格源、自动重连、异常恢复

**详细设计文档**：[PRICE_MONITOR_SERVICE_DESIGN.md](./PRICE_MONITOR_SERVICE_DESIGN.md)

#### 2.2 价格服务集成架构
```mermaid
graph TB
    subgraph "Price Monitor Service (独立服务)"
        A[Binance WebSocket] --> B[Price Processor]
        C[KuCoin WebSocket] --> B
        B --> D[Price Validator]
        D --> E[Redis Writer]
        F[Health Monitor] --> B
    end
    
    subgraph "Redis Cluster"
        E --> G[Price Data Cache]
        E --> H[Health Status]
    end
    
    subgraph "Swap Service"
        I[Price Client] --> G
        I --> J[Swap Logic]
        K[Staleness Check] --> I
    end
    
    subgraph "Admin Panel"
        L[Price Monitor] --> G
        L --> H
    end
```

#### 2.3 价格客户端接口
```go
// 闪兑服务使用的价格客户端接口
type IPriceClient interface {
    // 获取实时价格（带新鲜度检查）
    GetRealTimePrice(ctx context.Context, symbol string) (*PriceData, error)
    
    // 批量获取价格
    GetMultiplePrices(ctx context.Context, symbols []string) (map[string]*PriceData, error)
    
    // 检查价格数据新鲜度
    CheckPriceFreshness(ctx context.Context, symbol string, maxStale time.Duration) error
    
    // 获取价格更新时间
    GetLastUpdateTime(ctx context.Context, symbol string) (time.Time, error)
}

// 价格数据结构
type PriceData struct {
    Symbol          string          `json:"symbol"`           // 交易对符号 如: ETHUSDT
    Price           decimal.Decimal `json:"price"`            // 当前价格
    Volume24h       decimal.Decimal `json:"volume_24h"`       // 24小时交易量
    Change24h       decimal.Decimal `json:"change_24h"`       // 24小时涨跌幅
    High24h         decimal.Decimal `json:"high_24h"`         // 24小时最高价
    Low24h          decimal.Decimal `json:"low_24h"`          // 24小时最低价
    Provider        string          `json:"provider"`         // 价格提供商
    Timestamp       time.Time       `json:"timestamp"`        // 数据时间戳
    LastUpdated     int64           `json:"last_updated"`     // Unix时间戳
}
```

### 3. 业务流程设计

#### 3.1 闪兑核心流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant T as Telegram Bot
    participant S as SwapService
    participant P as PriceService
    participant W as WalletService
    participant DB as Database
    
    U->>T: 点击闪兑按钮
    T->>S: 获取支持的交易对
    S->>T: 返回交易对列表
    T->>U: 显示币种选择界面
    
    U->>T: 选择 ETH -> USDT
    U->>T: 输入兑换金额
    T->>S: 获取实时报价
    S->>P: 检查价格数据新鲜度
    P->>S: 确认数据有效
    S->>P: 获取 ETH/USDT 价格
    P->>S: 返回价格信息
    S->>DB: 保存报价记录
    S->>T: 返回报价详情
    T->>U: 显示预览确认页面
    
    U->>T: 确认兑换
    T->>S: 执行兑换
    S->>W: 锁定用户ETH余额
    S->>P: 检查价格数据新鲜度
    P->>S: 确认数据有效
    S->>P: 再次获取最新价格
    P->>S: 返回最新价格
    
    alt 价格在滑点容忍范围内
        S->>W: 扣除ETH,增加USDT
        S->>DB: 更新订单状态为成功
        S->>T: 返回成功结果
        T->>U: 显示兑换成功
    else 价格超出滑点范围
        S->>W: 释放锁定资金
        S->>DB: 更新订单状态为失败
        S->>T: 返回失败结果
        T->>U: 显示价格变动过大,兑换失败
    end
```

#### 3.2 Telegram 用户交互流程
```mermaid
flowchart TD
    A[主菜单] --> B[点击"💱 闪兑"]
    B --> C[显示交易对列表<br/>含实时价格]
    C --> D{选择交易对方向}
    D --> E[ETH → USDT]
    D --> F[USDT → ETH] 
    D --> G[BTC → USDT]
    D --> H[USDT → BTC]
    E --> I[输入兑换数量]
    F --> I
    G --> I
    H --> I
    I --> J[获取实时报价]
    J --> K[显示预览确认页面]
    K --> L{用户选择}
    L -->|确认兑换| M[验证支付密码]
    L -->|取消| C
    M --> N{密码验证}
    N -->|通过| O[执行兑换]
    N -->|失败| P[密码错误,重新输入]
    P --> M
    O --> Q{兑换结果}
    Q -->|成功| R[显示成功页面]
    Q -->|失败| S[显示失败页面]
    R --> A
    S --> A
```

### 4. 代码架构设计

#### 4.1 Logic 层文件结构
```
internal/logic/swap/
├── init.go                    # 服务注册
├── get_swap_quote.go         # 获取兑换报价
├── create_swap_order.go      # 创建兑换订单  
├── execute_swap_order.go     # 执行兑换
├── cancel_swap_order.go      # 取消兑换
├── get_swap_history.go       # 获取兑换历史
├── validate_swap_params.go   # 参数验证
└── swap_calculator.go        # 兑换计算逻辑
```

#### 4.2 Service 层文件结构  
```
internal/service/swap.go            # 闪兑服务接口定义
internal/service/price_client.go    # 价格客户端接口定义

internal/service/v2/impl/
├── swap_service.go                 # 闪兑服务实现
├── price_client_service.go        # 价格客户端实现（连接Redis）
└── price_staleness_checker.go     # 价格新鲜度检查器
```

#### 4.3 Bot 层文件结构
```
internal/bot/swap/
├── init.go                   # 路由注册
├── handler.go               # 主处理器
├── keyboards.go             # 键盘定义
├── responses.go             # 响应消息
├── callback_handlers.go     # 回调处理
├── message_handlers.go      # 消息处理
└── validators.go            # 输入验证
```

### 5. 关键接口定义

#### 5.1 SwapService 接口
```go
type ISwap interface {
    // GetSwapQuote 获取兑换报价
    GetSwapQuote(ctx context.Context, req *SwapQuoteRequest) (*SwapQuote, error)
    
    // CreateSwapOrder 创建兑换订单
    CreateSwapOrder(ctx context.Context, req *SwapOrderRequest) (*SwapOrder, error)
    
    // ExecuteSwapOrder 执行兑换订单
    ExecuteSwapOrder(ctx context.Context, orderID string) (*SwapResult, error)
    
    // GetSwapHistory 获取用户兑换历史
    GetSwapHistory(ctx context.Context, userID uint64, page, limit int) ([]*SwapOrder, error)
    
    // GetSupportedPairs 获取支持的交易对
    GetSupportedPairs(ctx context.Context) ([]*SwapPair, error)
}
```

#### 5.2 价格客户端使用示例
```go
// 闪兑服务中集成价格客户端
type SwapService struct {
    priceClient     IPriceClient
    walletService   IWallet
    configService   ISwapConfig
    maxStaleTime    time.Duration // 最大允许的价格数据过期时间
}

// 获取兑换报价时的价格检查
func (s *SwapService) GetSwapQuote(ctx context.Context, req *SwapQuoteRequest) (*SwapQuote, error) {
    symbol := req.FromSymbol + req.ToSymbol
    
    // 1. 检查价格数据新鲜度
    if err := s.priceClient.CheckPriceFreshness(ctx, symbol, s.maxStaleTime); err != nil {
        return nil, &SwapError{
            Type:    ErrorTypeStaleData,
            Code:    "PRICE_DATA_STALE",
            Message: fmt.Sprintf("Price data for %s is too old", symbol),
            Symbol:  symbol,
        }
    }
    
    // 2. 获取实时价格
    priceData, err := s.priceClient.GetRealTimePrice(ctx, symbol)
    if err != nil {
        return nil, fmt.Errorf("failed to get price for %s: %w", symbol, err)
    }
    
    // 3. 计算兑换金额
    exchangeRate := priceData.Price
    toAmount := req.FromAmount.Mul(exchangeRate)
    
    // 4. 计算手续费
    feeAmount := s.calculateFee(req.FromAmount, exchangeRate)
    
    return &SwapQuote{
        QuoteID:      generateQuoteID(),
        FromSymbol:   req.FromSymbol,
        ToSymbol:     req.ToSymbol,
        FromAmount:   req.FromAmount,
        ToAmount:     toAmount.Sub(feeAmount),
        ExchangeRate: exchangeRate,
        FeeAmount:    feeAmount,
        PriceSource:  priceData.Provider,
        ExpiresAt:    time.Now().Add(30 * time.Second),
    }, nil
}

// 执行兑换时的二次价格检查
func (s *SwapService) ExecuteSwapOrder(ctx context.Context, orderID string) (*SwapResult, error) {
    // 获取订单信息
    order, err := s.getSwapOrder(ctx, orderID)
    if err != nil {
        return nil, err
    }
    
    symbol := order.FromSymbol + order.ToSymbol
    
    // 1. 再次检查价格数据新鲜度
    if err := s.priceClient.CheckPriceFreshness(ctx, symbol, s.maxStaleTime); err != nil {
        return nil, &SwapError{
            Type:    ErrorTypeStaleData,
            Code:    "PRICE_DATA_STALE_ON_EXECUTION",
            Message: "Price data became stale during execution",
            Symbol:  symbol,
        }
    }
    
    // 2. 获取最新价格
    currentPriceData, err := s.priceClient.GetRealTimePrice(ctx, symbol)
    if err != nil {
        return nil, fmt.Errorf("failed to get current price: %w", err)
    }
    
    // 3. 滑点检查
    slippageCheck := s.validateSlippage(order.QuotedRate, currentPriceData.Price, order.SlippageTolerance)
    if !slippageCheck.IsValid {
        return &SwapResult{
            Status:       SwapStatusFailed,
            ErrorCode:    "SLIPPAGE_EXCEEDED",
            ErrorMessage: fmt.Sprintf("Price slippage %.2f%% exceeds tolerance %.2f%%", 
                         slippageCheck.ActualSlippage*100, order.SlippageTolerance*100),
        }, nil
    }
    
    // 4. 执行兑换
    return s.executeSwap(ctx, order, currentPriceData.Price)
}

// 错误处理示例
type SwapError struct {
    Type      ErrorType `json:"type"`
    Code      string    `json:"code"`
    Message   string    `json:"message"`
    Symbol    string    `json:"symbol,omitempty"`
    Timestamp time.Time `json:"timestamp"`
}

func (e *SwapError) Error() string {
    return fmt.Sprintf("[%s] %s: %s", e.Code, e.Symbol, e.Message)
}
```

### 6. 核心数据结构

```go
// 兑换报价请求
type SwapQuoteRequest struct {
    UserID      uint64          `json:"user_id"`
    FromSymbol  string          `json:"from_symbol"`
    ToSymbol    string          `json:"to_symbol"`  
    FromAmount  decimal.Decimal `json:"from_amount"`
}

// 兑换报价响应
type SwapQuote struct {
    QuoteID        string          `json:"quote_id"`
    FromSymbol     string          `json:"from_symbol"`
    ToSymbol       string          `json:"to_symbol"`
    FromAmount     decimal.Decimal `json:"from_amount"`
    ToAmount       decimal.Decimal `json:"to_amount"`
    ExchangeRate   decimal.Decimal `json:"exchange_rate"`
    FeeAmount      decimal.Decimal `json:"fee_amount"`
    FeeSymbol      string          `json:"fee_symbol"`
    PriceSource    string          `json:"price_source"`
    ExpiresAt      time.Time       `json:"expires_at"`
}

// 兑换订单
type SwapOrder struct {
    OrderUUID       string          `json:"order_uuid"`
    UserID          uint64          `json:"user_id"`
    FromSymbol      string          `json:"from_symbol"`
    ToSymbol        string          `json:"to_symbol"`
    FromAmount      decimal.Decimal `json:"from_amount"`
    ExpectedToAmount decimal.Decimal `json:"expected_to_amount"`
    ActualToAmount   decimal.Decimal `json:"actual_to_amount"`
    Status          SwapOrderStatus `json:"status"`
    CreatedAt       time.Time       `json:"created_at"`
    ExecutedAt      *time.Time      `json:"executed_at"`
}
```

### 7. Telegram 界面设计

#### 7.1 键盘布局
```go
// 主菜单新增闪兑按钮
var MainMenuKeyboard = tgbotapi.NewReplyKeyboard(
    tgbotapi.NewKeyboardButtonRow(
        tgbotapi.NewKeyboardButton("💰 余额"),
        tgbotapi.NewKeyboardButton("💱 闪兑"), // 新增
    ),
    // ... 其他按钮
)

// 币种选择键盘  
func CreateCurrencySelectionKeyboard(currencies []string) tgbotapi.InlineKeyboardMarkup {
    var rows [][]tgbotapi.InlineKeyboardButton
    for _, currency := range currencies {
        button := tgbotapi.NewInlineKeyboardButtonData(currency, fmt.Sprintf("swap_select_%s", currency))
        rows = append(rows, []tgbotapi.InlineKeyboardButton{button})
    }
    return tgbotapi.NewInlineKeyboardMarkup(rows...)
}
```

#### 7.2 响应消息模板

```go
// 闪兑核心消息模板
const (
    // 主界面消息
    SwapWelcomeMessage = `💱 <b>闪兑中心</b>

🚀 快速兑换您的数字资产！支持实时汇率，安全便捷

<b>📊 实时价格 (USDT本位)</b>
%s

<b>请选择交易对开始兑换：</b>`

    // 实时价格显示模板
    SwapPriceDisplayTemplate = `💎 <b>ETH</b> %s USDT %s
₿ <b>BTC</b> %s USDT %s
🟡 <b>BNB</b> %s USDT %s`

    // 交易对选择消息
    SwapSelectTradingPairMessage = `💱 <b>选择交易对</b>

<b>📊 当前价格：</b>
%s

请选择您要交易的方向：`

    // 金额输入消息
    SwapEnterAmountMessage = `💰 <b>输入兑换金额</b>

<b>交易对：</b>%s
<b>当前价格：</b>1 %s = %s USDT
<b>您的余额：</b>%s %s
<b>兑换范围：</b>%s - %s %s

请输入您要兑换的 <b>%s</b> 数量：

<i>💡 提示：输入数字即可，例如：0.1</i>`

    // 报价预览消息
    SwapQuotePreviewMessage = `📊 <b>兑换预览确认</b>

┌─────────────────────────
│ <b>兑换详情</b>
├─────────────────────────
│ <b>兑出：</b>%s %s
│ <b>兑入：</b>~%s %s
│ <b>汇率：</b>1 %s ≈ %s %s
│ <b>手续费：</b>%s %s (%s%%)
│ <b>实际到账：</b><b>%s %s</b>
└─────────────────────────

<b>💡 重要提示：</b>
⏰ 报价有效期：<code>%d</code> 秒
📈 滑点保护：±%s%%
🔒 资金将被临时锁定直到交易完成

<b>确认执行此次兑换吗？</b>`

    // 支付密码验证消息
    SwapPasswordPromptMessage = `🔐 <b>安全验证</b>

即将执行兑换：
<b>%s %s</b> → <b>%s %s</b>

请输入您的支付密码以继续：

<i>🛡️ 为了您的资金安全，请确保周围环境安全</i>`

    // 处理中消息
    SwapProcessingMessage = `⏳ <b>兑换处理中...</b>

正在为您执行兑换操作，请稍候...

<b>订单详情：</b>
• 订单号：<code>%s</code>
• 兑换：%s %s → %s %s
• 预期汇率：1 %s ≈ %s %s

<i>📡 正在获取最新汇率并执行交易...</i>`

    // 成功消息
    SwapSuccessMessage = `✅ <b>兑换成功！</b>

┌─────────────────────────
│ <b>交易完成</b>
├─────────────────────────
│ <b>订单号：</b><code>%s</code>
│ <b>完成时间：</b>%s
├─────────────────────────
│ <b>兑出：</b>%s %s
│ <b>兑入：</b>%s %s
│ <b>实际汇率：</b>1 %s = %s %s
│ <b>手续费：</b>%s %s
│ <b>滑点：</b>%s%%
└─────────────────────────

<b>💰 更新后余额：</b>
• %s：%s
• %s：%s

感谢您使用闪兑服务！`

    // 失败消息
    SwapFailedMessage = `❌ <b>兑换失败</b>

<b>订单号：</b><code>%s</code>
<b>失败原因：</b>%s
<b>失败时间：</b>%s

<b>您的资金已安全退回，无任何损失。</b>

<b>💡 建议：</b>
• 检查网络连接状态
• 稍后重试或联系客服

您可以重新发起兑换或查看其他功能。`

    // 取消消息
    SwapCancelledMessage = `🚫 <b>兑换已取消</b>

您已取消本次兑换操作。

<b>订单号：</b><code>%s</code>
<b>取消时间：</b>%s

您的资金未受任何影响，可以随时重新发起兑换。`

    // 历史记录消息
    SwapHistoryMessage = `📊 <b>兑换历史</b>

<b>最近 %d 条兑换记录：</b>

%s

<i>📄 第 %d 页 / 共 %d 页</i>`

    SwapHistoryItemTemplate = `┌─────────────────────────
│ <b>%s</b> %s
├─────────────────────────
│ <b>兑换：</b>%s %s → %s %s
│ <b>汇率：</b>1 %s = %s %s
│ <b>状态：</b>%s
│ <b>时间：</b>%s
└─────────────────────────`

    // 错误消息模板
    SwapErrorInsufficientBalance = `⚠️ <b>余额不足</b>

您的 <b>%s</b> 余额不足以完成此次兑换。

<b>需要：</b>%s %s
<b>当前：</b>%s %s
<b>缺少：</b>%s %s

请先充值或减少兑换金额。`

    SwapErrorInvalidAmount = `⚠️ <b>金额无效</b>

输入的金额不符合要求：

<b>您输入：</b>%s
<b>最小金额：</b>%s %s
<b>最大金额：</b>%s %s

请重新输入正确的金额。`

    SwapErrorQuoteExpired = `⏰ <b>报价已过期</b>

当前报价已超过有效期（%d秒）。

由于市场价格实时变动，请重新获取最新报价。`

    SwapErrorSlippageTooHigh = `📈 <b>价格变动过大</b>

执行兑换时发现价格变动超出可接受范围：

<b>报价时汇率：</b>1 %s = %s %s
<b>执行时汇率：</b>1 %s = %s %s
<b>价格变动：</b>%s%%
<b>滑点保护：</b>±%s%%

为保护您的资金，已自动取消此次兑换。`

    SwapErrorServiceUnavailable = `🔧 <b>服务暂时不可用</b>

闪兑服务正在维护或遇到技术问题：

<b>错误代码：</b>%s
<b>预计恢复：</b>%s

请稍后重试或联系客服获取帮助。`

    SwapErrorPasswordIncorrect = `🔐 <b>支付密码错误</b>

您输入的支付密码不正确。

<b>剩余尝试次数：</b>%d
<b>错误次数：</b>%d/5

<i>⚠️ 连续错误5次将锁定账户30分钟</i>`

    SwapErrorNetworkIssue = `🌐 <b>网络连接问题</b>

获取实时汇率时遇到网络问题：

<b>价格源：</b>%s
<b>重试次数：</b>%d/3

正在尝试切换到备用价格源...`

    // 系统状态消息
    SwapStatusMessage = `📊 <b>闪兑服务状态</b>

<b>🔄 服务状态：</b>%s
<b>📈 支持交易对：</b>%d 个
<b>💰 24h交易量：</b>$%s
<b>⚡ 平均处理时间：</b>%s

<b>📊 实时汇率：</b>
%s

<b>🛠️ 系统信息：</b>
• 最后更新：%s
• API延迟：%dms
• 成功率：%s%%`

    // 帮助消息
    SwapHelpMessage = `❓ <b>闪兑帮助</b>

<b>🔍 什么是闪兑？</b>
闪兑是一种快速数字资产兑换服务，让您能够在不同代币间即时转换。

<b>📝 使用步骤：</b>
1️⃣ 选择兑换方向（如 ETH → USDT）
2️⃣ 输入兑换金额
3️⃣ 确认汇率和手续费
4️⃣ 输入支付密码完成兑换

<b>💡 注意事项：</b>
• 报价有效期为30秒
• 设有滑点保护机制
• 支持7×24小时服务
• 所有交易都有完整记录

<b>💰 费用说明：</b>
• 手续费：%s%%
• 最小金额：%s %s
• 最大金额：%s %s

<b>🔐 安全保障：</b>
• 资金实时监控
• 多重签名保护
• 交易全程加密

需要更多帮助？请联系客服。`
)

// 键盘按钮常量
const (
    SwapButtonConfirm       = "✅ 确认兑换"
    SwapButtonCancel        = "❌"
    SwapButtonRetry         = "🔄 重试"
    SwapButtonHistory       = "📊 兑换历史"
    SwapButtonHelp          = "❓ 帮助"
    SwapButtonSettings      = "⚙️ 设置"
    SwapButtonBack          = "◀️ 返回"
    SwapButtonNext          = "▶️ 下一页"
    SwapButtonPrev          = "◀️ 上一页"
    SwapButtonETH           = "💎 ETH"
    SwapButtonUSDT          = "💰 USDT"
    SwapButtonBalance       = "💰 查看余额"
    SwapButtonNewSwap       = "🆕 新兑换"
)

// 状态文本常量
const (
    SwapStatusPending    = "⏳ 待处理"
    SwapStatusProcessing = "⚡ 处理中"
    SwapStatusSuccess    = "✅ 成功"
    SwapStatusFailed     = "❌ 失败"
    SwapStatusCancelled  = "🚫 已取消"
    SwapStatusExpired    = "⏰ 已过期"
)

// 创建内联键盘的辅助函数
func CreateSwapConfirmationKeyboard() tgbotapi.InlineKeyboardMarkup {
    return tgbotapi.NewInlineKeyboardMarkup(
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData(SwapButtonConfirm, "swap_confirm"),
            tgbotapi.NewInlineKeyboardButtonData(SwapButtonCancel, "swap_cancel"),
        ),
    )
}

func CreateSwapTradingPairKeyboard(tradingPairs []TradingPairInfo) tgbotapi.InlineKeyboardMarkup {
    var rows [][]tgbotapi.InlineKeyboardButton
    
    for _, pair := range tradingPairs {
        // 创建两个方向的按钮，例如：ETH→USDT 和 USDT→ETH
        baseEmoji := getTokenEmoji(pair.BaseSymbol)
        quoteEmoji := getTokenEmoji(pair.QuoteSymbol)
        
        // 买入方向按钮 (BASE → QUOTE，如 ETH → USDT)
        buyText := fmt.Sprintf("%s%s → %s%s", baseEmoji, pair.BaseSymbol, quoteEmoji, pair.QuoteSymbol)
        buyCallback := fmt.Sprintf("swap_pair_%s_%s", pair.BaseSymbol, pair.QuoteSymbol)
        
        // 卖出方向按钮 (QUOTE → BASE，如 USDT → ETH)
        sellText := fmt.Sprintf("%s%s → %s%s", quoteEmoji, pair.QuoteSymbol, baseEmoji, pair.BaseSymbol)
        sellCallback := fmt.Sprintf("swap_pair_%s_%s", pair.QuoteSymbol, pair.BaseSymbol)
        
        // 每行放置一对交易方向
        row := []tgbotapi.InlineKeyboardButton{
            tgbotapi.NewInlineKeyboardButtonData(buyText, buyCallback),
            tgbotapi.NewInlineKeyboardButtonData(sellText, sellCallback),
        }
        rows = append(rows, row)
    }
    
    // 添加功能按钮
    rows = append(rows, []tgbotapi.InlineKeyboardButton{
        tgbotapi.NewInlineKeyboardButtonData("📊 兑换历史", "swap_history"),
        tgbotapi.NewInlineKeyboardButtonData("❓ 帮助", "swap_help"),
    })
    
    return tgbotapi.NewInlineKeyboardMarkup(rows...)
}

func CreateSwapHistoryKeyboard(page, totalPages int) tgbotapi.InlineKeyboardMarkup {
    var rows [][]tgbotapi.InlineKeyboardButton
    
    // 分页按钮
    if totalPages > 1 {
        var pageRow []tgbotapi.InlineKeyboardButton
        if page > 1 {
            pageRow = append(pageRow, tgbotapi.NewInlineKeyboardButtonData(SwapButtonPrev, fmt.Sprintf("swap_history_page_%d", page-1)))
        }
        pageRow = append(pageRow, tgbotapi.NewInlineKeyboardButtonData(fmt.Sprintf("%d/%d", page, totalPages), "swap_history_info"))
        if page < totalPages {
            pageRow = append(pageRow, tgbotapi.NewInlineKeyboardButtonData(SwapButtonNext, fmt.Sprintf("swap_history_page_%d", page+1)))
        }
        rows = append(rows, pageRow)
    }
    
    // 功能按钮
    rows = append(rows, []tgbotapi.InlineKeyboardButton{
        tgbotapi.NewInlineKeyboardButtonData(SwapButtonNewSwap, "swap_new"),
        tgbotapi.NewInlineKeyboardButtonData(SwapButtonBack, "swap_back_main"),
    })
    
    return tgbotapi.NewInlineKeyboardMarkup(rows...)
}

// 辅助函数
func getTokenEmoji(symbol string) string {
    emojiMap := map[string]string{
        "ETH":  "💎",
        "USDT": "💰",
        "BTC":  "₿",
        "BNB":  "🟡",
    }
    if emoji, exists := emojiMap[symbol]; exists {
        return emoji
    }
    return "🪙"
}

func formatSwapStatus(status int) string {
    statusMap := map[int]string{
        1: SwapStatusPending,
        2: SwapStatusSuccess,
        3: SwapStatusFailed,
        4: SwapStatusCancelled,
        5: SwapStatusExpired,
    }
    if text, exists := statusMap[status]; exists {
        return text
    }
    return "❓ 未知"
}

// 闪兑配置信息结构
type SwapConfig struct {
    ID                   uint            `json:"id"`
    TradingPair          string          `json:"trading_pair"`
    FromTokenID          uint            `json:"from_token_id"`
    FromSymbol           string          `json:"from_symbol"`
    ToTokenID            uint            `json:"to_token_id"`
    ToSymbol             string          `json:"to_symbol"`
    IsEnabled            bool            `json:"is_enabled"`
    IsDisplayed          bool            `json:"is_displayed"`
    DisplayOrder         int             `json:"display_order"`
    MinAmount            decimal.Decimal `json:"min_amount"`
    MaxAmount            decimal.Decimal `json:"max_amount"`
    DefaultFeeType       int             `json:"default_fee_type"`
    DefaultFeeRate       decimal.Decimal `json:"default_fee_rate"`
    DefaultSlippage      decimal.Decimal `json:"default_slippage"`
    QuoteValiditySeconds int             `json:"quote_validity_seconds"`
    PriceSourcePriority  []string        `json:"price_source_priority"`
    CreatedAt            time.Time       `json:"created_at"`
    UpdatedAt            time.Time       `json:"updated_at"`
}

// 交易对信息结构
type TradingPairInfo struct {
    ConfigID      uint            `json:"config_id"`      // 配置ID
    TradingPair   string          `json:"trading_pair"`   // 交易对，如 ETH_USDT
    BaseSymbol    string          `json:"base_symbol"`    // 基础币种，如 ETH
    QuoteSymbol   string          `json:"quote_symbol"`   // 计价币种，如 USDT
    CurrentPrice  decimal.Decimal `json:"current_price"`  // 当前价格
    Change24h     decimal.Decimal `json:"change_24h"`     // 24小时涨跌幅
    Volume24h     decimal.Decimal `json:"volume_24h"`     // 24小时交易量
    MinAmount     decimal.Decimal `json:"min_amount"`     // 最小兑换金额
    MaxAmount     decimal.Decimal `json:"max_amount"`     // 最大兑换金额
    DisplayOrder  int             `json:"display_order"`  // 显示顺序
    LastUpdated   time.Time       `json:"last_updated"`   // 最后更新时间
}

// 格式化价格显示
func FormatPriceDisplay(pairs []TradingPairInfo) string {
    var priceLines []string
    
    for _, pair := range pairs {
        emoji := getTokenEmoji(pair.BaseSymbol)
        changeEmoji := "📈"
        if pair.Change24h.IsNegative() {
            changeEmoji = "📉"
        }
        
        // 格式化价格，保留适当的小数位
        priceStr := formatPrice(pair.CurrentPrice)
        changeStr := formatChange(pair.Change24h)
        
        line := fmt.Sprintf("%s <b>%s</b> %s USDT %s", 
            emoji, pair.BaseSymbol, priceStr, changeEmoji + changeStr)
        priceLines = append(priceLines, line)
    }
    
    return strings.Join(priceLines, "\n")
}

// 格式化价格，根据价格大小调整小数位
func formatPrice(price decimal.Decimal) string {
    if price.GreaterThan(decimal.NewFromInt(1000)) {
        return price.StringFixed(2) // 大于1000显示2位小数
    } else if price.GreaterThan(decimal.NewFromInt(1)) {
        return price.StringFixed(4) // 1-1000显示4位小数
    } else {
        return price.StringFixed(6) // 小于1显示6位小数
    }
}

// 格式化涨跌幅
func formatChange(change decimal.Decimal) string {
    changePercent := change.Mul(decimal.NewFromInt(100))
    if change.IsNegative() {
        return changePercent.StringFixed(2) + "%"
    } else {
        return "+" + changePercent.StringFixed(2) + "%"
    }
}

// 获取实时价格数据的辅助函数
func GetSwapWelcomeMessageWithPrices(pairs []TradingPairInfo) string {
    priceDisplay := FormatPriceDisplay(pairs)
    return fmt.Sprintf(SwapWelcomeMessage, priceDisplay)
}

// 从数据库获取可显示的交易对配置
func GetDisplayableTradingPairs(ctx context.Context) ([]SwapConfig, error) {
    // 示例查询逻辑
    query := `
        SELECT 
            id, trading_pair, from_token_id, from_symbol, 
            to_token_id, to_symbol, is_enabled, is_displayed,
            display_order, min_amount, max_amount, default_fee_type,
            default_fee_rate, default_slippage, quote_validity_seconds,
            price_source_priority, created_at, updated_at
        FROM swap_configs 
        WHERE is_enabled = TRUE AND is_displayed = TRUE 
        ORDER BY display_order ASC
    `
    // 实际实现将在service层完成
    return nil, nil
}

// 根据交易对符号获取配置
func GetSwapConfigByPair(ctx context.Context, fromSymbol, toSymbol string) (*SwapConfig, error) {
    query := `
        SELECT 
            id, trading_pair, from_token_id, from_symbol,
            to_token_id, to_symbol, is_enabled, is_displayed,
            display_order, min_amount, max_amount, default_fee_type,
            default_fee_rate, default_slippage, quote_validity_seconds,
            price_source_priority, created_at, updated_at
        FROM swap_configs 
        WHERE from_symbol = ? AND to_symbol = ? AND is_enabled = TRUE
    `
    // 实际实现将在service层完成
    return nil, nil
}

// 切换交易对显示状态
func ToggleSwapConfigDisplay(ctx context.Context, configID uint, isDisplayed bool) error {
    query := `
        UPDATE swap_configs 
        SET is_displayed = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
    `
    // 实际实现将在service层完成
    return nil
}

// 更新交易对显示顺序
func UpdateSwapConfigDisplayOrder(ctx context.Context, configID uint, displayOrder int) error {
    query := `
        UPDATE swap_configs 
        SET display_order = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
    `
    // 实际实现将在service层完成
    return nil
}
```

#### 7.3 国际化支持结构

```go
// 国际化消息键常量
const (
    // 核心功能消息键
    I18nSwapWelcome           = "swap.welcome"
    I18nSwapSelectFromToken   = "swap.select_from_token"
    I18nSwapSelectToToken     = "swap.select_to_token"
    I18nSwapEnterAmount       = "swap.enter_amount"
    I18nSwapQuotePreview      = "swap.quote_preview"
    I18nSwapPasswordPrompt    = "swap.password_prompt"
    I18nSwapProcessing        = "swap.processing"
    I18nSwapSuccess           = "swap.success"
    I18nSwapFailed            = "swap.failed"
    I18nSwapCancelled         = "swap.cancelled"
    I18nSwapHistory           = "swap.history"
    I18nSwapHelp              = "swap.help"
    I18nSwapStatus            = "swap.status"
    
    // 错误消息键
    I18nSwapErrorInsufficientBalance = "swap.error.insufficient_balance"
    I18nSwapErrorInvalidAmount       = "swap.error.invalid_amount"
    I18nSwapErrorQuoteExpired        = "swap.error.quote_expired"
    I18nSwapErrorSlippageTooHigh     = "swap.error.slippage_too_high"
    I18nSwapErrorServiceUnavailable  = "swap.error.service_unavailable"
    I18nSwapErrorPasswordIncorrect   = "swap.error.password_incorrect"
    I18nSwapErrorNetworkIssue        = "swap.error.network_issue"
    
    // 按钮文本键
    I18nSwapButtonConfirm     = "swap.button.confirm"
    I18nSwapButtonCancel      = "swap.button.cancel"
    I18nSwapButtonRetry       = "swap.button.retry"
    I18nSwapButtonHistory     = "swap.button.history"
    I18nSwapButtonHelp        = "swap.button.help"
    I18nSwapButtonBack        = "swap.button.back"
    I18nSwapButtonNext        = "swap.button.next"
    I18nSwapButtonPrev        = "swap.button.prev"
)

// 消息格式化辅助函数
func FormatSwapMessage(i18nService II18n, key string, lang string, args ...interface{}) string {
    template := i18nService.Get(lang, key)
    if len(args) > 0 {
        return fmt.Sprintf(template, args...)
    }
    return template
}

// 获取本地化的状态文本
func GetLocalizedSwapStatus(i18nService II18n, lang string, status int) string {
    statusKeys := map[int]string{
        1: "swap.status.pending",
        2: "swap.status.success", 
        3: "swap.status.failed",
        4: "swap.status.cancelled",
        5: "swap.status.expired",
    }
    
    if key, exists := statusKeys[status]; exists {
        return i18nService.Get(lang, key)
    }
    return i18nService.Get(lang, "swap.status.unknown")
}

// 创建本地化的键盘
func CreateLocalizedSwapKeyboard(i18nService II18n, lang string, keyboardType string) tgbotapi.InlineKeyboardMarkup {
    switch keyboardType {
    case "confirmation":
        confirmText := i18nService.Get(lang, I18nSwapButtonConfirm)
        cancelText := i18nService.Get(lang, I18nSwapButtonCancel)
        return tgbotapi.NewInlineKeyboardMarkup(
            tgbotapi.NewInlineKeyboardRow(
                tgbotapi.NewInlineKeyboardButtonData(confirmText, "swap_confirm"),
                tgbotapi.NewInlineKeyboardButtonData(cancelText, "swap_cancel"),
            ),
        )
    case "retry":
        retryText := i18nService.Get(lang, I18nSwapButtonRetry)
        backText := i18nService.Get(lang, I18nSwapButtonBack)
        return tgbotapi.NewInlineKeyboardMarkup(
            tgbotapi.NewInlineKeyboardRow(
                tgbotapi.NewInlineKeyboardButtonData(retryText, "swap_retry"),
                tgbotapi.NewInlineKeyboardButtonData(backText, "swap_back"),
            ),
        )
    default:
        return tgbotapi.NewInlineKeyboardMarkup()
    }
}
```

### 8. 配置管理

#### 8.0 价格客户端配置
```yaml
# 闪兑服务中的价格客户端配置
price_client:
  redis:
    addresses: ["localhost:6379"]
    password: ""
    db: 0
    pool_size: 10
    dial_timeout: "5s"
    read_timeout: "3s"
    write_timeout: "3s"
  
  staleness:
    max_allowed_stale: "45s"      # 最大允许的价格数据过期时间
    warning_threshold: "30s"      # 价格数据预警阈值
    check_interval: "5s"          # 新鲜度检查间隔
  
  retry:
    max_attempts: 3               # 最大重试次数
    retry_delay: "1s"             # 重试延迟
    backoff_multiplier: 2.0       # 退避乘数
  
  monitoring:
    enable_metrics: true          # 启用指标收集
    log_slow_queries: true        # 记录慢查询
    slow_query_threshold: "100ms" # 慢查询阈值
```

### 8. 配置管理

#### 8.1 默认配置
```sql
-- 插入默认配置
INSERT INTO `swap_configs` (
    `trading_pair`, 
    `from_token_id`, 
    `from_symbol`, 
    `to_token_id`, 
    `to_symbol`, 
    `is_enabled`, 
    `is_displayed`, 
    `display_order`, 
    `min_amount`, 
    `max_amount`, 
    `default_fee_rate`, 
    `price_source_priority`
) VALUES
-- ETH ↔ USDT 交易对
('ETH_USDT', 2, 'ETH', 1, 'USDT', TRUE, TRUE, 1, '0.001', '100', '0.001', '["binance", "kucoin", "okx"]'),
('USDT_ETH', 1, 'USDT', 2, 'ETH', TRUE, TRUE, 2, '10', '100000', '0.001', '["binance", "kucoin", "okx"]'),

-- BTC ↔ USDT 交易对
('BTC_USDT', 3, 'BTC', 1, 'USDT', TRUE, TRUE, 3, '0.0001', '10', '0.001', '["binance", "kucoin", "okx"]'),
('USDT_BTC', 1, 'USDT', 3, 'BTC', TRUE, TRUE, 4, '100', '500000', '0.001', '["binance", "kucoin", "okx"]'),

-- BNB ↔ USDT 交易对 (暂时不显示)
('BNB_USDT', 4, 'BNB', 1, 'USDT', TRUE, FALSE, 5, '0.01', '1000', '0.001', '["binance", "kucoin", "okx"]'),
('USDT_BNB', 1, 'USDT', 4, 'BNB', TRUE, FALSE, 6, '10', '100000', '0.001', '["binance", "kucoin", "okx"]');

-- 查询显示在界面的交易对
SELECT 
    trading_pair,
    from_symbol,
    to_symbol,
    min_amount,
    max_amount,
    default_fee_rate,
    display_order
FROM swap_configs 
WHERE is_enabled = TRUE AND is_displayed = TRUE 
ORDER BY display_order ASC;
```

### 9. 部署和监控

#### 9.1 价格服务监控

**注意：价格监控由独立的价格监控服务负责，详见 [PRICE_MONITOR_SERVICE_DESIGN.md](./PRICE_MONITOR_SERVICE_DESIGN.md)**

闪兑服务端监控重点：
- **价格数据新鲜度监控**：监控价格数据的时效性，超过阈值时告警
- **价格服务可用性**：监控Redis连接状态和价格客户端健康状态
- **兑换成功率监控**：按价格数据新鲜度分组统计成功率
- **滑点分布统计**：分析实际滑点分布，优化滑点策略

```go
// 闪兑服务监控指标
type SwapMetrics struct {
    // 价格相关指标
    priceDataStaleCount    prometheus.GaugeVec   // 价格数据过期次数
    priceClientErrors      prometheus.CounterVec // 价格客户端错误次数
    priceDataLatency       prometheus.HistogramVec // 价格数据获取延迟
    
    // 兑换业务指标
    swapSuccessRate        prometheus.GaugeVec   // 兑换成功率
    slippageDistribution   prometheus.HistogramVec // 滑点分布
    swapVolumeTotal        prometheus.CounterVec // 兑换总量
    
    // 错误监控
    staleDataRejects       prometheus.CounterVec // 因数据过期拒绝的兑换数
    slippageExceededCount  prometheus.CounterVec // 滑点超限次数
}

// 价格新鲜度告警配置
const (
    PriceStaleWarningThreshold  = 30 * time.Second // 价格数据预警阈值
    PriceStaleErrorThreshold    = 60 * time.Second // 价格数据错误阈值
    MaxAllowedStaleTime         = 45 * time.Second // 最大允许的过期时间
)
```

#### 9.2 业务指标监控
- 每日兑换量统计
- 手续费收入统计  
- 用户活跃度分析
- 错误率监控

### 10. 安全考虑

#### 10.1 风控措施
- 单用户每日兑换限额
- 大额兑换人工审核
- 异常交易模式检测
- 价格操纵检测

