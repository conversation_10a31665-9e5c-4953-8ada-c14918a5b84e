# 闪兑手续费策略部署指南

## 概述

本文档提供了闪兑手续费策略重构的完整部署指南，包括数据库迁移、配置更新、部署步骤和回滚方案。

## 部署前准备

### 1. 环境要求
- Go 1.24.3+
- MySQL 8.0+
- Redis 6.0+
- 足够的数据库备份空间

### 2. 依赖检查
```bash
# 检查Go版本
go version

# 检查数据库连接
mysql -h <host> -u <user> -p -e "SELECT VERSION();"

# 检查Redis连接
redis-cli ping
```

### 3. 代码审查清单
- [ ] 所有单元测试通过
- [ ] 集成测试通过
- [ ] 代码审查完成
- [ ] 性能测试通过
- [ ] 安全审查完成

## 数据库迁移

### 1. 备份现有数据
```bash
# 备份exchange_products表
mysqldump -h <host> -u <user> -p <database> exchange_products > exchange_products_backup.sql

# 备份exchange_orders表
mysqldump -h <host> -u <user> -p <database> exchange_orders > exchange_orders_backup.sql

# 备份exchange_quotes表
mysqldump -h <host> -u <user> -p <database> exchange_quotes > exchange_quotes_backup.sql
```

### 2. 执行数据库迁移
```sql
-- 1. 为exchange_products表添加新字段
ALTER TABLE exchange_products 
ADD COLUMN output_fee_rate DECIMAL(10,8) DEFAULT 0.002 COMMENT '输出手续费率',
ADD COLUMN min_output_fee_amount DECIMAL(20,8) DEFAULT 0 COMMENT '最小输出手续费金额';

-- 2. 为exchange_orders表添加新字段
ALTER TABLE exchange_orders 
ADD COLUMN output_amount_before_fee DECIMAL(20,8) DEFAULT 0 COMMENT '扣费前输出金额',
ADD COLUMN output_amount_after_fee DECIMAL(20,8) DEFAULT 0 COMMENT '扣费后输出金额',
ADD COLUMN fee_calculation_method VARCHAR(50) DEFAULT 'legacy' COMMENT '手续费计算方法';

-- 3. 为exchange_quotes表添加新字段
ALTER TABLE exchange_quotes 
ADD COLUMN output_amount_before_fee DECIMAL(20,8) DEFAULT 0 COMMENT '扣费前输出金额',
ADD COLUMN output_amount_after_fee DECIMAL(20,8) DEFAULT 0 COMMENT '扣费后输出金额',
ADD COLUMN fee_calculation_method VARCHAR(50) DEFAULT 'output_percentage' COMMENT '手续费计算方法';

-- 4. 创建索引
CREATE INDEX idx_exchange_orders_fee_method ON exchange_orders(fee_calculation_method);
CREATE INDEX idx_exchange_quotes_fee_method ON exchange_quotes(fee_calculation_method);

-- 5. 更新现有产品配置（示例）
UPDATE exchange_products SET 
  output_fee_rate = 0.002,
  min_output_fee_amount = CASE 
    WHEN base_token = 'BTC' THEN 0.0001
    WHEN base_token = 'ETH' THEN 0.001
    WHEN quote_token = 'USDT' THEN 1.0
    ELSE 0
  END
WHERE product_id IN (1, 2, 3); -- 根据实际产品ID调整
```

### 3. 验证迁移结果
```sql
-- 检查新字段是否添加成功
DESCRIBE exchange_products;
DESCRIBE exchange_orders;
DESCRIBE exchange_quotes;

-- 检查数据完整性
SELECT COUNT(*) FROM exchange_products WHERE output_fee_rate IS NULL;
SELECT COUNT(*) FROM exchange_orders WHERE fee_calculation_method IS NULL;
```

## 配置更新

### 1. 应用配置
```yaml
# config/config.yaml
swap:
  fee_strategy: "output_percentage"  # 新策略
  enable_legacy_support: true       # 启用向后兼容
  max_slippage: 0.01                # 最大滑点 1%
  
  # 新增：手续费配置
  fee:
    default_rate: 0.002              # 默认手续费率 0.2%
```

### 2. 环境变量
```bash
# 新增环境变量
export SWAP_FEE_STRATEGY=output_percentage
export SWAP_ENABLE_LEGACY_SUPPORT=true
export SWAP_MAX_SLIPPAGE=0.01
```

### 3. 产品配置示例
```json
{
  "products": [
    {
      "product_id": 1,
      "symbol": "BTC/USDT",
      "base_token": "BTC",
      "quote_token": "USDT",
      "output_fee_rate": "0.002",
      "min_output_fee_amount": "0.0001",
      "is_active": 1,
      "allow_buy": 1,
      "allow_sell": 1
    },
    {
      "product_id": 2,
      "symbol": "ETH/USDT",
      "base_token": "ETH",
      "quote_token": "USDT",
      "output_fee_rate": "0.002",
      "min_output_fee_amount": "0.001",
      "is_active": 1,
      "allow_buy": 1,
      "allow_sell": 1
    }
  ]
}
```

## 部署步骤

### 阶段1：准备部署
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 构建应用
go mod tidy
go build -o bot-api ./cmd/main.go

# 3. 运行测试
go test ./internal/service/v2/impl -v

# 4. 检查配置文件
./bot-api --check-config
```

### 阶段2：数据库迁移
```bash
# 1. 停止应用（如果需要）
systemctl stop bot-api

# 2. 执行数据库迁移
mysql -h <host> -u <user> -p <database> < migration.sql

# 3. 验证迁移
mysql -h <host> -u <user> -p <database> < verify_migration.sql
```

### 阶段3：应用部署
```bash
# 1. 备份当前版本
cp /opt/bot-api/bot-api /opt/bot-api/bot-api.backup

# 2. 部署新版本
cp bot-api /opt/bot-api/
chmod +x /opt/bot-api/bot-api

# 3. 更新配置
cp config/config.yaml /opt/bot-api/config/

# 4. 启动应用
systemctl start bot-api

# 5. 检查服务状态
systemctl status bot-api
```

### 阶段4：验证部署
```bash
# 1. 健康检查
curl http://localhost:8080/health

# 2. API测试
curl -X POST http://localhost:8080/api/v2/swap/quote \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1001,
    "from_token_id": 2,
    "to_token_id": 1,
    "amount": "100",
    "amount_type": "from"
  }'

# 3. 检查日志
tail -f /var/log/bot-api/app.log
```

## 监控和告警

### 1. 关键指标监控
```yaml
# prometheus配置示例
- name: swap_fee_calculation_errors
  query: rate(swap_fee_calculation_errors_total[5m])
  threshold: 0.01
  
- name: swap_order_success_rate
  query: rate(swap_orders_completed_total[5m]) / rate(swap_orders_created_total[5m])
  threshold: 0.95
  
- name: swap_balance_validation_failures
  query: rate(swap_balance_validation_failures_total[5m])
  threshold: 0.05
```

### 2. 日志监控
```bash
# 关键日志模式
grep "SWAP.*ERROR" /var/log/bot-api/app.log
grep "fee_calculation_failed" /var/log/bot-api/app.log
grep "balance_insufficient" /var/log/bot-api/app.log
```

### 3. 业务指标监控
- 手续费计算准确性
- 订单执行成功率
- 用户余额验证失败率
- API响应时间

## 回滚方案

### 紧急回滚步骤
```bash
# 1. 停止当前服务
systemctl stop bot-api

# 2. 恢复应用程序
cp /opt/bot-api/bot-api.backup /opt/bot-api/bot-api

# 3. 恢复配置文件
cp /opt/bot-api/config/config.yaml.backup /opt/bot-api/config/config.yaml

# 4. 恢复数据库（如果需要）
mysql -h <host> -u <user> -p <database> < exchange_products_backup.sql
mysql -h <host> -u <user> -p <database> < exchange_orders_backup.sql

# 5. 启动服务
systemctl start bot-api

# 6. 验证回滚
curl http://localhost:8080/health
```

### 数据库回滚脚本
```sql
-- 回滚数据库更改
ALTER TABLE exchange_products 
DROP COLUMN output_fee_rate,
DROP COLUMN min_output_fee_amount;

ALTER TABLE exchange_orders 
DROP COLUMN output_amount_before_fee,
DROP COLUMN output_amount_after_fee,
DROP COLUMN fee_calculation_method;

ALTER TABLE exchange_quotes 
DROP COLUMN output_amount_before_fee,
DROP COLUMN output_amount_after_fee,
DROP COLUMN fee_calculation_method;

-- 删除索引
DROP INDEX idx_exchange_orders_fee_method ON exchange_orders;
DROP INDEX idx_exchange_quotes_fee_method ON exchange_quotes;
```

## 故障排除

### 常见问题

#### 1. 手续费计算错误
```bash
# 检查产品配置
SELECT * FROM exchange_products WHERE output_fee_rate IS NULL OR output_fee_rate < 0;

# 检查日志
grep "fee_calculation" /var/log/bot-api/app.log
```

#### 2. 数据库连接问题
```bash
# 检查数据库连接
mysql -h <host> -u <user> -p -e "SELECT 1;"

# 检查连接池
grep "database.*connection" /var/log/bot-api/app.log
```

#### 3. API响应异常
```bash
# 检查API日志
grep "swap.*quote" /var/log/bot-api/app.log
grep "HTTP.*500" /var/log/bot-api/app.log

# 检查系统资源
top
df -h
```

### 调试命令
```bash
# 启用调试模式
export LOG_LEVEL=debug
systemctl restart bot-api

# 查看详细日志
tail -f /var/log/bot-api/app.log | grep -E "(DEBUG|ERROR)"

# 检查配置
./bot-api --dump-config
```

## 性能优化

### 1. 数据库优化
```sql
-- 添加必要的索引
CREATE INDEX idx_exchange_orders_user_status ON exchange_orders(user_id, status);
CREATE INDEX idx_exchange_quotes_user_expires ON exchange_quotes(user_id, expires_at);

-- 分析表性能
ANALYZE TABLE exchange_products;
ANALYZE TABLE exchange_orders;
ANALYZE TABLE exchange_quotes;
```

### 2. 应用优化
```yaml
# 连接池配置
database:
  max_open_conns: 100
  max_idle_conns: 10
  conn_max_lifetime: 3600

# 缓存配置
redis:
  pool_size: 100
  min_idle_conns: 10
```

## 总结

本部署指南提供了完整的部署流程，包括数据库迁移、配置更新、部署验证和回滚方案。请严格按照步骤执行，并在生产环境部署前在测试环境充分验证。

### 部署检查清单
- [ ] 数据库备份完成
- [ ] 迁移脚本验证通过
- [ ] 配置文件更新
- [ ] 应用程序构建成功
- [ ] 测试用例全部通过
- [ ] 监控告警配置完成
- [ ] 回滚方案准备就绪
- [ ] 团队通知和文档更新
