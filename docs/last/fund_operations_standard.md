# 资金操作标准化方案

## 概述

本文档定义了 XPay Bot API 项目中所有资金操作的 BusinessID 和 Description 的统一标准。

## BusinessID 标准

### 格式规范

`{operation_prefix}_{unique_identifier}_{timestamp_or_id}`

### 操作前缀常量

所有前缀定义在 `internal/constants/fund_operations.go` 中：

| 操作类型 | 前缀常量 | 值 | 说明 |
|---------|---------|-----|------|
| 红包创建 | BizPrefixRedPacketCreate | rp_create | 创建红包时扣款 |
| 红包领取 | BizPrefixRedPacketClaim | rp_claim | 用户领取红包 |
| 红包取消 | BizPrefixRedPacketCancel | rp_cancel | 手动取消红包退款 |
| 红包过期 | BizPrefixRedPacketExpire | rp_expire | 红包过期自动退款 |
| 转账发出 | BizPrefixTransferOut | transfer_out | 转账扣款 |
| 转账收入 | BizPrefixTransferIn | transfer_in | 转账入账 |
| 转账过期 | BizPrefixTransferExpire | transfer_expire | 转账过期退款 |
| 提现申请 | BizPrefixWithdrawReq | withdraw_req | 提现申请扣款 |
| 提现退款 | BizPrefixWithdrawRefund | withdraw_refund | 提现失败退款 |
| 兑换扣除 | BizPrefixSwapOut | swap_out | 兑换时扣除代币 |
| 兑换入账 | BizPrefixSwapIn | swap_in | 兑换时增加代币 |
| 支付扣款 | BizPrefixPaymentOut | payment_out | 支付请求扣款 |
| 支付收款 | BizPrefixPaymentIn | payment_in | 支付请求收款 |

### 生成函数

使用 `utils.GenerateBusinessID()` 函数生成标准化的 BusinessID：

```go
// 示例：红包取消
BusinessID: utils.GenerateBusinessID(constants.BizPrefixRedPacketCancel, packet.Uuid, redPacketID)
// 结果：rp_cancel_a1b2c3d4e5f6g7h8_12345

// 示例：转账
BusinessID: utils.GenerateBusinessID(constants.BizPrefixTransferOut, senderUserId, gtime.Now().Unix())
// 结果：transfer_out_1001_1704290400
```

## Description 标准

### 中文模板

所有描述模板定义在 `internal/constants/fund_operations.go` 中：

| 操作类型 | 模板常量 | 模板内容 |
|---------|---------|---------|
| 转账发出 | DescTplTransferOut | 转账给 %s: %s %s |
| 转账收入 | DescTplTransferIn | 收到转账: %s %s |
| 红包创建 | DescTplRedPacketCreate | 创建红包 %s |
| 红包领取 | DescTplRedPacketClaim | 领取红包: %s %s |
| 红包取消 | DescTplRedPacketCancel | 取消红包退款 %s |
| 红包过期 | DescTplRedPacketExpire | 红包过期退款 %s |
| 提现 | DescTplWithdraw | 提现到 %s: %s %s |
| 提现退款 | DescTplWithdrawRefund | 提现失败退款: %s %s |
| 兑换扣除 | DescTplSwapOut | 兑换扣除: %s %s |
| 兑换获得 | DescTplSwapIn | 兑换获得: %s %s |

### 英文模板

支持国际化，所有英文模板以 `EN` 后缀命名：

| 操作类型 | 模板常量 | 模板内容 |
|---------|---------|---------|
| 转账发出 | DescTplTransferOutEN | Transfer to %s: %s %s |
| 转账收入 | DescTplTransferInEN | Received transfer: %s %s |
| 红包创建 | DescTplRedPacketCreateEN | Create red packet %s |
| 红包领取 | DescTplRedPacketClaimEN | Claim red packet: %s %s |

### 格式化函数

使用对应的格式化函数生成标准化的描述：

```go
// 转账描述
utils.FormatTransferDescription("out", recipientUsername, amount, symbol)
// 结果：转账给 张三: 100.00 USDT

// 红包描述
utils.FormatRedPacketDescription("claim", uuid, amount, symbol)
// 结果：领取红包: 10.00 USDT

// 支持多语言
utils.FormatTransferDescriptionEN("in", "", amount, symbol)
// 结果：Received transfer: 100.00 USDT
```

## 使用示例

### 红包操作

```go
// 创建红包
walletReq := &walletConstants.FundOperationRequest{
    UserID:      uint64(user.Id),
    TokenSymbol: input.TokenSymbol,
    Amount:      input.TotalAmount,
    BusinessID:  utils.GenerateBusinessID(constants.BizPrefixRedPacketCreate, redPacketUUID, gtime.Now().Unix()),
    FundType:    walletConstants.FundTypeRedPacketCreate,
    Description: utils.FormatRedPacketDescription("create", redPacketUUID, "", ""),
    // ...
}

// 领取红包
walletReq := &walletConstants.FundOperationRequest{
    UserID:      uint64(claimerUser.Id),
    TokenSymbol: packet.Symbol,
    Amount:      claimedAmount,
    BusinessID:  utils.GenerateBusinessID(constants.BizPrefixRedPacketClaim, redPacketUUID, claimId),
    FundType:    walletConstants.FundTypeRedPacketClaim,
    Description: utils.FormatRedPacketDescription("claim", redPacketUUID, claimedAmount.String(), packet.Symbol),
    // ...
}
```

### 转账操作

```go
// 转账扣款
senderReq := &walletConstants.FundOperationRequest{
    UserID:      senderUserId,
    TokenSymbol: symbol,
    Amount:      amountDecimal,
    BusinessID:  utils.GenerateBusinessID(constants.BizPrefixTransferOut, senderUserId, gtime.Now().Unix()),
    FundType:    walletConstants.FundTypeTransferOut,
    Description: utils.FormatTransferDescription("out", recipientUsername, amountDecimal.String(), symbol),
    // ...
}

// 转账入账
recipientReq := &walletConstants.FundOperationRequest{
    UserID:      recipientUserId,
    TokenSymbol: symbol,
    Amount:      amountDecimal,
    BusinessID:  utils.GenerateBusinessID(constants.BizPrefixTransferIn, recipientUserId, gtime.Now().Unix()),
    FundType:    walletConstants.FundTypeTransferIn,
    Description: utils.FormatTransferDescription("in", "", amountDecimal.String(), symbol),
    // ...
}
```

## 优势

1. **唯一性保证**：通过组合操作类型、唯一标识符和时间戳/ID确保全局唯一
2. **易于识别**：前缀清晰标识操作类型，便于日志追踪和问题排查
3. **统一规范**：所有操作遵循相同的命名模式，减少认知负担
4. **可扩展性**：新增操作类型只需添加常量和模板，无需修改核心逻辑
5. **国际化支持**：Description 模板支持多语言，便于全球化部署
6. **类型安全**：使用常量避免硬编码字符串，减少拼写错误

## 维护指南

### 添加新的操作类型

1. 在 `internal/constants/fund_operations.go` 中添加新的前缀常量
2. 添加对应的中英文描述模板
3. 在 `internal/utils/fund_operations.go` 中添加格式化函数（如需要）
4. 更新本文档

### 最佳实践

1. 始终使用 `utils.GenerateBusinessID()` 生成 BusinessID
2. 始终使用格式化函数生成 Description
3. 不要硬编码任何 BusinessID 前缀或 Description 模板
4. 保持 BusinessID 的可读性和可追踪性
5. 确保 Description 准确描述操作内容和金额

## 已实施文件

以下文件已经更新为使用新的标准：

### 第一批更新（基础实施）
- [x] `/internal/logic/red_packet/cancel_red_packet.go` - 红包取消
- [x] `/internal/logic/red_packet/claim_red_packet.go` - 红包领取
- [x] `/internal/logic/red_packet/create_red_packet_v2.go` - 红包创建
- [x] `/internal/logic/transfer/execute_transfer.go` - 转账执行
- [x] `/internal/logic/withdraw/create_withdraw_request.go` - 提现申请
- [x] `/internal/task/red_packet_expiry.go` - 红包过期处理

### 第二批更新（Description 统一）
- [x] `/internal/logic/payment_request/execute_payment.go` - 支付请求
- [x] `/internal/logic/transfer/create_and_execute_direct_transfer.go` - 直接转账
- [x] `/internal/logic/transfer/collect_transfer_v2.go` - 收集转账
- [x] `/internal/logic/withdraw/process_withdraw_request.go` - 处理提现
- [x] `/internal/service/v2/impl/swap_execute_order.go` - 兑换订单执行
- [x] `/internal/logic/user/create_user_wallet_v2.go` - 创建用户钱包

## 新增的描述模板

### 中文模板
- `DescTplTransferOutWithMemo` - 带备注的转账发出
- `DescTplTransferInWithMemo` - 带备注的转账收入
- `DescTplDemoFunds` - 演示资金
- `DescTplWithdrawRefundDetail` - 详细的提现退款
- `DescTplPaymentRequestPay` - 支付请求付款
- `DescTplPaymentRequestReceive` - 支付请求收款
- `DescTplTransferCollectOut` - 转账收集扣款
- `DescTplTransferCollectIn` - 转账收集入账

### 英文模板
对应的英文版本均以 `EN` 后缀命名

## 新增的格式化函数

- `FormatTransferWithMemoDescription()` - 格式化带备注的转账描述
- `FormatDemoFundsDescription()` - 格式化演示资金描述
- `FormatWithdrawRefundDetailDescription()` - 格式化详细的提现退款描述
- `FormatPaymentRequestDescription()` - 格式化支付请求描述
- `FormatTransferCollectDescription()` - 格式化转账收集描述