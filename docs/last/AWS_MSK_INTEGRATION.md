# AWS MSK (Managed Streaming for Apache Kafka) Integration Guide

本文档详细说明如何将现有 Telegram Bot API 项目集成到 AWS MSK 服务中。

## 概述

AWS MSK 是 AWS 提供的完全托管 Apache Kafka 服务，支持多种认证机制和安全配置。本项目已更新支持：

- **SASL/SCRAM 认证** (SHA-256, SHA-512)
- **TLS 加密连接**
- **自动连接测试和验证**
- **配置模板和示例**

## 前置条件

1. **AWS MSK 集群已创建并运行**
2. **SCRAM 用户凭证已配置** (如果使用 SCRAM 认证)
3. **安全组规则允许访问 Kafka 端口**
   - SASL/SCRAM: 端口 9096
   - TLS: 端口 9094
4. **网络连通性确认** (VPC 配置或 VPN/Direct Connect)

## 快速开始

### 1. 复制环境配置模板

```bash
cp .env.aws-msk.example .env
```

### 2. 更新配置文件

编辑 `.env` 文件，替换以下关键配置：

```bash
# 替换为你的 AWS MSK broker 端点
BOT_API_KAFKA_BROKERS_0=your-msk-cluster-broker-1.c3.kafka.region.amazonaws.com:9096

# 替换为你的 SCRAM 凭证
BOT_API_KAFKA_PRODUCERCONFIG_SASLUSER=your-msk-username
BOT_API_KAFKA_PRODUCERCONFIG_SASLPASSWORD=your-msk-password
```

### 3. 测试连接

```bash
# 编译测试工具
go build -o kafka-test cmd/kafka-test/main.go

# 运行完整连接测试
./kafka-test -test=all -verbose

# 或者运行特定测试
./kafka-test -test=config    # 配置验证
./kafka-test -test=network   # 网络连通性
./kafka-test -test=connection # Kafka 协议连接
```

## 配置详解

### Broker 配置

```yaml
kafka:
  brokers:
    - "your-msk-broker-1.c3.kafka.region.amazonaws.com:9096"
    - "your-msk-broker-2.c3.kafka.region.amazonaws.com:9096"  # 可选：多个 broker
```

**端口说明：**
- `9096`: SASL/SCRAM 认证端口
- `9094`: TLS 认证端口
- `9092`: 明文连接端口 (不推荐生产环境使用)

### SASL/SCRAM 认证配置

```yaml
producerConfig:
  saslEnable: true
  saslMechanism: "SCRAM-SHA-512"  # 或 "SCRAM-SHA-256", "PLAIN"
  saslUser: "your-msk-username"
  saslPassword: "your-msk-password"
```

**支持的认证机制：**
- `SCRAM-SHA-512`: AWS MSK 推荐，最高安全性
- `SCRAM-SHA-256`: 备选 SCRAM 机制
- `PLAIN`: 基础认证机制

### TLS 配置

```yaml
producerConfig:
  tlsEnable: true
  tlsInsecureSkipVerify: false  # 生产环境应设为 false
```

**TLS 说明：**
- `tlsEnable: true`: 启用 TLS 加密传输
- `tlsInsecureSkipVerify: false`: 验证服务器证书 (推荐)
- `tlsInsecureSkipVerify: true`: 跳过证书验证 (仅测试环境)

## AWS MSK 集群配置

### 1. 创建 MSK 集群

```bash
# 使用 AWS CLI 创建集群 (示例)
aws kafka create-cluster \
    --cluster-name "telegram-bot-kafka" \
    --broker-node-group-info "instanceType=kafka.t3.small,clientSubnets=subnet-xxx,securityGroups=sg-xxx" \
    --kafka-version "2.8.1" \
    --number-of-broker-nodes 3
```

### 2. 配置 SCRAM 认证

```bash
# 启用 SCRAM 认证
aws kafka put-cluster-policy \
    --cluster-arn "your-cluster-arn" \
    --policy '{
        "Version": "2012-10-17",
        "Statement": [{
            "Effect": "Allow",
            "Principal": {"AWS": "your-account-id"},
            "Action": "kafka-cluster:*",
            "Resource": "*"
        }]
    }'

# 创建 SCRAM 用户
aws kafka-scram create-scram-secret \
    --cluster-arn "your-cluster-arn" \
    --secret-arn "your-secret-manager-arn"
```

### 3. 安全组配置

确保安全组允许以下端口：

```bash
# 从应用服务器到 MSK 集群
Type: Custom TCP
Port: 9096          # SASL/SCRAM
Source: your-app-security-group

Type: Custom TCP  
Port: 9094          # TLS
Source: your-app-security-group
```

## 常见问题排查

### 1. 连接超时

**可能原因：**
- 网络连通性问题
- 安全组规则不正确
- VPC 配置问题

**解决方法：**
```bash
# 测试网络连通性
telnet your-msk-broker.amazonaws.com 9096

# 检查 DNS 解析
nslookup your-msk-broker.amazonaws.com
```

### 2. 认证失败

**可能原因：**
- SCRAM 凭证错误
- 认证机制配置不匹配

**解决方法：**
```bash
# 验证 SCRAM 凭证
aws secretsmanager get-secret-value --secret-id your-secret-id

# 测试配置
./kafka-test -test=config -verbose
```

### 3. TLS 证书错误

**可能原因：**
- 证书验证失败
- 时间同步问题

**解决方法：**
```yaml
# 临时跳过证书验证 (仅测试)
tlsInsecureSkipVerify: true

# 或同步系统时间
sudo ntpdate -s time.nist.gov
```

## 性能优化建议

### 1. 生产者配置

```yaml
producerConfig:
  flushMessages: 100        # 批量大小
  flushFrequency: "10ms"    # 批量超时
  retryMax: 3               # 重试次数
```

### 2. 消费者配置

```yaml
consumerConfig:
  fetchMin: 10000          # 最小拉取字节
  fetchDefault: 10000000   # 默认拉取字节  
  sessionTimeout: "20s"    # 会话超时
  heartbeatInterval: "3s"  # 心跳间隔
```

### 3. MSK 集群配置

- **实例类型**: 根据吞吐量需求选择 (kafka.m5.large, kafka.m5.xlarge 等)
- **存储**: 使用 gp3 SSD 获得更好的 IOPS 性能
- **分区数**: 根据并发消费者数量合理设置

## 监控和告警

### 1. AWS CloudWatch 指标

关键指标监控：
- `CPUUtilization`: CPU 使用率
- `NetworkRxBytes/NetworkTxBytes`: 网络吞吐量  
- `KafkaDataLogsDiskUsed`: 磁盘使用率
- `OffsetLag`: 消费延迟

### 2. 应用层监控

```go
// 在应用中添加指标收集
tester := service.NewKafkaConnectionTester(ctx)
if err := tester.TestKafkaConnection(); err != nil {
    // 记录连接失败指标
    metrics.KafkaConnectionFailures.Inc()
}
```

## 安全最佳实践

1. **使用 SCRAM-SHA-512 认证机制**
2. **启用 TLS 加密传输**
3. **定期轮换 SCRAM 凭证**
4. **使用 AWS Secrets Manager 存储敏感信息**
5. **配置最小权限安全组规则**
6. **启用 MSK 访问日志记录**

## 成本优化

1. **按需调整实例类型和数量**
2. **配置合适的存储保留策略**
3. **使用 Reserved Instances 降低成本**
4. **监控数据传输成本**

## 故障转移和高可用

1. **多 AZ 部署 MSK 集群**
2. **配置多个 broker 端点**
3. **实现应用层重试机制**
4. **定期备份重要 topic 数据**

## 迁移检查清单

- [ ] AWS MSK 集群已创建并运行正常
- [ ] SCRAM 用户凭证已配置
- [ ] 安全组规则已正确配置
- [ ] 网络连通性已验证
- [ ] 配置文件已更新
- [ ] 连接测试已通过
- [ ] 应用程序已重启
- [ ] 监控和告警已配置
- [ ] 备份策略已制定

## 联系支持

如遇到问题，请联系：
- AWS 技术支持
- 项目维护团队
- [AWS MSK 用户指南](https://docs.aws.amazon.com/msk/)