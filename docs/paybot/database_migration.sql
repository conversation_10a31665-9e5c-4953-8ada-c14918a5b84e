-- PayBot数据库集成迁移脚本
-- 版本: v1.0.0
-- 创建时间: 2025-01-10
-- 说明: 为PayBot内部服务集成创建和修改相关数据库表

-- =====================================================
-- 1. 创建PayBot授权支付订单表
-- =====================================================

CREATE TABLE `paybot_auth_orders` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `paybot_order_no` VARCHAR(64) NOT NULL COMMENT 'PayBot系统订单号',
    `merchant_order_no` VARCHAR(64) NOT NULL COMMENT '商户订单号',
    `user_account` VARCHAR(255) NOT NULL COMMENT '用户账户标识符（关联users.account）',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID（关联users.id）',
    `order_type` ENUM('add', 'deduct') NOT NULL COMMENT '订单类型：add-加款，deduct-扣款',
    `token_symbol` VARCHAR(20) NOT NULL COMMENT '代币符号（如USDT）',
    `amount` DECIMAL(36,18) NOT NULL COMMENT '交易金额',
    `auth_reason` TEXT NOT NULL COMMENT '授权原因',
    `status` ENUM('pending', 'completed', 'expired', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
    `callback_url` VARCHAR(500) NULL COMMENT '回调URL',
    `callback_status` ENUM('pending', 'success', 'failed') NOT NULL DEFAULT 'pending' COMMENT '回调状态',
    `expire_at` TIMESTAMP NULL COMMENT '过期时间',
    `completed_at` TIMESTAMP NULL COMMENT '完成时间',
    `error_message` TEXT NULL COMMENT '错误信息',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_paybot_order_no` (`paybot_order_no`),
    KEY `idx_user_account` (`user_account`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_merchant_order_no` (`merchant_order_no`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_status_created` (`status`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PayBot授权支付订单表';

-- =====================================================
-- 2. 创建PayBot回调记录表
-- =====================================================

CREATE TABLE `paybot_callbacks` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `event_type` VARCHAR(50) NOT NULL COMMENT '事件类型（deposit_confirmed/withdraw_completed等）',
    `order_no` VARCHAR(64) NOT NULL COMMENT '关联的订单号',
    `related_table` VARCHAR(50) NULL COMMENT '关联的表名',
    `related_id` BIGINT UNSIGNED NULL COMMENT '关联表的记录ID',
    `merchant_id` BIGINT UNSIGNED NULL COMMENT '商户ID',
    `payload` JSON NOT NULL COMMENT '回调数据（JSON格式）',
    `signature` VARCHAR(255) NULL COMMENT '回调签名',
    `callback_time` TIMESTAMP NOT NULL COMMENT '回调接收时间',
    `processed_status` ENUM('pending', 'success', 'failed') NOT NULL DEFAULT 'pending' COMMENT '处理状态',
    `process_attempts` INT NOT NULL DEFAULT 0 COMMENT '处理尝试次数',
    `error_message` TEXT NULL COMMENT '处理错误信息',
    `next_retry_at` TIMESTAMP NULL COMMENT '下次重试时间',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    
    PRIMARY KEY (`id`),
    KEY `idx_event_type` (`event_type`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_processed_status` (`processed_status`),
    KEY `idx_next_retry_at` (`next_retry_at`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_status_retry` (`processed_status`, `next_retry_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PayBot回调记录表';

-- =====================================================
-- 3. 修改user_address表（充值地址表）
-- =====================================================

-- 3.1 添加PayBot相关字段
ALTER TABLE `user_address` 
ADD COLUMN `paybot_address_id` VARCHAR(64) NULL COMMENT 'PayBot系统中的地址ID' AFTER `type`,
ADD COLUMN `is_reused` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否复用地址：0-新生成，1-复用' AFTER `paybot_address_id`,
ADD COLUMN `qr_code_data` TEXT NULL COMMENT '二维码数据（Base64格式）' AFTER `is_reused`,
ADD COLUMN `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '地址状态' AFTER `qr_code_data`,
ADD COLUMN `deposit_count` INT NOT NULL DEFAULT 0 COMMENT '充值次数统计' AFTER `status`,
ADD COLUMN `last_deposit_at` TIMESTAMP NULL COMMENT '最后一次充值时间' AFTER `deposit_count`;

-- 3.2 添加索引
ALTER TABLE `user_address`
ADD KEY `idx_paybot_address_id` (`paybot_address_id`),
ADD KEY `idx_user_chain_token` (`user_id`, `chan`, `name`),
ADD KEY `idx_status` (`status`);

-- 3.3 修正字段名的拼写错误（如果需要的话，可选执行）
-- 注意：这个操作会影响现有代码，需要谨慎执行
-- ALTER TABLE `user_address` CHANGE `lable` `label` VARCHAR(255) COMMENT '备注';
-- ALTER TABLE `user_address` CHANGE `chan` `chain` VARCHAR(255) COMMENT '链';

-- =====================================================
-- 4. 修改user_withdraws表（提现表）
-- =====================================================

-- 4.1 添加PayBot同步相关字段
ALTER TABLE `user_withdraws`
ADD COLUMN `paybot_order_no` VARCHAR(64) NULL COMMENT 'PayBot系统订单号' AFTER `order_no`,
ADD COLUMN `paybot_sync_status` ENUM('pending', 'synced', 'failed') NOT NULL DEFAULT 'pending' COMMENT 'PayBot同步状态' AFTER `paybot_order_no`,
ADD COLUMN `paybot_sync_at` TIMESTAMP NULL COMMENT 'PayBot同步时间' AFTER `paybot_sync_status`,
ADD COLUMN `paybot_error_message` TEXT NULL COMMENT 'PayBot同步错误信息' AFTER `paybot_sync_at`;

-- 4.2 添加索引
ALTER TABLE `user_withdraws`
ADD KEY `idx_paybot_order_no` (`paybot_order_no`),
ADD KEY `idx_paybot_sync_status` (`paybot_sync_status`);

-- =====================================================
-- 5. 创建PayBot配置表（可选，用于存储动态配置）
-- =====================================================

CREATE TABLE `paybot_config` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` TEXT NOT NULL COMMENT '配置值',
    `config_type` ENUM('string', 'number', 'boolean', 'json') NOT NULL DEFAULT 'string' COMMENT '配置类型',
    `description` VARCHAR(500) NULL COMMENT '配置说明',
    `is_enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`),
    KEY `idx_is_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PayBot配置表';

-- 插入默认配置
INSERT INTO `paybot_config` (`config_key`, `config_value`, `config_type`, `description`) VALUES
('paybot.enabled', 'true', 'boolean', 'PayBot功能是否启用'),
('paybot.api.base_url', 'http://**************:9000', 'string', 'PayBot API基础URL'),
('paybot.api.timeout', '30', 'number', 'API请求超时时间（秒）'),
('paybot.sync.enabled', 'true', 'boolean', '是否启用数据同步'),
('paybot.sync.batch_size', '100', 'number', '数据同步批次大小'),
('paybot.retry.max_attempts', '3', 'number', '最大重试次数'),
('paybot.retry.backoff_seconds', '60', 'number', '重试间隔时间（秒）');

-- =====================================================
-- 6. 创建用于统计和监控的视图（可选）
-- =====================================================

-- 6.1 PayBot授权订单统计视图
CREATE VIEW `v_paybot_auth_orders_stats` AS
SELECT 
    `user_account`,
    `order_type`,
    `token_symbol`,
    `status`,
    COUNT(*) as `order_count`,
    SUM(`amount`) as `total_amount`,
    AVG(`amount`) as `avg_amount`,
    MIN(`created_at`) as `first_order_at`,
    MAX(`created_at`) as `last_order_at`
FROM `paybot_auth_orders`
GROUP BY `user_account`, `order_type`, `token_symbol`, `status`;

-- 6.2 PayBot回调处理统计视图
CREATE VIEW `v_paybot_callbacks_stats` AS
SELECT 
    `event_type`,
    `processed_status`,
    COUNT(*) as `callback_count`,
    AVG(`process_attempts`) as `avg_attempts`,
    MIN(`created_at`) as `first_callback_at`,
    MAX(`created_at`) as `last_callback_at`
FROM `paybot_callbacks`
GROUP BY `event_type`, `processed_status`;

-- =====================================================
-- 7. 数据完整性检查和清理（可选执行）
-- =====================================================

-- 检查是否有孤立的记录
-- SELECT COUNT(*) FROM paybot_auth_orders pao 
-- LEFT JOIN users u ON pao.user_account = u.account 
-- WHERE u.id IS NULL;

-- 清理测试数据（生产环境慎用）
-- DELETE FROM paybot_auth_orders WHERE paybot_order_no LIKE 'TEST_%';
-- DELETE FROM paybot_callbacks WHERE order_no LIKE 'TEST_%';

-- =====================================================
-- 8. 权限设置（根据实际需要调整）
-- =====================================================

-- 为应用用户授权访问新表
-- GRANT SELECT, INSERT, UPDATE, DELETE ON paybot_auth_orders TO 'app_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON paybot_callbacks TO 'app_user'@'%';
-- GRANT SELECT, INSERT, UPDATE ON paybot_config TO 'app_user'@'%';

-- =====================================================
-- 迁移脚本执行说明
-- =====================================================

/*
执行顺序：
1. 备份现有数据库
2. 按顺序执行上述SQL语句
3. 重新生成GoFrame DAO文件：gf gen dao
4. 更新相关业务代码
5. 执行测试验证

注意事项：
1. 字段名修正（lable->label, chan->chain）是可选的，需要评估对现有代码的影响
2. 生产环境执行前请充分测试
3. 建议在维护窗口期间执行
4. 执行后需要更新应用程序的数据库连接配置
5. 监控执行过程中的锁等待和性能影响

回滚策略：
1. 新建的表可以直接删除：
   DROP TABLE paybot_auth_orders;
   DROP TABLE paybot_callbacks;
   DROP TABLE paybot_config;
   
2. 新增的字段可以删除：
   ALTER TABLE user_address DROP COLUMN paybot_address_id, DROP COLUMN is_reused, ...;
   ALTER TABLE user_withdraws DROP COLUMN paybot_order_no, DROP COLUMN paybot_sync_status, ...;
   
3. 删除视图：
   DROP VIEW v_paybot_auth_orders_stats;
   DROP VIEW v_paybot_callbacks_stats;
*/