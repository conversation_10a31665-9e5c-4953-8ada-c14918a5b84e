# PayBot 数据库集成设计文档

## 概述

本文档描述了PayBot内部服务与现有数据库结构的集成方案，包括表结构设计、DAO层集成、数据映射关系等。

## 现有表结构分析

### 1. user_address 表（充值地址）

**现有结构**：
```go
type UserAddress struct {
    UserAddressId uint        `json:"userAddressId" orm:"user_address_id"`
    TokenId       uint        `json:"tokenId"       orm:"token_id"`
    UserId        int64       `json:"userId"        orm:"user_id"`
    Lable         string      `json:"lable"         orm:"lable"`          // 拼写错误
    Name          string      `json:"name"          orm:"name"`
    Chan          string      `json:"chan"          orm:"chan"`           // 应为chain
    Address       string      `json:"address"       orm:"address"`
    Image         string      `json:"image"         orm:"image"`
    CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"`
    UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"`
    Type          string      `json:"type"          orm:"type"`
}
```

**PayBot集成需求**：
- 支持多链地址生成（TRX、TRC20、ETH、ERC20）
- 地址复用逻辑
- 二维码数据存储
- 充值状态跟踪

### 2. user_withdraws 表（提现）

**现有结构**：
```go
type UserWithdraws struct {
    UserWithdrawsId    uint            `json:"userWithdrawsId"`
    UserId             uint64          `json:"userId"`
    TokenId            uint            `json:"tokenId"`
    OrderNo            string          `json:"orderNo"`
    Address            string          `json:"address"`
    Amount             decimal.Decimal `json:"amount"`
    HandlingFee        decimal.Decimal `json:"handlingFee"`
    ActualAmount       decimal.Decimal `json:"actualAmount"`
    State              uint            `json:"state"`
    TxHash             string          `json:"txHash"`
    // ... 其他字段
}
```

**PayBot集成需求**：
- 与PayBot提现订单关联
- 同步状态管理
- 错误信息记录

### 3. users 表（用户）

**现有结构**：
```go
type Users struct {
    Id      uint64 `json:"id"      orm:"id"`
    Account string `json:"account" orm:"account"` // PayBot使用此字段作为user_label
    // ... 其他字段
}
```

**PayBot集成**：
- `Account` 字段作为PayBot的 `user_label`
- 无需修改现有结构

## 新增表设计

### 1. paybot_auth_orders 表（授权支付订单）

```sql
CREATE TABLE `paybot_auth_orders` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `paybot_order_no` VARCHAR(64) NOT NULL COMMENT 'PayBot系统订单号',
    `merchant_order_no` VARCHAR(64) NOT NULL COMMENT '商户订单号',
    `user_account` VARCHAR(255) NOT NULL COMMENT '用户账户（关联users.account）',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID（关联users.id）',
    `order_type` ENUM('add', 'deduct') NOT NULL COMMENT '订单类型',
    `token_symbol` VARCHAR(20) NOT NULL COMMENT '代币符号',
    `amount` DECIMAL(36,18) NOT NULL COMMENT '交易金额',
    `auth_reason` TEXT NOT NULL COMMENT '授权原因',
    `status` ENUM('pending', 'completed', 'expired', 'cancelled') NOT NULL DEFAULT 'pending',
    `callback_url` VARCHAR(500) NULL COMMENT '回调URL',
    `callback_status` ENUM('pending', 'success', 'failed') NOT NULL DEFAULT 'pending',
    `expire_at` TIMESTAMP NULL COMMENT '过期时间',
    `completed_at` TIMESTAMP NULL COMMENT '完成时间',
    `error_message` TEXT NULL COMMENT '错误信息',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_paybot_order_no` (`paybot_order_no`),
    KEY `idx_user_account` (`user_account`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_merchant_order_no` (`merchant_order_no`),
    KEY `idx_status_created` (`status`, `created_at`)
);
```

**对应Entity**：
```go
type PaybotAuthOrders struct {
    Id               uint64          `json:"id"               orm:"id"`
    PaybotOrderNo    string          `json:"paybotOrderNo"    orm:"paybot_order_no"`
    MerchantOrderNo  string          `json:"merchantOrderNo"  orm:"merchant_order_no"`
    UserAccount      string          `json:"userAccount"      orm:"user_account"`
    UserId           uint64          `json:"userId"           orm:"user_id"`
    OrderType        string          `json:"orderType"        orm:"order_type"`
    TokenSymbol      string          `json:"tokenSymbol"      orm:"token_symbol"`
    Amount           decimal.Decimal `json:"amount"           orm:"amount"`
    AuthReason       string          `json:"authReason"       orm:"auth_reason"`
    Status           string          `json:"status"           orm:"status"`
    CallbackUrl      string          `json:"callbackUrl"      orm:"callback_url"`
    CallbackStatus   string          `json:"callbackStatus"   orm:"callback_status"`
    ExpireAt         *gtime.Time     `json:"expireAt"         orm:"expire_at"`
    CompletedAt      *gtime.Time     `json:"completedAt"      orm:"completed_at"`
    ErrorMessage     string          `json:"errorMessage"     orm:"error_message"`
    CreatedAt        *gtime.Time     `json:"createdAt"        orm:"created_at"`
    UpdatedAt        *gtime.Time     `json:"updatedAt"        orm:"updated_at"`
}
```

### 2. paybot_callbacks 表（回调记录）

```sql
CREATE TABLE `paybot_callbacks` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `event_type` VARCHAR(50) NOT NULL COMMENT '事件类型',
    `order_no` VARCHAR(64) NOT NULL COMMENT '关联的订单号',
    `related_table` VARCHAR(50) NULL COMMENT '关联的表名',
    `related_id` BIGINT UNSIGNED NULL COMMENT '关联表的记录ID',
    `merchant_id` BIGINT UNSIGNED NULL COMMENT '商户ID',
    `payload` JSON NOT NULL COMMENT '回调数据',
    `signature` VARCHAR(255) NULL COMMENT '回调签名',
    `callback_time` TIMESTAMP NOT NULL COMMENT '回调接收时间',
    `processed_status` ENUM('pending', 'success', 'failed') NOT NULL DEFAULT 'pending',
    `process_attempts` INT NOT NULL DEFAULT 0 COMMENT '处理尝试次数',
    `error_message` TEXT NULL COMMENT '处理错误信息',
    `next_retry_at` TIMESTAMP NULL COMMENT '下次重试时间',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (`id`),
    KEY `idx_event_type` (`event_type`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_status_retry` (`processed_status`, `next_retry_at`)
);
```

**对应Entity**：
```go
type PaybotCallbacks struct {
    Id              uint64      `json:"id"              orm:"id"`
    EventType       string      `json:"eventType"       orm:"event_type"`
    OrderNo         string      `json:"orderNo"         orm:"order_no"`
    RelatedTable    string      `json:"relatedTable"    orm:"related_table"`
    RelatedId       uint64      `json:"relatedId"       orm:"related_id"`
    MerchantId      uint64      `json:"merchantId"      orm:"merchant_id"`
    Payload         *gjson.Json `json:"payload"         orm:"payload"`
    Signature       string      `json:"signature"       orm:"signature"`
    CallbackTime    *gtime.Time `json:"callbackTime"    orm:"callback_time"`
    ProcessedStatus string      `json:"processedStatus" orm:"processed_status"`
    ProcessAttempts int         `json:"processAttempts" orm:"process_attempts"`
    ErrorMessage    string      `json:"errorMessage"    orm:"error_message"`
    NextRetryAt     *gtime.Time `json:"nextRetryAt"     orm:"next_retry_at"`
    CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"`
    UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"`
}
```

## 现有表修改

### 1. user_address 表扩展

**新增字段**：
```sql
ALTER TABLE `user_address` 
ADD COLUMN `paybot_address_id` VARCHAR(64) NULL COMMENT 'PayBot系统中的地址ID',
ADD COLUMN `is_reused` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否复用地址',
ADD COLUMN `qr_code_data` TEXT NULL COMMENT '二维码数据（Base64格式）',
ADD COLUMN `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '地址状态',
ADD COLUMN `deposit_count` INT NOT NULL DEFAULT 0 COMMENT '充值次数统计',
ADD COLUMN `last_deposit_at` TIMESTAMP NULL COMMENT '最后一次充值时间';
```

**更新后的Entity**（新增字段）：
```go
type UserAddress struct {
    // ... 现有字段
    PaybotAddressId string      `json:"paybotAddressId" orm:"paybot_address_id"`
    IsReused        int         `json:"isReused"        orm:"is_reused"`
    QrCodeData      string      `json:"qrCodeData"      orm:"qr_code_data"`
    Status          string      `json:"status"          orm:"status"`
    DepositCount    int         `json:"depositCount"    orm:"deposit_count"`
    LastDepositAt   *gtime.Time `json:"lastDepositAt"   orm:"last_deposit_at"`
}
```

### 2. user_withdraws 表扩展

**新增字段**：
```sql
ALTER TABLE `user_withdraws`
ADD COLUMN `paybot_order_no` VARCHAR(64) NULL COMMENT 'PayBot系统订单号',
ADD COLUMN `paybot_sync_status` ENUM('pending', 'synced', 'failed') NOT NULL DEFAULT 'pending',
ADD COLUMN `paybot_sync_at` TIMESTAMP NULL COMMENT 'PayBot同步时间',
ADD COLUMN `paybot_error_message` TEXT NULL COMMENT 'PayBot同步错误信息';
```

**更新后的Entity**（新增字段）：
```go
type UserWithdraws struct {
    // ... 现有字段
    PaybotOrderNo      string      `json:"paybotOrderNo"      orm:"paybot_order_no"`
    PaybotSyncStatus   string      `json:"paybotSyncStatus"   orm:"paybot_sync_status"`
    PaybotSyncAt       *gtime.Time `json:"paybotSyncAt"       orm:"paybot_sync_at"`
    PaybotErrorMessage string      `json:"paybotErrorMessage" orm:"paybot_error_message"`
}
```

## DAO层集成设计

### 1. 新增DAO文件

#### paybot_auth_orders.go
```go
package dao

import (
    "context"
    "telegram-bot-api/internal/dao/internal"
    "telegram-bot-api/internal/model/entity"
    "github.com/shopspring/decimal"
)

type paybotAuthOrdersDao struct {
    *internal.PaybotAuthOrdersDao
}

var (
    PaybotAuthOrders = paybotAuthOrdersDao{internal.NewPaybotAuthOrdersDao()}
)

// CreateAuthOrder 创建授权支付订单
func (d *paybotAuthOrdersDao) CreateAuthOrder(ctx context.Context, order *entity.PaybotAuthOrders) error {
    result, err := d.Ctx(ctx).Data(order).Insert()
    if err != nil {
        return err
    }
    
    lastId, err := result.LastInsertId()
    if err == nil {
        order.Id = uint64(lastId)
    }
    
    return nil
}

// GetByPayBotOrderNo 根据PayBot订单号查询
func (d *paybotAuthOrdersDao) GetByPayBotOrderNo(ctx context.Context, orderNo string) (*entity.PaybotAuthOrders, error) {
    var order entity.PaybotAuthOrders
    err := d.Ctx(ctx).Where("paybot_order_no", orderNo).Scan(&order)
    if err != nil {
        return nil, err
    }
    return &order, nil
}

// GetByMerchantOrderNo 根据商户订单号查询
func (d *paybotAuthOrdersDao) GetByMerchantOrderNo(ctx context.Context, merchantOrderNo string) (*entity.PaybotAuthOrders, error) {
    var order entity.PaybotAuthOrders
    err := d.Ctx(ctx).Where("merchant_order_no", merchantOrderNo).Scan(&order)
    if err != nil {
        return nil, err
    }
    return &order, nil
}

// ListByUserAccount 根据用户账户查询订单列表
func (d *paybotAuthOrdersDao) ListByUserAccount(ctx context.Context, userAccount string, page, pageSize int) ([]*entity.PaybotAuthOrders, int, error) {
    var orders []*entity.PaybotAuthOrders
    var total int
    
    query := d.Ctx(ctx).Where("user_account", userAccount)
    
    // 获取总数
    totalCount, err := query.Count()
    if err != nil {
        return nil, 0, err
    }
    total = totalCount
    
    // 分页查询
    err = query.Page(page, pageSize).OrderDesc("created_at").Scan(&orders)
    if err != nil {
        return nil, 0, err
    }
    
    return orders, total, nil
}

// UpdateStatus 更新订单状态
func (d *paybotAuthOrdersDao) UpdateStatus(ctx context.Context, id uint64, status string, completedAt *gtime.Time) error {
    data := g.Map{"status": status}
    if completedAt != nil {
        data["completed_at"] = completedAt
    }
    
    _, err := d.Ctx(ctx).Where("id", id).Data(data).Update()
    return err
}
```

#### paybot_callbacks.go
```go
package dao

import (
    "context"
    "telegram-bot-api/internal/dao/internal"
    "telegram-bot-api/internal/model/entity"
)

type paybotCallbacksDao struct {
    *internal.PaybotCallbacksDao
}

var (
    PaybotCallbacks = paybotCallbacksDao{internal.NewPaybotCallbacksDao()}
)

// CreateCallback 创建回调记录
func (d *paybotCallbacksDao) CreateCallback(ctx context.Context, callback *entity.PaybotCallbacks) error {
    result, err := d.Ctx(ctx).Data(callback).Insert()
    if err != nil {
        return err
    }
    
    lastId, err := result.LastInsertId()
    if err == nil {
        callback.Id = uint64(lastId)
    }
    
    return nil
}

// GetByOrderNo 根据订单号查询回调记录
func (d *paybotCallbacksDao) GetByOrderNo(ctx context.Context, orderNo string) ([]*entity.PaybotCallbacks, error) {
    var callbacks []*entity.PaybotCallbacks
    err := d.Ctx(ctx).Where("order_no", orderNo).OrderDesc("created_at").Scan(&callbacks)
    return callbacks, err
}

// ListPendingCallbacks 查询待处理的回调
func (d *paybotCallbacksDao) ListPendingCallbacks(ctx context.Context, limit int) ([]*entity.PaybotCallbacks, error) {
    var callbacks []*entity.PaybotCallbacks
    err := d.Ctx(ctx).
        Where("processed_status", "pending").
        WhereOrLt("next_retry_at", gtime.Now()).
        Limit(limit).
        OrderAsc("created_at").
        Scan(&callbacks)
    return callbacks, err
}

// UpdateProcessStatus 更新处理状态
func (d *paybotCallbacksDao) UpdateProcessStatus(ctx context.Context, id uint64, status string, attempts int, errorMsg string, nextRetryAt *gtime.Time) error {
    data := g.Map{
        "processed_status": status,
        "process_attempts": attempts,
    }
    
    if errorMsg != "" {
        data["error_message"] = errorMsg
    }
    if nextRetryAt != nil {
        data["next_retry_at"] = nextRetryAt
    }
    
    _, err := d.Ctx(ctx).Where("id", id).Data(data).Update()
    return err
}
```

### 2. 扩展现有DAO

#### user_address.go 扩展
```go
// 在现有文件中添加方法

// GetByUserAndChain 根据用户和链获取地址
func (d *userAddressDao) GetByUserAndChain(ctx context.Context, userAccount string, chain, token string) (*entity.UserAddress, error) {
    var address entity.UserAddress
    
    // 先通过account获取user_id
    var user entity.Users
    err := dao.Users.Ctx(ctx).Where("account", userAccount).Scan(&user)
    if err != nil {
        return nil, err
    }
    
    // 查询地址（注意字段名可能是chan而不是chain）
    err = d.Ctx(ctx).
        Where("user_id", user.Id).
        Where("chan", chain).  // 或者chain，取决于是否修改了字段名
        Where("name", token).
        Where("status", "active").
        Scan(&address)
    
    if err != nil {
        return nil, err
    }
    
    return &address, nil
}

// CreateOrUpdateAddress 创建或更新地址（支持复用逻辑）
func (d *userAddressDao) CreateOrUpdateAddress(ctx context.Context, userAccount, chain, token, address, qrCode string, isReused bool) (*entity.UserAddress, error) {
    // 先查找是否已存在
    existing, err := d.GetByUserAndChain(ctx, userAccount, chain, token)
    if err == nil && existing != nil {
        // 更新现有记录
        _, updateErr := d.Ctx(ctx).
            Where("user_address_id", existing.UserAddressId).
            Data(g.Map{
                "address":         address,
                "qr_code_data":    qrCode,
                "is_reused":       isReused,
                "updated_at":      gtime.Now(),
            }).Update()
        
        if updateErr != nil {
            return nil, updateErr
        }
        
        return existing, nil
    }
    
    // 创建新记录
    // 获取用户信息
    var user entity.Users
    err = dao.Users.Ctx(ctx).Where("account", userAccount).Scan(&user)
    if err != nil {
        return nil, err
    }
    
    newAddress := &entity.UserAddress{
        UserId:       user.Id,
        Name:         token,
        Chan:         chain,
        Address:      address,
        QrCodeData:   qrCode,
        IsReused:     gconv.Int(isReused),
        Status:       "active",
        DepositCount: 0,
        CreatedAt:    gtime.Now(),
        UpdatedAt:    gtime.Now(),
    }
    
    err = d.CreateUserAddress(ctx, newAddress)
    if err != nil {
        return nil, err
    }
    
    return newAddress, nil
}
```

#### user_withdraws.go 扩展
```go
// 在现有文件中添加方法

// UpdatePayBotSync 更新PayBot同步状态
func (d *userWithdrawsDao) UpdatePayBotSync(ctx context.Context, withdrawId uint, paybotOrderNo, syncStatus string, errorMessage string) error {
    data := g.Map{
        "paybot_order_no":      paybotOrderNo,
        "paybot_sync_status":   syncStatus,
        "paybot_sync_at":       gtime.Now(),
    }
    
    if errorMessage != "" {
        data["paybot_error_message"] = errorMessage
    }
    
    _, err := d.Ctx(ctx).Where("user_withdraws_id", withdrawId).Data(data).Update()
    return err
}

// GetByPayBotOrderNo 根据PayBot订单号查询
func (d *userWithdrawsDao) GetByPayBotOrderNo(ctx context.Context, paybotOrderNo string) (*entity.UserWithdraws, error) {
    var withdraw entity.UserWithdraws
    err := d.Ctx(ctx).Where("paybot_order_no", paybotOrderNo).Scan(&withdraw)
    if err != nil {
        return nil, err
    }
    return &withdraw, nil
}
```

#### users.go 扩展
```go
// 在现有文件中添加方法

// GetByAccount 根据账户获取用户信息
func (d *usersDao) GetByAccount(ctx context.Context, account string) (*entity.Users, error) {
    var user entity.Users
    err := d.Ctx(ctx).Where("account", account).Scan(&user)
    if err != nil {
        return nil, err
    }
    return &user, nil
}

// GetUserIdByAccount 根据账户获取用户ID
func (d *usersDao) GetUserIdByAccount(ctx context.Context, account string) (uint64, error) {
    var userId uint64
    err := d.Ctx(ctx).Fields("id").Where("account", account).Scan(&userId)
    return userId, err
}
```

## 数据映射关系

### PayBot API 与数据库字段映射

| PayBot字段 | 数据库表.字段 | 说明 |
|-----------|--------------|------|
| user_label | users.account | 用户标识符 |
| order_no | paybot_auth_orders.paybot_order_no | PayBot订单号 |
| merchant_order_no | paybot_auth_orders.merchant_order_no | 商户订单号 |
| amount | paybot_auth_orders.amount | 金额（DECIMAL精确计算） |
| order_type | paybot_auth_orders.order_type | 订单类型（add/deduct） |
| status | paybot_auth_orders.status | 订单状态 |
| chain | user_address.chan | 区块链网络 |
| token | user_address.name | 代币类型 |
| address | user_address.address | 充值地址 |
| qr_code | user_address.qr_code_data | 二维码数据 |
| is_reused | user_address.is_reused | 地址是否复用 |

### 状态映射

#### 授权支付订单状态
| PayBot状态 | 数据库状态 | 说明 |
|-----------|-----------|------|
| pending | pending | 待处理 |
| completed | completed | 已完成 |
| expired | expired | 已过期 |
| cancelled | cancelled | 已取消 |

#### 回调处理状态
| PayBot回调 | 数据库状态 | 说明 |
|-----------|-----------|------|
| deposit_confirmed | success | 充值确认成功 |
| withdraw_completed | success | 提现完成成功 |
| - | pending | 待处理 |
| - | failed | 处理失败 |

## 数据同步策略

### 1. 授权支付同步
```go
// 创建授权支付时的数据流
1. PayBot API调用 -> 获取PayBot订单号
2. 创建 paybot_auth_orders 记录
3. 如果是add类型，同时更新用户余额
4. 记录操作日志
```

### 2. 充值地址同步
```go
// 获取充值地址时的数据流
1. 检查本地是否已有地址（user_address表）
2. 如果没有，调用PayBot API生成
3. 保存到 user_address 表
4. 返回地址信息给用户
```

### 3. 回调数据同步
```go
// 接收回调时的数据流
1. 接收PayBot回调 -> 验证签名
2. 创建 paybot_callbacks 记录
3. 根据event_type更新相关表状态
4. 更新回调处理状态
```

## 事务处理策略

### 1. 授权支付事务
```go
func ProcessAuthPayment(ctx context.Context, req *CreateAuthPaymentRequest) error {
    return dao.DB().Transaction(ctx, func(ctx context.Context, tx *gdb.TX) error {
        // 1. 创建授权订单记录
        order := &entity.PaybotAuthOrders{...}
        err := dao.PaybotAuthOrders.CreateAuthOrder(ctx, order)
        if err != nil {
            return err
        }
        
        // 2. 如果是加款，更新用户余额
        if req.OrderType == "add" {
            err = dao.Wallets.UpdateBalance(ctx, req.UserAccount, req.Amount)
            if err != nil {
                return err
            }
        }
        
        // 3. 记录交易日志
        err = dao.Transactions.CreateTransaction(ctx, &entity.Transaction{...})
        return err
    })
}
```

### 2. 回调处理事务
```go
func ProcessCallback(ctx context.Context, callback *PaybotCallbackRequest) error {
    return dao.DB().Transaction(ctx, func(ctx context.Context, tx *gdb.TX) error {
        // 1. 创建回调记录
        callbackRecord := &entity.PaybotCallbacks{...}
        err := dao.PaybotCallbacks.CreateCallback(ctx, callbackRecord)
        if err != nil {
            return err
        }
        
        // 2. 根据事件类型更新相关状态
        switch callback.EventType {
        case "deposit_confirmed":
            // 更新充值状态和用户余额
            err = processDepositConfirmed(ctx, callback)
        case "withdraw_completed":
            // 更新提现状态
            err = processWithdrawCompleted(ctx, callback)
        }
        
        return err
    })
}
```

## 性能优化建议

### 1. 索引优化
- 为常用查询字段创建复合索引
- 定期分析慢查询并优化
- 考虑分区表策略（按时间分区）

### 2. 缓存策略
- 用户地址信息缓存（Redis）
- 配置信息缓存
- 查询结果缓存

### 3. 数据归档
- 定期归档历史回调记录
- 老旧授权订单数据归档
- 保持热数据表的合理大小

## 数据库迁移步骤

### 1. 预备工作
```bash
# 1. 备份现有数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 在测试环境验证迁移脚本
mysql -u username -p test_database < database_migration.sql
```

### 2. 执行迁移
```bash
# 在生产环境执行迁移（建议在维护窗口）
mysql -u username -p production_database < database_migration.sql
```

### 3. 重新生成DAO
```bash
# 使用GoFrame CLI重新生成DAO和Entity
gf gen dao
```

### 4. 验证迁移
```go
// 验证新表和字段是否正确创建
func TestMigration(t *testing.T) {
    // 测试新表查询
    count, err := dao.PaybotAuthOrders.Ctx(ctx).Count()
    assert.NoError(t, err)
    
    // 测试新字段
    address := &entity.UserAddress{}
    err = dao.UserAddress.Ctx(ctx).Limit(1).Scan(address)
    assert.NoError(t, err)
    // 验证新字段存在
    assert.NotNil(t, address.PaybotAddressId)
}
```

## 注意事项

1. **字段命名兼容性**：现有表中的 `lable`（应为label）和 `chan`（应为chain）字段名有拼写问题，但为了兼容性暂不修改

2. **数据类型一致性**：所有金额字段统一使用 `DECIMAL(36,18)` 确保精度

3. **外键约束**：当前设计使用逻辑外键而非物理外键，保持与现有系统一致

4. **时区处理**：所有时间字段统一使用UTC时间，在应用层转换

5. **JSON字段兼容性**：`payload` 字段使用JSON类型，需要MySQL 5.7+支持

6. **索引策略**：根据实际查询模式调整索引，避免过多索引影响写入性能

7. **数据迁移**：生产环境迁移前必须充分测试，准备回滚方案