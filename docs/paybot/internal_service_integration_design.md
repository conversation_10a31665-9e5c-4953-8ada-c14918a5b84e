# PayBot 内部服务集成设计文档

## 概述

### 目标
将PayBot测试代码中的API功能封装成**内部服务方法**，供系统内部模块调用，实现PayBot功能与现有支付系统的无缝集成。

### 核心理念
- **内部服务**：不暴露HTTP API，纯内部方法调用
- **功能封装**：将PayBot API调用封装成统一的服务接口
- **数据同步**：PayBot操作结果同步到本地数据库
- **架构一致**：遵循现有Service/DAO分层架构

## PayBot功能分析

### 从测试文件提取的核心功能

#### 1. 授权支付功能 (`test_auth_payment_apis.go`)
```go
// 核心功能：
- 创建授权支付订单（加款/扣款）
- 查询支付订单（按订单号或商户订单号）
- 列出支付订单（分页查询，支持筛选）
- 异常场景处理

// 主要用途：
- Bot用户间转账
- 商户向用户加款
- 用户向商户扣款
```

#### 2. 充值功能 (`test_deposit_apis.go`)
```go
// 核心功能：
- 获取充值地址（支持TRX、TRC20、ETH、ERC20）
- 查询充值记录（分页，支持条件筛选）
- 获取充值详情（按ID或交易哈希）
- 管理充值地址列表

// 主要用途：
- 为用户生成专属充值地址
- 监控充值状态
- 充值历史查询
```

#### 3. 交易查询功能 (`test_transaction_apis.go`)
```go
// 核心功能：
- 查询交易记录（分页，按类型筛选）
- 获取交易详情

// 主要用途：
- 交易历史查询
- 交易状态跟踪
```

#### 4. 提现功能 (`test_withdraw_comprehensive.go`)
```go
// 核心功能：
- 查询提现记录（分页，状态筛选）
- 获取提现详情

// 主要用途：
- 提现历史查询
- 提现状态跟踪
```

#### 5. 回调处理功能 (`callback_receiver.go`)
```go
// 核心功能：
- 处理充值确认回调
- 处理提现完成回调
- HMAC签名验证

// 主要用途：
- 实时更新交易状态
- 触发后续业务逻辑
```

## 内部服务设计

### 服务接口定义

```go
package paybot

import (
    "context"
    "github.com/shopspring/decimal"
)

// IPayBotService PayBot内部服务接口
type IPayBotService interface {
    // 授权支付相关
    CreateAuthPayment(ctx context.Context, req *CreateAuthPaymentRequest) (*AuthPaymentResult, error)
    QueryAuthPayment(ctx context.Context, req *QueryAuthPaymentRequest) (*AuthPaymentResult, error)
    ListAuthPayments(ctx context.Context, req *ListAuthPaymentsRequest) (*AuthPaymentListResult, error)
    
    // 充值相关
    GetDepositAddress(ctx context.Context, req *GetDepositAddressRequest) (*DepositAddressResult, error)
    QueryDepositRecords(ctx context.Context, req *QueryDepositsRequest) (*DepositListResult, error)
    GetDepositDetail(ctx context.Context, req *GetDepositDetailRequest) (*DepositDetailResult, error)
    ListDepositAddresses(ctx context.Context, req *ListDepositAddressesRequest) (*DepositAddressListResult, error)
    
    // 交易查询
    QueryTransactions(ctx context.Context, req *QueryTransactionsRequest) (*TransactionListResult, error)
    GetTransactionDetail(ctx context.Context, req *GetTransactionDetailRequest) (*TransactionDetailResult, error)
    
    // 提现相关
    QueryWithdrawals(ctx context.Context, req *QueryWithdrawalsRequest) (*WithdrawalListResult, error)
    GetWithdrawalDetail(ctx context.Context, req *GetWithdrawalDetailRequest) (*WithdrawalDetailResult, error)
    
    // 回调处理
    ProcessDepositCallback(ctx context.Context, req *DepositCallbackRequest) error
    ProcessWithdrawalCallback(ctx context.Context, req *WithdrawalCallbackRequest) error
    
    // 健康检查
    HealthCheck(ctx context.Context) error
}
```

### 请求/响应数据结构

#### 授权支付相关
```go
// CreateAuthPaymentRequest 创建授权支付请求
type CreateAuthPaymentRequest struct {
    UserAccount     string          `json:"userAccount"`     // 用户账户
    OrderType       string          `json:"orderType"`       // 订单类型: add/deduct
    TokenSymbol     string          `json:"tokenSymbol"`     // 代币符号
    Amount          decimal.Decimal `json:"amount"`          // 金额
    AuthReason      string          `json:"authReason"`      // 授权原因
    MerchantOrderNo string          `json:"merchantOrderNo"` // 商户订单号
    ExpireMinutes   int             `json:"expireMinutes"`   // 过期时间(分钟)
    CallbackUrl     string          `json:"callbackUrl"`     // 回调URL
}

// AuthPaymentResult 授权支付结果
type AuthPaymentResult struct {
    OrderNo         string          `json:"orderNo"`         // 系统订单号
    MerchantOrderNo string          `json:"merchantOrderNo"` // 商户订单号
    UserAccount     string          `json:"userAccount"`     // 用户账户
    OrderType       string          `json:"orderType"`       // 订单类型
    TokenSymbol     string          `json:"tokenSymbol"`     // 代币符号
    Amount          decimal.Decimal `json:"amount"`          // 金额
    AuthReason      string          `json:"authReason"`      // 授权原因
    Status          string          `json:"status"`          // 状态
    CallbackStatus  string          `json:"callbackStatus"`  // 回调状态
    ExpireAt        *time.Time      `json:"expireAt"`        // 过期时间
    CompletedAt     *time.Time      `json:"completedAt"`     // 完成时间
    ErrorMessage    string          `json:"errorMessage"`    // 错误信息
    CreatedAt       time.Time       `json:"createdAt"`       // 创建时间
}

// QueryAuthPaymentRequest 查询授权支付请求
type QueryAuthPaymentRequest struct {
    OrderNo         string `json:"orderNo"`         // 系统订单号
    MerchantOrderNo string `json:"merchantOrderNo"` // 商户订单号
}

// ListAuthPaymentsRequest 列出授权支付请求
type ListAuthPaymentsRequest struct {
    Page        int    `json:"page"`        // 页码
    PageSize    int    `json:"pageSize"`    // 每页数量
    OrderType   string `json:"orderType"`   // 订单类型
    Status      string `json:"status"`      // 状态
    UserAccount string `json:"userAccount"` // 用户账户
    StartTime   string `json:"startTime"`   // 开始时间
    EndTime     string `json:"endTime"`     // 结束时间
}

// AuthPaymentListResult 授权支付列表结果
type AuthPaymentListResult struct {
    Total    int                  `json:"total"`    // 总数
    Page     int                  `json:"page"`     // 当前页
    PageSize int                  `json:"pageSize"` // 每页数量
    List     []*AuthPaymentResult `json:"list"`     // 数据列表
}
```

#### 充值相关
```go
// GetDepositAddressRequest 获取充值地址请求
type GetDepositAddressRequest struct {
    UserLabel string `json:"userLabel"` // 用户标识
    Chain     string `json:"chain"`     // 区块链: TRX/TRC20/ETH/ERC20
    Token     string `json:"token"`     // 代币: native或合约地址
}

// DepositAddressResult 充值地址结果
type DepositAddressResult struct {
    Address   string    `json:"address"`   // 充值地址
    UserLabel string    `json:"userLabel"` // 用户标识
    Chain     string    `json:"chain"`     // 区块链
    Token     string    `json:"token"`     // 代币
    IsReused  bool      `json:"isReused"`  // 是否复用
    QRCode    string    `json:"qrCode"`    // 二维码
    CreatedAt time.Time `json:"createdAt"` // 创建时间
}

// QueryDepositsRequest 查询充值记录请求
type QueryDepositsRequest struct {
    Page      int    `json:"page"`      // 页码
    PageSize  int    `json:"pageSize"`  // 每页数量
    Chain     string `json:"chain"`     // 区块链
    Status    string `json:"status"`    // 状态
    UserLabel string `json:"userLabel"` // 用户标识
    StartTime string `json:"startTime"` // 开始时间
    EndTime   string `json:"endTime"`   // 结束时间
    MinAmount string `json:"minAmount"` // 最小金额
    MaxAmount string `json:"maxAmount"` // 最大金额
}

// DepositRecord 充值记录
type DepositRecord struct {
    ID            int             `json:"id"`            // 记录ID
    MerchantID    int             `json:"merchantId"`    // 商户ID
    UserLabel     string          `json:"userLabel"`     // 用户标识
    Address       string          `json:"address"`       // 充值地址
    Chain         string          `json:"chain"`         // 区块链
    Token         string          `json:"token"`         // 代币
    Amount        decimal.Decimal `json:"amount"`        // 金额
    TxHash        string          `json:"txHash"`        // 交易哈希
    Status        string          `json:"status"`        // 状态
    Confirmations int             `json:"confirmations"` // 确认数
    CallbackSent  bool            `json:"callbackSent"`  // 回调状态
    CreatedAt     time.Time       `json:"createdAt"`     // 创建时间
}

// DepositListResult 充值列表结果
type DepositListResult struct {
    Total    int              `json:"total"`    // 总数
    Page     int              `json:"page"`     // 当前页
    PageSize int              `json:"pageSize"` // 每页数量
    Data     []*DepositRecord `json:"data"`     // 数据列表
}
```

#### 交易查询相关
```go
// QueryTransactionsRequest 查询交易请求
type QueryTransactionsRequest struct {
    Page     int    `json:"page"`     // 页码
    PageSize int    `json:"pageSize"` // 每页数量
    Type     string `json:"type"`     // 交易类型
    Status   string `json:"status"`   // 状态
}

// TransactionRecord 交易记录
type TransactionRecord struct {
    ID         int             `json:"id"`         // 记录ID
    MerchantID int             `json:"merchantId"` // 商户ID
    Type       string          `json:"type"`       // 交易类型
    Chain      string          `json:"chain"`      // 区块链
    Token      string          `json:"token"`      // 代币
    Amount     decimal.Decimal `json:"amount"`     // 金额
    Status     string          `json:"status"`     // 状态
    CreatedAt  time.Time       `json:"createdAt"`  // 创建时间
}

// TransactionListResult 交易列表结果
type TransactionListResult struct {
    Total    int                  `json:"total"`    // 总数
    Page     int                  `json:"page"`     // 当前页
    PageSize int                  `json:"pageSize"` // 每页数量
    Data     []*TransactionRecord `json:"data"`     // 数据列表
}
```

#### 回调相关
```go
// DepositCallbackRequest 充值回调请求
type DepositCallbackRequest struct {
    EventType     string          `json:"eventType"`     // 事件类型
    OrderNo       string          `json:"orderNo"`       // 订单号
    MerchantID    int             `json:"merchantId"`    // 商户ID
    Amount        decimal.Decimal `json:"amount"`        // 金额
    Currency      string          `json:"currency"`      // 币种
    FromAddress   string          `json:"fromAddress"`   // 来源地址
    ToAddress     string          `json:"toAddress"`     // 目标地址
    TxHash        string          `json:"txHash"`        // 交易哈希
    Confirmations int             `json:"confirmations"` // 确认数
    CompletedAt   time.Time       `json:"completedAt"`   // 完成时间
    Timestamp     int64           `json:"timestamp"`     // 时间戳
}

// WithdrawalCallbackRequest 提现回调请求
type WithdrawalCallbackRequest struct {
    EventType    string          `json:"eventType"`    // 事件类型
    OrderNo      string          `json:"orderNo"`      // 订单号
    MerchantID   int             `json:"merchantId"`   // 商户ID
    Amount       decimal.Decimal `json:"amount"`       // 金额
    Currency     string          `json:"currency"`     // 币种
    ActualAmount decimal.Decimal `json:"actualAmount"` // 实际金额
    HandlingFee  decimal.Decimal `json:"handlingFee"`  // 手续费
    TxHash       string          `json:"txHash"`       // 交易哈希
    CompletedAt  time.Time       `json:"completedAt"`  // 完成时间
    Timestamp    int64           `json:"timestamp"`    // 时间戳
}
```

## 服务实现架构

### 服务实现结构

```go
package impl

import (
    "context"
    "sync"
    "time"
    
    "github.com/gogf/gf/v2/os/glog"
    "github.com/shopspring/decimal"
    "go.uber.org/zap"
    
    "your-project/internal/service/paybot"
    "your-project/internal/dao"
    "your-project/internal/service"
)

type PayBotService struct {
    // PayBot API 客户端
    client *PayBotAPIClient
    
    // 配置
    config *PayBotConfig
    
    // 日志
    logger *zap.Logger
    
    // 与现有服务的集成
    paymentSvc service.IPaymentRequest // 现有支付服务
    txSvc      service.ITransaction    // 现有交易服务
    walletSvc  service.IWallet        // 现有钱包服务
    
    // 数据同步
    syncEnabled bool
    syncQueue   chan *SyncTask
    syncWG      sync.WaitGroup
    
    // 错误重试
    retryConfig *RetryConfig
}

// PayBotAPIClient PayBot API 客户端
type PayBotAPIClient struct {
    baseURL    string
    apiKey     string
    secretHash string
    timeout    time.Duration
    client     *http.Client
    logger     *zap.Logger
}

// PayBotConfig PayBot 配置
type PayBotConfig struct {
    Enabled bool `yaml:"enabled"`
    
    API struct {
        BaseURL    string        `yaml:"base_url"`
        APIKey     string        `yaml:"api_key"`
        SecretHash string        `yaml:"secret_hash"`
        Timeout    time.Duration `yaml:"timeout"`
    } `yaml:"api"`
    
    Features struct {
        AuthPayment bool `yaml:"auth_payment"`
        Deposits    bool `yaml:"deposits"`
        Withdrawals bool `yaml:"withdrawals"`
        Callbacks   bool `yaml:"callbacks"`
    } `yaml:"features"`
    
    Sync struct {
        Enabled   bool `yaml:"enabled"`
        BatchSize int  `yaml:"batch_size"`
    } `yaml:"sync"`
    
    Retry struct {
        MaxRetries int           `yaml:"max_retries"`
        Backoff    time.Duration `yaml:"backoff"`
    } `yaml:"retry"`
}

// SyncTask 同步任务
type SyncTask struct {
    Type      string      // "auth_payment", "deposit", "transaction", "withdrawal"
    Operation string      // "create", "update", "status_change"
    Data      interface{} // 具体数据
}

// RetryConfig 重试配置
type RetryConfig struct {
    MaxRetries int
    Backoff    time.Duration
}
```

### 核心方法实现示例

#### 1. 创建授权支付
```go
func (s *PayBotService) CreateAuthPayment(ctx context.Context, req *paybot.CreateAuthPaymentRequest) (*paybot.AuthPaymentResult, error) {
    // 1. 参数验证
    if err := s.validateCreateAuthPaymentRequest(req); err != nil {
        return nil, err
    }
    
    // 2. 构建PayBot API请求
    apiReq := &CreateAuthPaymentAPIRequest{
        UserAccount:     req.UserAccount,
        OrderType:       req.OrderType,
        TokenSymbol:     req.TokenSymbol,
        Amount:          req.Amount.String(),
        AuthReason:      req.AuthReason,
        MerchantOrderNo: req.MerchantOrderNo,
        ExpireMinutes:   req.ExpireMinutes,
        CallbackUrl:     req.CallbackUrl,
    }
    
    // 3. 调用PayBot API
    apiResp, err := s.client.CreateAuthPayment(ctx, apiReq)
    if err != nil {
        s.logger.Error("Failed to call PayBot API", zap.Error(err))
        return nil, err
    }
    
    // 4. 解析响应
    result := &paybot.AuthPaymentResult{
        OrderNo:         apiResp.Data.OrderNo,
        MerchantOrderNo: apiResp.Data.MerchantOrderNo,
        UserAccount:     apiResp.Data.UserAccount,
        OrderType:       apiResp.Data.OrderType,
        TokenSymbol:     apiResp.Data.TokenSymbol,
        Amount:          decimal.RequireFromString(apiResp.Data.Amount),
        Status:          apiResp.Data.Status,
        CreatedAt:       apiResp.Data.CreatedAt,
    }
    
    if apiResp.Data.ExpireAt != "" {
        if expireAt, err := time.Parse(time.RFC3339, apiResp.Data.ExpireAt); err == nil {
            result.ExpireAt = &expireAt
        }
    }
    
    // 5. 同步到本地数据库（异步）
    if s.syncEnabled {
        s.syncToLocal(ctx, &SyncTask{
            Type:      "auth_payment",
            Operation: "create",
            Data:      result,
        })
    }
    
    // 6. 记录操作日志
    s.logger.Info("Created auth payment", 
        zap.String("order_no", result.OrderNo),
        zap.String("user_account", result.UserAccount),
        zap.String("order_type", result.OrderType),
        zap.String("amount", result.Amount.String()),
    )
    
    return result, nil
}
```

#### 2. 获取充值地址
```go
func (s *PayBotService) GetDepositAddress(ctx context.Context, req *paybot.GetDepositAddressRequest) (*paybot.DepositAddressResult, error) {
    // 1. 参数验证
    if err := s.validateGetDepositAddressRequest(req); err != nil {
        return nil, err
    }
    
    // 2. 检查本地缓存（可选）
    if cached := s.getDepositAddressFromCache(req); cached != nil {
        return cached, nil
    }
    
    // 3. 调用PayBot API
    apiReq := &GetDepositAddressAPIRequest{
        UserLabel: req.UserLabel,
        Chain:     req.Chain,
        Token:     req.Token,
    }
    
    apiResp, err := s.client.GetDepositAddress(ctx, apiReq)
    if err != nil {
        return nil, err
    }
    
    // 4. 构建结果
    result := &paybot.DepositAddressResult{
        Address:   apiResp.Data.Address,
        UserLabel: apiResp.Data.UserLabel,
        Chain:     apiResp.Data.Chain,
        Token:     apiResp.Data.Token,
        IsReused:  apiResp.Data.IsReused,
        QRCode:    apiResp.Data.QRCode,
        CreatedAt: apiResp.Data.CreatedAt,
    }
    
    // 5. 缓存地址（可选）
    s.cacheDepositAddress(req, result)
    
    // 6. 同步到本地数据库
    if s.syncEnabled {
        s.syncToLocal(ctx, &SyncTask{
            Type:      "deposit_address",
            Operation: "create",
            Data:      result,
        })
    }
    
    return result, nil
}
```

#### 3. 处理回调
```go
func (s *PayBotService) ProcessDepositCallback(ctx context.Context, req *paybot.DepositCallbackRequest) error {
    // 1. 验证回调数据
    if err := s.validateDepositCallback(req); err != nil {
        return err
    }
    
    // 2. 更新本地数据库
    if err := s.updateDepositStatus(ctx, req); err != nil {
        s.logger.Error("Failed to update deposit status", zap.Error(err))
        return err
    }
    
    // 3. 触发业务逻辑
    switch req.EventType {
    case "deposit_confirmed":
        // 充值确认，触发余额更新
        if err := s.processDepositConfirmed(ctx, req); err != nil {
            s.logger.Error("Failed to process deposit confirmed", zap.Error(err))
            return err
        }
    }
    
    // 4. 记录回调日志
    s.logger.Info("Processed deposit callback",
        zap.String("event_type", req.EventType),
        zap.String("order_no", req.OrderNo),
        zap.String("tx_hash", req.TxHash),
        zap.String("amount", req.Amount.String()),
    )
    
    return nil
}

func (s *PayBotService) processDepositConfirmed(ctx context.Context, req *paybot.DepositCallbackRequest) error {
    // 调用现有的钱包服务更新余额
    return s.walletSvc.UpdateBalance(ctx, &service.UpdateBalanceRequest{
        UserAccount: req.OrderNo, // 这里需要根据实际业务逻辑映射用户账户
        TokenSymbol: req.Currency,
        Amount:      req.Amount,
        Type:        "deposit",
        Reference:   req.TxHash,
    })
}
```

### PayBot API 客户端实现

```go
package impl

import (
    "bytes"
    "context"
    "crypto/hmac"
    "crypto/sha256"
    "encoding/hex"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "strconv"
    "time"
    
    "go.uber.org/zap"
)

type PayBotAPIClient struct {
    baseURL    string
    apiKey     string
    secretHash string
    timeout    time.Duration
    client     *http.Client
    logger     *zap.Logger
}

func NewPayBotAPIClient(config *PayBotConfig, logger *zap.Logger) *PayBotAPIClient {
    return &PayBotAPIClient{
        baseURL:    config.API.BaseURL,
        apiKey:     config.API.APIKey,
        secretHash: config.API.SecretHash,
        timeout:    config.API.Timeout,
        client: &http.Client{
            Timeout: config.API.Timeout,
        },
        logger: logger,
    }
}

// generateSignature 生成HMAC签名
func (c *PayBotAPIClient) generateSignature(method, uri, timestamp, nonce, body string) string {
    signString := method + uri + timestamp + nonce + body
    h := hmac.New(sha256.New, []byte(c.secretHash))
    h.Write([]byte(signString))
    return hex.EncodeToString(h.Sum(nil))
}

// makeRequest 发送API请求
func (c *PayBotAPIClient) makeRequest(ctx context.Context, method, path string, data interface{}) (*APIResponse, error) {
    var jsonData []byte
    var err error
    
    if data != nil {
        jsonData, err = json.Marshal(data)
        if err != nil {
            return nil, fmt.Errorf("failed to marshal request data: %w", err)
        }
    }
    
    // 生成认证参数
    timestamp := strconv.FormatInt(time.Now().Unix(), 10)
    nonce := generateNonce()
    body := string(jsonData)
    signature := c.generateSignature(method, path, timestamp, nonce, body)
    
    // 创建HTTP请求
    url := c.baseURL + path
    var req *http.Request
    if len(jsonData) > 0 {
        req, err = http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(jsonData))
    } else {
        req, err = http.NewRequestWithContext(ctx, method, url, nil)
    }
    
    if err != nil {
        return nil, fmt.Errorf("failed to create HTTP request: %w", err)
    }
    
    // 设置请求头
    if len(jsonData) > 0 {
        req.Header.Set("Content-Type", "application/json")
    }
    req.Header.Set("X-API-Key", c.apiKey)
    req.Header.Set("X-Timestamp", timestamp)
    req.Header.Set("X-Nonce", nonce)
    req.Header.Set("X-Signature", signature)
    
    // 发送请求
    resp, err := c.client.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to send HTTP request: %w", err)
    }
    defer resp.Body.Close()
    
    // 读取响应
    responseBody, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %w", err)
    }
    
    // 检查HTTP状态码
    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("HTTP error: %d, body: %s", resp.StatusCode, string(responseBody))
    }
    
    // 解析响应
    var apiResp APIResponse
    if err := json.Unmarshal(responseBody, &apiResp); err != nil {
        return nil, fmt.Errorf("failed to unmarshal response: %w", err)
    }
    
    // 检查API错误
    if apiResp.Code != 0 {
        return nil, fmt.Errorf("API error: code=%d, message=%s", apiResp.Code, apiResp.Message)
    }
    
    return &apiResp, nil
}

// CreateAuthPayment 创建授权支付
func (c *PayBotAPIClient) CreateAuthPayment(ctx context.Context, req *CreateAuthPaymentAPIRequest) (*CreateAuthPaymentAPIResponse, error) {
    resp, err := c.makeRequest(ctx, "POST", "/api/v1/auth-payment/create", req)
    if err != nil {
        return nil, err
    }
    
    var result CreateAuthPaymentAPIResponse
    if err := json.Unmarshal(resp.Data, &result); err != nil {
        return nil, fmt.Errorf("failed to unmarshal auth payment response: %w", err)
    }
    
    return &result, nil
}

// GetDepositAddress 获取充值地址
func (c *PayBotAPIClient) GetDepositAddress(ctx context.Context, req *GetDepositAddressAPIRequest) (*GetDepositAddressAPIResponse, error) {
    resp, err := c.makeRequest(ctx, "POST", "/api/v1/deposits/address", req)
    if err != nil {
        return nil, err
    }
    
    var result GetDepositAddressAPIResponse
    if err := json.Unmarshal(resp.Data, &result); err != nil {
        return nil, fmt.Errorf("failed to unmarshal deposit address response: %w", err)
    }
    
    return &result, nil
}
```

## 与现有系统集成

### 服务注册

```go
// internal/service/service.go

var (
    localPayBot paybot.IPayBotService
)

// RegisterPayBot 注册PayBot服务
func RegisterPayBot(i paybot.IPayBotService) {
    localPayBot = i
}

// PayBot 获取PayBot服务实例
func PayBot() paybot.IPayBotService {
    if localPayBot == nil {
        panic("implement not found for interface IPayBotService, forgot register?")
    }
    return localPayBot
}
```

### 服务初始化

```go
// internal/service/paybot/impl/init.go

import (
    "your-project/internal/service"
    "your-project/internal/service/paybot/impl"
)

func init() {
    // 注册PayBot服务
    service.RegisterPayBot(impl.NewPayBotService())
}

func NewPayBotService() *PayBotService {
    // 从配置中获取PayBot配置
    config := loadPayBotConfig()
    
    // 创建API客户端
    client := NewPayBotAPIClient(config, glog.New())
    
    // 创建服务实例
    service := &PayBotService{
        client:      client,
        config:      config,
        logger:      glog.New(),
        paymentSvc:  service.PaymentRequest(),
        txSvc:       service.Transaction(),
        walletSvc:   service.Wallet(),
        syncEnabled: config.Sync.Enabled,
        syncQueue:   make(chan *SyncTask, 1000),
        retryConfig: &RetryConfig{
            MaxRetries: config.Retry.MaxRetries,
            Backoff:    config.Retry.Backoff,
        },
    }
    
    // 启动同步协程
    if service.syncEnabled {
        service.startSyncWorker()
    }
    
    return service
}
```

### 配置管理

```yaml
# manifest/config/config.yaml

paybot:
  enabled: true
  api:
    base_url: "http://paybot-api:9000"
    api_key: "${PAYBOT_API_KEY}"
    secret_hash: "${PAYBOT_SECRET_HASH}"
    timeout: 30s
  
  features:
    auth_payment: true
    deposits: true
    withdrawals: true
    callbacks: true
  
  sync:
    enabled: true
    batch_size: 100
  
  retry:
    max_retries: 3
    backoff: 1s
```

### 在Bot逻辑中使用

```go
// internal/logic/payment/deposit_logic.go

import (
    "context"
    "your-project/internal/service"
    "your-project/internal/service/paybot"
)

// HandleDepositRequest 处理充值请求
func (l *DepositLogic) HandleDepositRequest(ctx context.Context, userAccount, chain, token string) error {
    // 调用PayBot服务获取充值地址
    depositAddr, err := service.PayBot().GetDepositAddress(ctx, &paybot.GetDepositAddressRequest{
        UserLabel: userAccount,
        Chain:     chain,
        Token:     token,
    })
    if err != nil {
        l.logger.Error("Failed to get deposit address", zap.Error(err))
        return err
    }
    
    // 向用户发送充值地址
    message := fmt.Sprintf("请向以下地址充值：\n地址：%s\n网络：%s\n代币：%s", 
        depositAddr.Address, depositAddr.Chain, depositAddr.Token)
    
    return l.sendMessageToUser(ctx, userAccount, message, depositAddr.QRCode)
}
```

```go
// internal/logic/payment/auth_payment_logic.go

// HandleTransferRequest 处理转账请求
func (l *TransferLogic) HandleTransferRequest(ctx context.Context, fromUser, toUser, tokenSymbol string, amount decimal.Decimal) error {
    // 1. 从发送方扣款
    deductReq := &paybot.CreateAuthPaymentRequest{
        UserAccount:     fromUser,
        OrderType:       "deduct",
        TokenSymbol:     tokenSymbol,
        Amount:          amount,
        AuthReason:      fmt.Sprintf("转账给用户 %s", toUser),
        MerchantOrderNo: generateTransferOrderNo(),
        ExpireMinutes:   30,
    }
    
    deductResp, err := service.PayBot().CreateAuthPayment(ctx, deductReq)
    if err != nil {
        return fmt.Errorf("扣款失败: %w", err)
    }
    
    // 2. 给接收方加款
    addReq := &paybot.CreateAuthPaymentRequest{
        UserAccount:     toUser,
        OrderType:       "add",
        TokenSymbol:     tokenSymbol,
        Amount:          amount,
        AuthReason:      fmt.Sprintf("收到来自用户 %s 的转账", fromUser),
        MerchantOrderNo: generateTransferOrderNo(),
        ExpireMinutes:   30,
    }
    
    addResp, err := service.PayBot().CreateAuthPayment(ctx, addReq)
    if err != nil {
        // 如果加款失败，需要撤销扣款操作
        l.logger.Error("加款失败，需要撤销扣款", 
            zap.String("deduct_order", deductResp.OrderNo),
            zap.Error(err))
        return fmt.Errorf("转账失败: %w", err)
    }
    
    l.logger.Info("转账完成",
        zap.String("from_user", fromUser),
        zap.String("to_user", toUser),
        zap.String("amount", amount.String()),
        zap.String("deduct_order", deductResp.OrderNo),
        zap.String("add_order", addResp.OrderNo),
    )
    
    return nil
}
```

## 数据同步设计

### 同步机制

```go
// 同步工作协程
func (s *PayBotService) startSyncWorker() {
    s.syncWG.Add(1)
    go func() {
        defer s.syncWG.Done()
        
        for task := range s.syncQueue {
            if err := s.processSyncTask(task); err != nil {
                s.logger.Error("Failed to process sync task",
                    zap.String("type", task.Type),
                    zap.String("operation", task.Operation),
                    zap.Error(err),
                )
            }
        }
    }()
}

// 处理同步任务
func (s *PayBotService) processSyncTask(task *SyncTask) error {
    switch task.Type {
    case "auth_payment":
        return s.syncAuthPayment(task)
    case "deposit_address":
        return s.syncDepositAddress(task)
    case "deposit":
        return s.syncDeposit(task)
    case "withdrawal":
        return s.syncWithdrawal(task)
    default:
        return fmt.Errorf("unknown sync task type: %s", task.Type)
    }
}

// 同步授权支付到本地
func (s *PayBotService) syncAuthPayment(task *SyncTask) error {
    authPayment, ok := task.Data.(*paybot.AuthPaymentResult)
    if !ok {
        return fmt.Errorf("invalid auth payment data")
    }
    
    switch task.Operation {
    case "create":
        // 创建本地支付请求记录
        paymentReq := &entity.PaymentRequest{
            OrderNo:         authPayment.OrderNo,
            MerchantOrderNo: authPayment.MerchantOrderNo,
            UserAccount:     authPayment.UserAccount,
            OrderType:       authPayment.OrderType,
            TokenSymbol:     authPayment.TokenSymbol,
            Amount:          authPayment.Amount,
            AuthReason:      authPayment.AuthReason,
            Status:          authPayment.Status,
            ExpireAt:        authPayment.ExpireAt,
            CreatedAt:       authPayment.CreatedAt,
        }
        
        return s.paymentSvc.CreatePaymentRequest(context.Background(), paymentReq)
        
    case "update":
        // 更新本地支付请求状态
        return s.paymentSvc.UpdatePaymentRequestStatus(context.Background(), 
            authPayment.OrderNo, authPayment.Status)
    }
    
    return nil
}
```

### 数据库扩展

```sql
-- 扩展现有表以支持PayBot集成

-- 添加PayBot相关字段到payment_requests表
ALTER TABLE payment_requests 
ADD COLUMN paybot_order_no VARCHAR(64) DEFAULT NULL COMMENT 'PayBot订单号',
ADD COLUMN paybot_sync_status ENUM('pending', 'synced', 'failed') DEFAULT 'pending' COMMENT 'PayBot同步状态',
ADD COLUMN paybot_sync_at TIMESTAMP NULL COMMENT 'PayBot同步时间';

-- 创建PayBot充值地址表
CREATE TABLE paybot_deposit_addresses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_label VARCHAR(100) NOT NULL COMMENT '用户标识',
    address VARCHAR(100) NOT NULL COMMENT '充值地址',
    chain VARCHAR(20) NOT NULL COMMENT '区块链',
    token VARCHAR(100) NOT NULL COMMENT '代币',
    qr_code TEXT COMMENT '二维码',
    is_reused TINYINT(1) DEFAULT 0 COMMENT '是否复用',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_chain_token (user_label, chain, token),
    INDEX idx_address (address),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PayBot充值地址表';

-- 创建PayBot同步日志表
CREATE TABLE paybot_sync_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    sync_type VARCHAR(50) NOT NULL COMMENT '同步类型',
    operation VARCHAR(50) NOT NULL COMMENT '操作类型',
    order_no VARCHAR(64) NOT NULL COMMENT '订单号',
    status ENUM('success', 'failed') NOT NULL COMMENT '同步状态',
    error_message TEXT COMMENT '错误信息',
    sync_data JSON COMMENT '同步数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_order_no (order_no),
    INDEX idx_sync_type (sync_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PayBot同步日志表';
```

## 错误处理与重试

### 错误分类

```go
package paybot

import "fmt"

// PayBotError PayBot错误
type PayBotError struct {
    Code       string `json:"code"`       // 错误码
    Message    string `json:"message"`    // 错误信息
    Type       string `json:"type"`       // 错误类型: network, api, business, config
    Retryable  bool   `json:"retryable"`  // 是否可重试
    StatusCode int    `json:"statusCode"` // HTTP状态码
}

func (e *PayBotError) Error() string {
    return fmt.Sprintf("PayBot Error [%s]: %s", e.Code, e.Message)
}

// 预定义错误
var (
    ErrNetworkTimeout    = &PayBotError{Code: "NETWORK_TIMEOUT", Type: "network", Retryable: true, Message: "网络超时"}
    ErrAPIKeyInvalid     = &PayBotError{Code: "API_KEY_INVALID", Type: "api", Retryable: false, Message: "API密钥无效"}
    ErrSignatureInvalid  = &PayBotError{Code: "SIGNATURE_INVALID", Type: "api", Retryable: false, Message: "签名验证失败"}
    ErrInsufficientFunds = &PayBotError{Code: "INSUFFICIENT_FUNDS", Type: "business", Retryable: false, Message: "余额不足"}
    ErrOrderNotFound     = &PayBotError{Code: "ORDER_NOT_FOUND", Type: "business", Retryable: false, Message: "订单不存在"}
    ErrRateLimitExceeded = &PayBotError{Code: "RATE_LIMIT_EXCEEDED", Type: "api", Retryable: true, Message: "请求频率超限"}
    ErrServiceUnavailable = &PayBotError{Code: "SERVICE_UNAVAILABLE", Type: "network", Retryable: true, Message: "服务不可用"}
)
```

### 重试机制

```go
// 重试配置
type RetryConfig struct {
    MaxRetries int           // 最大重试次数
    Backoff    time.Duration // 退避时间
    MaxBackoff time.Duration // 最大退避时间
}

// 带重试的API调用
func (c *PayBotAPIClient) makeRequestWithRetry(ctx context.Context, method, path string, data interface{}) (*APIResponse, error) {
    var lastErr error
    
    for i := 0; i <= c.retryConfig.MaxRetries; i++ {
        if i > 0 {
            // 指数退避
            backoff := time.Duration(i) * c.retryConfig.Backoff
            if backoff > c.retryConfig.MaxBackoff {
                backoff = c.retryConfig.MaxBackoff
            }
            
            select {
            case <-ctx.Done():
                return nil, ctx.Err()
            case <-time.After(backoff):
            }
            
            c.logger.Info("Retrying PayBot API request",
                zap.String("method", method),
                zap.String("path", path),
                zap.Int("retry", i),
                zap.Duration("backoff", backoff),
            )
        }
        
        resp, err := c.makeRequest(ctx, method, path, data)
        if err == nil {
            return resp, nil
        }
        
        lastErr = err
        
        // 检查是否可重试
        if !c.isRetryableError(err) {
            break
        }
    }
    
    return nil, lastErr
}

// 判断错误是否可重试
func (c *PayBotAPIClient) isRetryableError(err error) bool {
    var payBotErr *PayBotError
    if errors.As(err, &payBotErr) {
        return payBotErr.Retryable
    }
    
    // 网络错误通常可重试
    if isNetworkError(err) {
        return true
    }
    
    return false
}
```

## 监控与日志

### 结构化日志

```go
// 日志记录器
type PayBotLogger struct {
    logger *zap.Logger
}

func NewPayBotLogger() *PayBotLogger {
    logger, _ := zap.NewProduction()
    return &PayBotLogger{logger: logger}
}

// 记录API调用
func (l *PayBotLogger) LogAPICall(method, path string, req, resp interface{}, duration time.Duration, err error) {
    fields := []zap.Field{
        zap.String("method", method),
        zap.String("path", path),
        zap.Duration("duration", duration),
    }
    
    if req != nil {
        if reqData, marshalErr := json.Marshal(req); marshalErr == nil {
            fields = append(fields, zap.String("request", string(reqData)))
        }
    }
    
    if resp != nil {
        if respData, marshalErr := json.Marshal(resp); marshalErr == nil {
            fields = append(fields, zap.String("response", string(respData)))
        }
    }
    
    if err != nil {
        fields = append(fields, zap.Error(err))
        l.logger.Error("PayBot API call failed", fields...)
    } else {
        l.logger.Info("PayBot API call succeeded", fields...)
    }
}

// 记录同步操作
func (l *PayBotLogger) LogSync(syncType, operation, orderNo string, success bool, err error) {
    fields := []zap.Field{
        zap.String("sync_type", syncType),
        zap.String("operation", operation),
        zap.String("order_no", orderNo),
        zap.Bool("success", success),
    }
    
    if err != nil {
        fields = append(fields, zap.Error(err))
        l.logger.Error("PayBot sync failed", fields...)
    } else {
        l.logger.Info("PayBot sync succeeded", fields...)
    }
}
```

### 性能监控

```go
// 性能指标
type PayBotMetrics struct {
    // API调用统计
    APICallsTotal     *prometheus.CounterVec   // 总调用次数
    APICallDuration   *prometheus.HistogramVec // 调用耗时
    APICallErrors     *prometheus.CounterVec   // 错误次数
    
    // 同步统计
    SyncTotal         *prometheus.CounterVec   // 同步总次数
    SyncErrors        *prometheus.CounterVec   // 同步错误次数
    SyncQueueSize     prometheus.Gauge         // 同步队列大小
}

func NewPayBotMetrics() *PayBotMetrics {
    return &PayBotMetrics{
        APICallsTotal: promauto.NewCounterVec(
            prometheus.CounterOpts{
                Name: "paybot_api_calls_total",
                Help: "Total number of PayBot API calls",
            },
            []string{"method", "path", "status"},
        ),
        APICallDuration: promauto.NewHistogramVec(
            prometheus.HistogramOpts{
                Name:    "paybot_api_call_duration_seconds",
                Help:    "PayBot API call duration in seconds",
                Buckets: prometheus.DefBuckets,
            },
            []string{"method", "path"},
        ),
        // ... 其他指标
    }
}

// 记录API调用指标
func (m *PayBotMetrics) RecordAPICall(method, path string, duration time.Duration, err error) {
    status := "success"
    if err != nil {
        status = "error"
    }
    
    m.APICallsTotal.WithLabelValues(method, path, status).Inc()
    m.APICallDuration.WithLabelValues(method, path).Observe(duration.Seconds())
    
    if err != nil {
        m.APICallErrors.WithLabelValues(method, path).Inc()
    }
}
```

## 测试策略

### 单元测试

```go
package impl

import (
    "context"
    "testing"
    "time"
    
    "github.com/shopspring/decimal"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "go.uber.org/zap"
    
    "your-project/internal/service/paybot"
)

// Mock PayBot API Client
type MockPayBotAPIClient struct {
    mock.Mock
}

func (m *MockPayBotAPIClient) CreateAuthPayment(ctx context.Context, req *CreateAuthPaymentAPIRequest) (*CreateAuthPaymentAPIResponse, error) {
    args := m.Called(ctx, req)
    return args.Get(0).(*CreateAuthPaymentAPIResponse), args.Error(1)
}

func TestPayBotService_CreateAuthPayment(t *testing.T) {
    tests := []struct {
        name    string
        req     *paybot.CreateAuthPaymentRequest
        mockResp *CreateAuthPaymentAPIResponse
        mockErr error
        wantErr bool
    }{
        {
            name: "创建加款订单成功",
            req: &paybot.CreateAuthPaymentRequest{
                UserAccount:     "test_user",
                OrderType:       "add",
                TokenSymbol:     "USDT",
                Amount:          decimal.NewFromFloat(100.50),
                AuthReason:      "测试加款",
                MerchantOrderNo: "TEST_001",
                ExpireMinutes:   60,
            },
            mockResp: &CreateAuthPaymentAPIResponse{
                Data: AuthPaymentData{
                    OrderNo:         "AP123456789",
                    MerchantOrderNo: "TEST_001",
                    UserAccount:     "test_user",
                    OrderType:       "add",
                    TokenSymbol:     "USDT",
                    Amount:          "100.50",
                    Status:          "completed",
                    CreatedAt:       time.Now(),
                },
            },
            mockErr: nil,
            wantErr: false,
        },
        {
            name: "API调用失败",
            req: &paybot.CreateAuthPaymentRequest{
                UserAccount:     "test_user",
                OrderType:       "add",
                TokenSymbol:     "USDT",
                Amount:          decimal.NewFromFloat(100.50),
                AuthReason:      "测试加款",
                MerchantOrderNo: "TEST_002",
                ExpireMinutes:   60,
            },
            mockResp: nil,
            mockErr:  ErrNetworkTimeout,
            wantErr:  true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 创建Mock客户端
            mockClient := &MockPayBotAPIClient{}
            mockClient.On("CreateAuthPayment", mock.Anything, mock.Anything).
                Return(tt.mockResp, tt.mockErr)
            
            // 创建服务实例
            service := &PayBotService{
                client:      mockClient,
                logger:      zap.NewNop(),
                syncEnabled: false,
            }
            
            // 执行测试
            result, err := service.CreateAuthPayment(context.Background(), tt.req)
            
            // 验证结果
            if tt.wantErr {
                assert.Error(t, err)
                assert.Nil(t, result)
            } else {
                assert.NoError(t, err)
                assert.NotNil(t, result)
                assert.Equal(t, tt.mockResp.Data.OrderNo, result.OrderNo)
                assert.Equal(t, tt.req.UserAccount, result.UserAccount)
                assert.Equal(t, tt.req.OrderType, result.OrderType)
            }
            
            mockClient.AssertExpectations(t)
        })
    }
}
```

### 集成测试

```go
package integration

import (
    "context"
    "testing"
    "time"
    
    "github.com/shopspring/decimal"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
    
    "your-project/internal/service/paybot"
    "your-project/internal/service/paybot/impl"
)

type PayBotIntegrationTestSuite struct {
    suite.Suite
    service paybot.IPayBotService
}

func (suite *PayBotIntegrationTestSuite) SetupSuite() {
    // 创建测试配置
    config := &impl.PayBotConfig{
        Enabled: true,
        API: struct {
            BaseURL    string        `yaml:"base_url"`
            APIKey     string        `yaml:"api_key"`
            SecretHash string        `yaml:"secret_hash"`
            Timeout    time.Duration `yaml:"timeout"`
        }{
            BaseURL:    "http://**************:9000", // 测试环境
            APIKey:     "test_api_key",
            SecretHash: "test_secret_hash",
            Timeout:    30 * time.Second,
        },
        Sync: struct {
            Enabled   bool `yaml:"enabled"`
            BatchSize int  `yaml:"batch_size"`
        }{
            Enabled:   false, // 集成测试时禁用同步
            BatchSize: 100,
        },
    }
    
    // 创建服务实例
    suite.service = impl.NewPayBotServiceWithConfig(config)
}

func (suite *PayBotIntegrationTestSuite) TestCreateAndQueryAuthPayment() {
    ctx := context.Background()
    
    // 创建授权支付订单
    createReq := &paybot.CreateAuthPaymentRequest{
        UserAccount:     "integration_test_user",
        OrderType:       "add",
        TokenSymbol:     "USDT",
        Amount:          decimal.NewFromFloat(50.00),
        AuthReason:      "集成测试加款",
        MerchantOrderNo: fmt.Sprintf("INTEGRATION_TEST_%d", time.Now().Unix()),
        ExpireMinutes:   30,
    }
    
    createResp, err := suite.service.CreateAuthPayment(ctx, createReq)
    suite.NoError(err)
    suite.NotNil(createResp)
    suite.NotEmpty(createResp.OrderNo)
    suite.Equal(createReq.UserAccount, createResp.UserAccount)
    suite.Equal(createReq.OrderType, createResp.OrderType)
    
    // 查询创建的订单
    queryReq := &paybot.QueryAuthPaymentRequest{
        OrderNo: createResp.OrderNo,
    }
    
    queryResp, err := suite.service.QueryAuthPayment(ctx, queryReq)
    suite.NoError(err)
    suite.NotNil(queryResp)
    suite.Equal(createResp.OrderNo, queryResp.OrderNo)
    suite.Equal(createResp.UserAccount, queryResp.UserAccount)
}

func (suite *PayBotIntegrationTestSuite) TestGetDepositAddress() {
    ctx := context.Background()
    
    req := &paybot.GetDepositAddressRequest{
        UserLabel: "integration_test_user",
        Chain:     "TRX",
        Token:     "native",
    }
    
    resp, err := suite.service.GetDepositAddress(ctx, req)
    suite.NoError(err)
    suite.NotNil(resp)
    suite.NotEmpty(resp.Address)
    suite.Equal(req.UserLabel, resp.UserLabel)
    suite.Equal(req.Chain, resp.Chain)
    suite.Equal(req.Token, resp.Token)
}

func TestPayBotIntegrationTestSuite(t *testing.T) {
    suite.Run(t, new(PayBotIntegrationTestSuite))
}
```

## 部署与配置

### 环境变量

```bash
# PayBot相关环境变量
export PAYBOT_ENABLED=true
export PAYBOT_API_KEY="your_api_key_here"
export PAYBOT_SECRET_HASH="your_secret_hash_here"
export PAYBOT_BASE_URL="http://paybot-api:9000"
export PAYBOT_SYNC_ENABLED=true
```

### Docker配置

```dockerfile
# Dockerfile中添加PayBot相关配置
ENV PAYBOT_ENABLED=true
ENV PAYBOT_BASE_URL=http://paybot-api:9000
```

### Kubernetes ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: paybot-config
  namespace: botapi
data:
  paybot.yaml: |
    paybot:
      enabled: true
      api:
        base_url: "http://paybot-api:9000"
        timeout: 30s
      features:
        auth_payment: true
        deposits: true
        withdrawals: true
        callbacks: true
      sync:
        enabled: true
        batch_size: 100
      retry:
        max_retries: 3
        backoff: 1s
```

## 使用示例

### 在Bot逻辑中的完整使用示例

```go
// internal/logic/wallet/wallet_logic.go

package wallet

import (
    "context"
    "fmt"
    
    "github.com/shopspring/decimal"
    "go.uber.org/zap"
    
    "your-project/internal/service"
    "your-project/internal/service/paybot"
)

type WalletLogic struct {
    logger *zap.Logger
}

// HandleUserDeposit 处理用户充值
func (l *WalletLogic) HandleUserDeposit(ctx context.Context, userAccount, chain, token string) (string, error) {
    // 获取充值地址
    depositAddr, err := service.PayBot().GetDepositAddress(ctx, &paybot.GetDepositAddressRequest{
        UserLabel: userAccount,
        Chain:     chain,
        Token:     token,
    })
    if err != nil {
        l.logger.Error("获取充值地址失败", 
            zap.String("user", userAccount),
            zap.String("chain", chain),
            zap.String("token", token),
            zap.Error(err))
        return "", fmt.Errorf("获取充值地址失败: %w", err)
    }
    
    l.logger.Info("成功生成充值地址",
        zap.String("user", userAccount),
        zap.String("address", depositAddr.Address),
        zap.String("chain", chain),
        zap.String("token", token),
        zap.Bool("is_reused", depositAddr.IsReused))
    
    return depositAddr.Address, nil
}

// HandleUserTransfer 处理用户间转账
func (l *WalletLogic) HandleUserTransfer(ctx context.Context, fromUser, toUser, tokenSymbol string, amount decimal.Decimal) error {
    transferOrderNo := fmt.Sprintf("TRANSFER_%d", time.Now().Unix())
    
    // 1. 从发送方扣款
    deductReq := &paybot.CreateAuthPaymentRequest{
        UserAccount:     fromUser,
        OrderType:       "deduct",
        TokenSymbol:     tokenSymbol,
        Amount:          amount,
        AuthReason:      fmt.Sprintf("转账给用户 %s", toUser),
        MerchantOrderNo: transferOrderNo + "_DEDUCT",
        ExpireMinutes:   30,
    }
    
    deductResp, err := service.PayBot().CreateAuthPayment(ctx, deductReq)
    if err != nil {
        l.logger.Error("扣款失败",
            zap.String("from_user", fromUser),
            zap.String("amount", amount.String()),
            zap.Error(err))
        return fmt.Errorf("扣款失败: %w", err)
    }
    
    l.logger.Info("扣款成功",
        zap.String("order_no", deductResp.OrderNo),
        zap.String("from_user", fromUser),
        zap.String("amount", amount.String()))
    
    // 2. 给接收方加款
    addReq := &paybot.CreateAuthPaymentRequest{
        UserAccount:     toUser,
        OrderType:       "add",
        TokenSymbol:     tokenSymbol,
        Amount:          amount,
        AuthReason:      fmt.Sprintf("收到来自用户 %s 的转账", fromUser),
        MerchantOrderNo: transferOrderNo + "_ADD",
        ExpireMinutes:   30,
    }
    
    addResp, err := service.PayBot().CreateAuthPayment(ctx, addReq)
    if err != nil {
        l.logger.Error("加款失败，转账异常",
            zap.String("to_user", toUser),
            zap.String("amount", amount.String()),
            zap.String("deduct_order", deductResp.OrderNo),
            zap.Error(err))
        
        // 这里应该有补偿机制，但PayBot可能不支持撤销操作
        // 需要记录异常情况，人工处理
        return fmt.Errorf("转账失败，扣款已完成但加款失败: %w", err)
    }
    
    l.logger.Info("转账完成",
        zap.String("from_user", fromUser),
        zap.String("to_user", toUser),
        zap.String("amount", amount.String()),
        zap.String("deduct_order", deductResp.OrderNo),
        zap.String("add_order", addResp.OrderNo))
    
    return nil
}

// QueryUserTransactions 查询用户交易历史
func (l *WalletLogic) QueryUserTransactions(ctx context.Context, userAccount string, page, pageSize int) ([]*paybot.TransactionRecord, error) {
    resp, err := service.PayBot().QueryTransactions(ctx, &paybot.QueryTransactionsRequest{
        Page:     page,
        PageSize: pageSize,
        // 这里可能需要根据实际API支持的筛选条件调整
    })
    if err != nil {
        l.logger.Error("查询交易历史失败",
            zap.String("user", userAccount),
            zap.Error(err))
        return nil, fmt.Errorf("查询交易历史失败: %w", err)
    }
    
    return resp.Data, nil
}
```

### 在回调处理中的使用

```go
// internal/controller/webhook/paybot_webhook.go

package webhook

import (
    "context"
    "net/http"
    
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
    
    "your-project/internal/service"
    "your-project/internal/service/paybot"
)

type PayBotWebhookController struct {
    logger *zap.Logger
}

// HandleDepositCallback 处理充值回调
func (c *PayBotWebhookController) HandleDepositCallback(ctx *gin.Context) {
    var req paybot.DepositCallbackRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        c.logger.Error("解析充值回调数据失败", zap.Error(err))
        ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
        return
    }
    
    // 处理回调
    if err := service.PayBot().ProcessDepositCallback(ctx.Request.Context(), &req); err != nil {
        c.logger.Error("处理充值回调失败",
            zap.String("order_no", req.OrderNo),
            zap.String("tx_hash", req.TxHash),
            zap.Error(err))
        ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process callback"})
        return
    }
    
    c.logger.Info("充值回调处理成功",
        zap.String("order_no", req.OrderNo),
        zap.String("tx_hash", req.TxHash),
        zap.String("amount", req.Amount.String()))
    
    ctx.JSON(http.StatusOK, gin.H{"status": "success"})
}
```

## 总结

本设计文档提供了将PayBot API功能集成为内部服务方法的完整解决方案：

### 核心优势

1. **内部化**：不暴露HTTP API，纯内部服务调用
2. **架构一致**：遵循现有Service/DAO分层架构
3. **数据同步**：PayBot操作结果自动同步到本地数据库
4. **错误处理**：完善的错误分类和重试机制
5. **监控日志**：结构化日志和性能监控
6. **易于使用**：简洁的服务接口，便于业务逻辑调用

### 实现路径

1. **第一阶段**：实现核心服务接口和API客户端
2. **第二阶段**：添加数据同步功能
3. **第三阶段**：完善监控、日志和错误处理
4. **第四阶段**：集成测试和性能优化

### 使用方式

```go
// 系统内部任何地方都可以这样调用
resp, err := service.PayBot().CreateAuthPayment(ctx, req)
```

这种设计确保了PayBot功能与现有系统的无缝集成，同时保持了架构的一致性和可维护性。