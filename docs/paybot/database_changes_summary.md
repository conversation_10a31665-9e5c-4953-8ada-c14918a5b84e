# PayBot 数据库修改总结

## 修改概述

根据PayBot内部服务集成需求，对现有数据库进行以下修改：

### 1. 复用现有表
- **user_address** - 用于充值地址管理
- **user_withdraws** - 用于提现记录管理  
- **users** - 使用 `account` 字段作为PayBot的 `user_label`

### 2. 新建表
- **paybot_auth_orders** - 授权支付订单表
- **paybot_callbacks** - 回调记录表

### 3. 现有表扩展
- **user_address** 添加PayBot相关字段
- **user_withdraws** 添加PayBot同步字段

## 核心SQL脚本

### 执行顺序
1. 执行 `/docs/paybot/database_migration.sql`
2. 运行 `gf gen dao` 重新生成DAO文件
3. 更新相关业务代码

### 主要表结构

#### 新建：paybot_auth_orders（授权支付订单）
```sql
CREATE TABLE `paybot_auth_orders` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `paybot_order_no` VARCHAR(64) NOT NULL COMMENT 'PayBot系统订单号',
    `merchant_order_no` VARCHAR(64) NOT NULL COMMENT '商户订单号',
    `user_account` VARCHAR(255) NOT NULL COMMENT '用户账户（关联users.account）',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `order_type` ENUM('add', 'deduct') NOT NULL COMMENT '订单类型',
    `token_symbol` VARCHAR(20) NOT NULL COMMENT '代币符号',
    `amount` DECIMAL(36,18) NOT NULL COMMENT '交易金额',
    `status` ENUM('pending', 'completed', 'expired', 'cancelled') NOT NULL DEFAULT 'pending',
    -- ... 其他字段
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_paybot_order_no` (`paybot_order_no`)
);
```

#### 新建：paybot_callbacks（回调记录）
```sql
CREATE TABLE `paybot_callbacks` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `event_type` VARCHAR(50) NOT NULL COMMENT '事件类型',
    `order_no` VARCHAR(64) NOT NULL COMMENT '关联的订单号',
    `payload` JSON NOT NULL COMMENT '回调数据',
    `processed_status` ENUM('pending', 'success', 'failed') NOT NULL DEFAULT 'pending',
    -- ... 其他字段
    PRIMARY KEY (`id`)
);
```

#### 扩展：user_address
```sql
ALTER TABLE `user_address` 
ADD COLUMN `paybot_address_id` VARCHAR(64) NULL COMMENT 'PayBot系统地址ID',
ADD COLUMN `is_reused` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否复用地址',
ADD COLUMN `qr_code_data` TEXT NULL COMMENT '二维码数据',
ADD COLUMN `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active';
```

#### 扩展：user_withdraws  
```sql
ALTER TABLE `user_withdraws`
ADD COLUMN `paybot_order_no` VARCHAR(64) NULL COMMENT 'PayBot系统订单号',
ADD COLUMN `paybot_sync_status` ENUM('pending', 'synced', 'failed') NOT NULL DEFAULT 'pending',
ADD COLUMN `paybot_sync_at` TIMESTAMP NULL COMMENT 'PayBot同步时间';
```

## 数据映射关系

| PayBot字段 | 数据库映射 | 说明 |
|-----------|-----------|------|
| user_label | users.account | 用户标识符 |
| 充值地址 | user_address表 | 复用现有充值地址表 |
| 提现记录 | user_withdraws表 | 复用现有提现表 |
| 授权订单 | paybot_auth_orders表 | 新建专用表 |
| 回调记录 | paybot_callbacks表 | 新建专用表 |

## 文件说明

### 主要文档
- `database_migration.sql` - 完整的数据库迁移脚本
- `database_integration_design.md` - 详细的数据库集成设计
- `internal_service_integration_design.md` - 内部服务集成设计

### 实施步骤
1. 阅读 `database_integration_design.md` 了解完整设计
2. 在测试环境执行 `database_migration.sql`  
3. 运行 `gf gen dao` 重新生成DAO文件
4. 按照设计文档实现DAO扩展方法
5. 集成PayBot服务到现有系统

### 注意事项
- 现有 `user_address` 表中的字段名（`lable`、`chan`）保持不变以确保兼容性
- 所有金额字段使用 `DECIMAL(36,18)` 确保精度  
- 新增表使用标准的GoFrame命名规范
- 支持现有DAO模式，便于维护