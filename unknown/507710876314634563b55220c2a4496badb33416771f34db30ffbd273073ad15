package processor

import (
	"context"
	"fmt"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"

	"telegram-bot-api/internal/bot/chosen_inline_result"
	"telegram-bot-api/internal/bot/commands"
	"telegram-bot-api/internal/bot/inline_query"
	cover "telegram-bot-api/internal/bot/redpacket/cover"
	"telegram-bot-api/internal/model"
	"telegram-bot-api/internal/service"
)

// convertInlineKeyboardToModel converts tgbotapi.InlineKeyboardMarkup to model.InlineKeyboardButton format
func convertInlineKeyboardToModel(keyboard *tgbotapi.InlineKeyboardMarkup) [][]model.InlineKeyboardButton {
	if keyboard == nil || len(keyboard.InlineKeyboard) == 0 {
		return nil
	}

	var modelRows [][]model.InlineKeyboardButton
	for _, row := range keyboard.InlineKeyboard {
		var modelRow []model.InlineKeyboardButton
		for _, button := range row {
			modelButton := model.InlineKeyboardButton{
				Text: button.Text,
			}

			if button.CallbackData != nil {
				modelButton.CallbackData = *button.CallbackData
			}

			if button.URL != nil {
				modelButton.URL = *button.URL
			}

			modelRow = append(modelRow, modelButton)
		}
		modelRows = append(modelRows, modelRow)
	}

	return modelRows
}

// processUpdate routes the update to the appropriate handler based on its type.
func processUpdate(ctx context.Context, update *tgbotapi.Update) error {
	if update.Message != nil {
		// --- Backup Account Verification Check ---
		shouldStop, verificationErr := handleBackupVerification(ctx, update)
		if verificationErr != nil {
			// Log the error from verification initiation, but we still stop processing the original message.
			g.Log().Errorf(ctx, "Error during backup verification check for Update %d: %v", update.UpdateID, verificationErr)
			// The message should be marked as processed later in handleIncomingMessage
			return nil // Stop further processing in this chain
		}
		if shouldStop {
			g.Log().Infof(ctx, "Stopping processing for Update %d due to unverified backup account.", update.UpdateID)
			// The message should be marked as processed later in handleIncomingMessage
			return nil // Stop further processing in this chain
		}
		// --- End Backup Account Verification Check ---

		g.Log().Debugf(ctx, "Handling Update %d as a message.", update.UpdateID)
		return handleMessage(ctx, update.Message)
	} else if update.EditedMessage != nil {
		// Handle edited messages - treat them similarly to regular messages but with different logging
		g.Log().Debugf(ctx, "Handling Update %d as an edited message.", update.UpdateID)
		return handleMessage(ctx, update.EditedMessage)
	} else if update.CallbackQuery != nil {
		// Log callback query with keyboard content details
		keyboardInfo := ""
		if update.CallbackQuery.Message != nil {
			if update.CallbackQuery.Message.ReplyMarkup != nil {
				keyboardInfo = fmt.Sprintf(", keyboard_rows: %d", len(update.CallbackQuery.Message.ReplyMarkup.InlineKeyboard))
			} else {
				keyboardInfo = ", no_keyboard"
			}
		} else {
			keyboardInfo = ", no_message"
		}
		g.Log().Debugf(ctx, "Handling Update %d as a callback query with data: %s%s", update.UpdateID, update.CallbackQuery.Data, keyboardInfo)
		return handleCallbackQueryWrapper(ctx, update.CallbackQuery)
	} else if update.InlineQuery != nil {
		g.Log().Debugf(ctx, "Handling Update %d as an inline query.", update.UpdateID)
		return inline_query.HandleInlineQuery(ctx, update.InlineQuery) // Call the inline query handler
	} else if update.ChosenInlineResult != nil {
		g.Log().Debugf(ctx, "Handling Update %d as a chosen inline result.", update.UpdateID)
		// Call the handler for chosen inline results (to be created)
		return chosen_inline_result.HandleChosenInlineResult(ctx, update.ChosenInlineResult)
	} else if update.MyChatMember != nil {
		g.Log().Debugf(ctx, "Handling Update %d as a my_chat_member update.", update.UpdateID)
		// For now, just log it - actual handling will be added
		return nil
	} else if update.ChatMember != nil {
		g.Log().Debugf(ctx, "Handling Update %d as a chat_member update.", update.UpdateID)
		// For now, just log it - actual handling will be added
		return nil
	} else {
		g.Log().Debugf(ctx, "Update %d is not a message, callback query, inline query, chosen inline result, or chat member update, skipping.", update.UpdateID)
		return nil // Mark as processed, commit offset
	}
}

// handleMessage handles incoming messages (text, photo, etc.).
func handleMessage(ctx context.Context, message *tgbotapi.Message) error {
	// Log message type details for debugging
	messageType := "unknown"
	if message.Text != "" {
		messageType = "text"
	} else if message.Photo != nil && len(message.Photo) > 0 {
		messageType = "photo"
	} else if message.ChatShared != nil {
		messageType = "chat_shared"
		g.Log().Infof(ctx, "[GROUP_SELECT] Received ChatShared message - RequestID: %d, ChatID: %d",
			message.ChatShared.RequestID, message.ChatShared.ChatID)
	} else if message.Document != nil {
		messageType = "document"
	} else if message.Voice != nil {
		messageType = "voice"
	} else if message.Video != nil {
		messageType = "video"
	} else if message.Sticker != nil {
		messageType = "sticker"
	}

	g.Log().Debugf(ctx, "handleMessage: Processing message %d from user %d, type: %s",
		message.MessageID, message.From.ID, messageType)

	// --- Check for specific state handlers FIRST (e.g., waiting for photo) ---
	if message.From != nil { // Need sender ID to check state
		userState, stateErr := service.UserState().GetUserStateByTelegramId(ctx, message.From.ID)
		if stateErr != nil {
			g.Log().Warningf(ctx, "Failed to get user state while checking for specific handlers in handleMessage for Update %d: %v", message.MessageID, stateErr)
			// Continue processing even if state check fails? Or return error? Let's continue for now.
		} else if userState != nil {
			// Check if waiting for Red Packet Cover Photo
			// Use the correct state constant defined in model
			if userState.State == model.StateAwaitingCoverUpload && message.Photo != nil && len(message.Photo) > 0 {
				g.Log().Infof(ctx, "User %d is in state %s and sent a photo. Routing to HandleCoverUpload.", message.From.ID, userState.State)
				// Call the dedicated cover upload handler (to be created)
				err := cover.HandleCoverUpload(ctx, message) // Assuming HandleCoverUpload returns only error
				if err != nil {
					g.Log().Errorf(ctx, "Error from HandleCoverUpload for Update %d: %v", message.MessageID, err)
					// Send generic error reply
					replyMsg := model.InternalReplyMessage{
						ChatID: message.Chat.ID,
						Text:   service.I18n().T(ctx, "{#error_occurred}"),
					}
					_ = sendReplyToKafka(ctx, message.MessageID, replyMsg) // Ignore error
				}
				// Whether error or not, the message was intended for this state handler. Stop further processing.
				return nil // Mark as processed, commit offset
			} else if userState.State == "WITHDRAW_PROCESS" && message.Photo != nil && len(message.Photo) > 0 {
				// Handle photo upload for withdraw process
				g.Log().Infof(ctx, "User %d is in WITHDRAW_PROCESS state and sent a photo. Routing to withdraw handler.", message.From.ID)
				// Process through text handler which will route to withdraw message handler
				err := handleTextMessage(ctx, message)
				if err != nil {
					g.Log().Errorf(ctx, "Error handling photo in withdraw process for Update %d: %v", message.MessageID, err)
				}
				return nil // Mark as processed
			}
			// Add other 'else if' blocks here for other states that expect non-text input (e.g., documents, location)
		}
	}
	// --- End specific state handlers ---

	// If not handled by a specific state handler above, proceed with text/command checks
	if message.Text != "" {
		g.Log().Debugf(ctx, "Handling Update %d as a text message.", message.MessageID) // Use MessageID here
		return handleTextMessage(ctx, message)
	}

	// Handle ChatShared messages (when user selects a group using KeyboardButtonRequestChat)
	if message.ChatShared != nil {
		g.Log().Infof(ctx, "[GROUP_SELECT] Handling Update %d as a ChatShared message from user %d", message.MessageID, message.From.ID)
		// Route to text message handler which will process it through the message handlers
		return handleTextMessage(ctx, message)
	}

	// Handle other message types (photo, document, etc.) if needed in the future
	// For now, just log them. Commands might still be extracted from captions.

	// Check if it's a command even without text (e.g., /start)
	if message.IsCommand() {
		g.Log().Debugf(ctx, "Handling Update %d potentially as a command (no text body).", message.MessageID)
		// Attempt command handling even without text body
		cmdResponse, err := commands.HandleCommand(ctx, &tgbotapi.Update{Message: message}) // Wrap in Update for handler
		if err != nil {
			g.Log().Errorf(ctx, "Error handling command (no text body) for Update %d: %v", message.MessageID, err)
			// Fallback or error reporting if necessary
			replyMsg := model.InternalReplyMessage{
				ChatID: message.Chat.ID,
				Text:   service.I18n().T(ctx, "{#error_occurred}"), // Generic error
			}
			return sendReplyToKafka(ctx, message.MessageID, replyMsg) // Use MessageID
		}
		if cmdResponse != nil {
			replyMsg := model.InternalReplyMessage{
				ChatID:         cmdResponse.GetChatID(),
				Text:           cmdResponse.GetText(),
				ParseMode:      cmdResponse.GetParseMode(),
				InlineKeyboard: convertInlineKeyboardToModel(cmdResponse.GetReplyMarkup()),
			}
			return sendReplyToKafka(ctx, message.MessageID, replyMsg) // Use MessageID
		}
		// If command handler returns nil response and nil error, it means command not handled
		g.Log().Warningf(ctx, "Command '%s' (no text body) from Update %d was not handled.", message.Command(), message.MessageID)
		replyMsg := model.InternalReplyMessage{
			ChatID: message.Chat.ID,
			Text:   fmt.Sprintf("Unknown command: %s", message.Command()),
		}
		return sendReplyToKafka(ctx, message.MessageID, replyMsg) // Use MessageID
	}

	g.Log().Debugf(ctx, "Update %d (Message) is not a text message or handled command, skipping detailed processing.", message.MessageID)
	return nil // Mark as processed, commit offset
}
