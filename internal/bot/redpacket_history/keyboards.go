package redpacket_history

import (
	"context"
	"fmt"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"

	"telegram-bot-api/internal/model/callback" // Ensure callback model is imported
	"telegram-bot-api/internal/service"

	"github.com/a19ba14d/tg-bot-common/consts" // Add consts import
	"telegram-bot-api/internal/model/entity"
)

// buildSentHistoryKeyboard builds the keyboard for sent red packet history navigation.
// It now includes a share button for active packets.
// Uses callback.RedPacketHistoryData now.
func buildSentHistoryKeyboard(ctx context.Context, currentPage, totalPages int, status string, redPacketID int64, redPacketUUID string, isPremium int) (tgbotapi.InlineKeyboardMarkup, error) {
	i18n := service.I18n().Instance()
	rows := [][]tgbotapi.InlineKeyboardButton{}

	// Navigation Row (Prev/Next)
	navRow := []tgbotapi.InlineKeyboardButton{}
	if currentPage > 1 {
		prevData := &callback.RedPacketHistoryData{
			BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix}, // Use callback constants
			Action:           callback.RphActionSentPage,
			Status:           status,
			Page:             currentPage - 1,
		}
		navRow = append(navRow, tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#buttonPreviousPage}"), prevData.String()))
	}
	if currentPage < totalPages {
		nextData := &callback.RedPacketHistoryData{
			BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix}, // Use callback constants
			Action:           callback.RphActionSentPage,
			Status:           status,
			Page:             currentPage + 1,
		}
		navRow = append(navRow, tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#buttonNextPage}"), nextData.String()))
	}
	if len(navRow) > 0 {
		rows = append(rows, navRow)
	}

	// Action Buttons - Each button on its own row for better visibility

	// Add Cancel button row only if the packet is active
	if status == string(consts.RedPacketStatusActive) && redPacketID > 0 {
		// Cancel Button Row
		cancelConfirmData := &callback.RedPacketHistoryData{
			BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix},
			Action:           callback.RphActionCancelConfirm, // Use the new action
			RedPacketID:      redPacketID,
			Page:             currentPage, // Keep current page for context
			Status:           status,      // Keep status for context
		}
		cancelRow := tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#ButtonCancelRedPacket}"), cancelConfirmData.String()),
		)
		rows = append(rows, cancelRow)

		// Premium Toggle Button Row - only show if UUID is available
		if redPacketUUID != "" {
			if isPremium == 0 {
				// Add "Set as Premium" button
				setPremiumText := i18n.T(ctx, "{#SetAsPremiumRedPacket}")
				setPremiumData := fmt.Sprintf("rp_hist:set_premium:%s:%d:%s", redPacketUUID, currentPage, status)
				// Validate callback data length (Telegram limit is 64 bytes)
				if len(setPremiumData) <= 64 {
					setPremiumRow := tgbotapi.NewInlineKeyboardRow(
						tgbotapi.NewInlineKeyboardButtonData(setPremiumText, setPremiumData),
					)
					rows = append(rows, setPremiumRow)
				} else {
					g.Log().Warningf(ctx, "Set premium callback data too long (%d bytes): %s", len(setPremiumData), setPremiumData)
				}
			} else {
				// Add "Cancel Premium" button
				cancelPremiumText := i18n.T(ctx, "{#CancelPremiumRedPacket}")
				cancelPremiumData := fmt.Sprintf("rp_hist:cancel_premium:%s:%d:%s", redPacketUUID, currentPage, status)
				// Validate callback data length (Telegram limit is 64 bytes)
				if len(cancelPremiumData) <= 64 {
					cancelPremiumRow := tgbotapi.NewInlineKeyboardRow(
						tgbotapi.NewInlineKeyboardButtonData(cancelPremiumText, cancelPremiumData),
					)
					rows = append(rows, cancelPremiumRow)
				} else {
					g.Log().Warningf(ctx, "Cancel premium callback data too long (%d bytes): %s", len(cancelPremiumData), cancelPremiumData)
				}
			}
		}

		// Share Button Row (if UUID is available)
		if redPacketUUID != "" {
			shareQuery := fmt.Sprintf("%s%s ", consts.InlineQueryPrefixShareRedPacket, redPacketUUID)
			shareBtnText := i18n.T(ctx, "{#ShareRedPacketButton}")
			shareRow := tgbotapi.NewInlineKeyboardRow(
				tgbotapi.InlineKeyboardButton{Text: shareBtnText, SwitchInlineQuery: &shareQuery},
			)
			rows = append(rows, shareRow)
		}
	}

	// View Claims Button Row (always show if redPacketID > 0, regardless of status)
	if redPacketID > 0 {
		claimData := &callback.RedPacketHistoryData{
			BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix}, // Use callback constants
			Action:           callback.RphActionClaim,
			Status:           status, // Pass the original status filter
			RedPacketID:      redPacketID,
			Page:             1,           // Start claims view from page 1
			SentPage:         currentPage, // Store current sent page for back navigation
		}
		claimRow := tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#ButtonViewClaims}"), claimData.String()),
		)
		rows = append(rows, claimRow)
	}

	// Back Button Row
	backData := &callback.RedPacketHistoryData{
		BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix}, // Use callback constants
		Action:           callback.RphActionBackMenu,
		Page:             1, // Page doesn't matter for back to menu
	}
	backRow := tgbotapi.NewInlineKeyboardRow(
		tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#ButtonBackToMenu}"), backData.String()),
	)
	rows = append(rows, backRow)

	// Note: Back button is always added above, so rows should never be empty

	keyboard := tgbotapi.NewInlineKeyboardMarkup(rows...)
	return keyboard, nil
}

// buildClaimHistoryKeyboard builds the keyboard for claim history navigation.
// It now requires the original 'status' from the sent view to build the back button correctly.
// Uses callback.RedPacketHistoryData now.
func buildClaimHistoryKeyboard(ctx context.Context, currentPage, totalPages int, redPacketID int64, sentPage int, status string) (tgbotapi.InlineKeyboardMarkup, error) {
	i18n := service.I18n().Instance()
	rows := [][]tgbotapi.InlineKeyboardButton{}

	// Navigation Row (Prev/Next)
	navRow := []tgbotapi.InlineKeyboardButton{}
	if currentPage > 1 {
		prevData := &callback.RedPacketHistoryData{
			BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix}, // Use callback constants
			Action:           callback.RphActionClaimPage,
			RedPacketID:      redPacketID,
			Page:             currentPage - 1,
			SentPage:         sentPage,
		}
		navRow = append(navRow, tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#ButtonPreviousPage}"), prevData.String()))
	}
	if currentPage < totalPages {
		nextData := &callback.RedPacketHistoryData{
			BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix}, // Use callback constants
			Action:           callback.RphActionClaimPage,
			RedPacketID:      redPacketID,
			Page:             currentPage + 1,
			SentPage:         sentPage,
		}
		navRow = append(navRow, tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#ButtonNextPage}"), nextData.String()))
	}
	if len(navRow) > 0 {
		rows = append(rows, navRow)
	}

	// Action Row (Back)
	backData := &callback.RedPacketHistoryData{
		BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix}, // Use callback constants
		Action:           callback.RphActionBackSent,
		Status:           status,      // Include the original status filter
		RedPacketID:      redPacketID, // Keep RedPacketID for context if needed, though SentPage is primary
		Page:             1,           // Page here might not be relevant, SentPage is key
		SentPage:         sentPage,
	}
	actionRow := tgbotapi.NewInlineKeyboardRow(
		tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#ButtonBackToSent}"), backData.String()),
	)
	rows = append(rows, actionRow)

	keyboard := tgbotapi.NewInlineKeyboardMarkup(rows...)
	return keyboard, nil
}

// buildNoRecordsKeyboard builds a simple keyboard with only a back button.
// Uses callback.RedPacketHistoryData now.
func buildNoRecordsKeyboard(ctx context.Context, backAction string, redPacketID int64, sentPage int) (tgbotapi.InlineKeyboardMarkup, error) {
	i18n := service.I18n().Instance()
	var backButtonText string
	var backCallbackData string

	if backAction == callback.RphActionBackSent { // Use callback constants
		backButtonText = i18n.T(ctx, "{#ButtonBackToSent}")
		data := &callback.RedPacketHistoryData{
			BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix}, // Use callback constants
			Action:           callback.RphActionBackSent,
			RedPacketID:      redPacketID,
			Page:             1,
			SentPage:         sentPage,
		}
		backCallbackData = data.String()
	} else { // Default to back to menu (ActionBackMenu)
		backButtonText = i18n.T(ctx, "{#ButtonBackToMenu}")
		data := &callback.RedPacketHistoryData{
			BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix}, // Use callback constants
			Action:           callback.RphActionBackMenu,
			Page:             1,
		}
		backCallbackData = data.String()
	}

	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(backButtonText, backCallbackData),
		),
	)
	return keyboard, nil
}

// buildSentHistoryListKeyboard builds the keyboard for the list view of red packets
func buildSentHistoryListKeyboard(ctx context.Context, packets []*entity.RedPackets, currentPage, totalPages int, status string) (tgbotapi.InlineKeyboardMarkup, error) {
	i18n := service.I18n().Instance()
	rows := [][]tgbotapi.InlineKeyboardButton{}

	// Add a button for each red packet in the list
	for _, packet := range packets {
		// Format time: MM-DD HH:MM
		timeStr := packet.CreatedAt

		// Build button text based on status
		var buttonText string
		if packet.Status == string(consts.RedPacketStatusActive) {
			// Active: show remaining/total
			buttonText = fmt.Sprintf("🧧 [%s] %d/%d - %s %s",
				timeStr,
				packet.RemainingQuantity,
				packet.Quantity,
				packet.TotalAmount.String(),
				packet.Symbol,
			)
		} else {
			// Ended: show "over"
			buttonText = fmt.Sprintf("🧧 [%s] %d/%d - %s %s (over)",
				timeStr,
				packet.RemainingQuantity,
				packet.Quantity,
				packet.TotalAmount.String(),
				packet.Symbol,
			)
		}

		// Create callback data to show details with page and status info
		callbackData := fmt.Sprintf("rp:details:%s:%d:%s", packet.Uuid, currentPage, status)

		// Add button to its own row
		rows = append(rows, tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(buttonText, callbackData),
		))
	}

	// Add pagination row if needed
	if totalPages > 1 {
		navRow := []tgbotapi.InlineKeyboardButton{}

		// Previous page button
		if currentPage > 1 {
			prevData := &callback.RedPacketHistoryData{
				BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix},
				Action:           callback.RphActionSentPage,
				Status:           status,
				Page:             currentPage - 1,
			}
			navRow = append(navRow, tgbotapi.NewInlineKeyboardButtonData("⬅️ "+i18n.T(ctx, "上一页"), prevData.String()))
		}

		// Page indicator
		pageText := fmt.Sprintf("%d/%d", currentPage, totalPages)
		navRow = append(navRow, tgbotapi.NewInlineKeyboardButtonData(pageText, "noop"))

		// Next page button
		if currentPage < totalPages {
			nextData := &callback.RedPacketHistoryData{
				BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix},
				Action:           callback.RphActionSentPage,
				Status:           status,
				Page:             currentPage + 1,
			}
			navRow = append(navRow, tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "下一页")+" ➡️", nextData.String()))
		}

		rows = append(rows, navRow)
	}

	// Add back button
	backData := &callback.RedPacketHistoryData{
		BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix},
		Action:           callback.RphActionBackMenu,
		Page:             1,
	}
	rows = append(rows, tgbotapi.NewInlineKeyboardRow(
		tgbotapi.NewInlineKeyboardButtonData("🔙 "+i18n.T(ctx, "返回"), backData.String()),
	))

	return tgbotapi.NewInlineKeyboardMarkup(rows...), nil
}
