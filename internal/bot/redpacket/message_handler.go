package redpacket

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"           // Added fmt import
	"io"            // Added io import
	"net/http"      // Added net/http import
	"path/filepath" // Added path/filepath import
	"strconv"
	"strings" // Added strings import
	"time"    // Added time import

	// "github.com/a19ba14d/tg-bot-common/codes" // Unused
	config "telegram-bot-api/internal/config"
	"telegram-bot-api/internal/dao"
	"telegram-bot-api/internal/middleware"
	"telegram-bot-api/internal/model"
	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/utility"

	// "github.com/goforj/godump"
	"github.com/yalks/wallet" // Added wallet import

	localConsts "telegram-bot-api/internal/consts"

	"github.com/a19ba14d/tg-bot-common/consts"

	// "telegram-bot-api/internal/model" // Unused in this specific handler logic yet
	"telegram-bot-api/internal/service"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/errors/gerror" // Added gerror import
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx" // Added gctx import
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/google/uuid"

	// "github.com/gogf/gf/v2/util/gconv" // Unused
	"github.com/shopspring/decimal"
)

// getBotFromContext is a helper function to get bot instance with tenant ID from context
func getBotFromContext(ctx context.Context) (*tgbotapi.BotAPI, error) {
	tenantId, ok := middleware.GetTenantIdFromContext(ctx)
	if !ok {
		return nil, gerror.New("tenant ID not found in context")
	}

	bot, err := service.TenantBotManager().GetBot(ctx, tenantId)
	if err != nil {
		return nil, gerror.Wrapf(err, "failed to get bot for tenant %d", tenantId)
	}

	return bot, nil
}

// HandleRedPacketMessage handles incoming messages when the user is expected to provide red packet details.
func HandleRedPacketMessage(ctx context.Context, message *tgbotapi.Message) (handled bool, err error) {
	if message == nil || message.From == nil {
		return false, nil // Ignore invalid messages
	}

	telegramID := message.From.ID
	chatID := message.Chat.ID
	i18n := service.I18n().Instance()

	// Check if this is a ChatShared message (group selection)
	if message.ChatShared != nil {
		return handleChatSharedMessage(ctx, message)
	}

	// 1. Get User State
	userState, err := service.UserState().GetUserStateByTelegramId(ctx, telegramID)
	if err != nil {
		g.Log().Errorf(ctx, "Error getting user state for user %d in HandleRedPacketMessage: %v", telegramID, err)
		// Optionally send a generic error message? For now, let main handler decide.
		return false, err // Propagate error
	}

	// 2. Handle based on User State
	if userState == nil {
		return false, nil // No state, not handled here
	}

	switch userState.State {
	case consts.UserStateWaitingRedPacketQuantity:
		// --- Handle Quantity Input ---
		// 3. Get dynamic limits from config
		minNum, err := config.GetInt(ctx, "red_packet_setting.min_num", 1)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get min_num from config: %v", err)
			minNum = 1 // 使用默认值
		}

		maxNum, err := config.GetInt(ctx, "red_packet_setting.max_num", consts.RedPacketMaxQuantity)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get max_num from config: %v", err)
			maxNum = consts.RedPacketMaxQuantity // 使用默认值
		}

		// 4. Validate Input (expecting a positive integer for quantity, within configured range)
		quantity, err := strconv.Atoi(message.Text)
		isInvalid := err != nil || quantity < minNum || quantity > maxNum

		// 5. Handle Invalid Input
		if isInvalid {
			userState.RetryCount++
			errMsgText := ""

			if userState.RetryCount > userState.MaxRetries {
				// Max retries exceeded
				errMsgText = i18n.T(ctx, "{#TooManyAttempts}")
				errClear := service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
				if errClear != nil {
					g.Log().Warningf(ctx, "Failed to clear user state after max retries for user %d: %v", telegramID, errClear)
				}
			} else {
				// Still have retries left - provide specific error message
				if err != nil {
					// Not a valid integer
					errMsgText = i18n.Tf(ctx, "{#InvalidRedPacketQuantity}", minNum, maxNum)
				} else if quantity < minNum {
					// Below minimum limit
					errMsgText = i18n.Tf(ctx, "{#RedPacketQuantityBelowMinimum}", minNum)
				} else if quantity > maxNum {
					// Exceeds maximum limit
					errMsgText = i18n.Tf(ctx, "{#RedPacketQuantityExceedsLimit}", maxNum)
				} else {
					// Fallback
					errMsgText = i18n.Tf(ctx, "{#InvalidRedPacketQuantity}", minNum, maxNum)
				}
				// Update state in Redis (retry count and reset TTL)
				userState.ResetTTL() // Reset TTL on invalid attempt
				errSet := service.UserState().SetUserStateByTelegramId(ctx, telegramID, userState)
				if errSet != nil {
					g.Log().Errorf(ctx, "Failed to update user state after invalid quantity input for user %d: %v", telegramID, errSet)
					errMsgText = i18n.T(ctx, "{#SystemError}")
					_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
				}
			}

			// Send reply message indicating the error
			errMsg := tgbotapi.NewMessage(chatID, errMsgText)
			errMsg.ReplyToMessageID = message.MessageID
			bot, botErr := getBotFromContext(ctx)
			if botErr != nil {
				g.Log().Errorf(ctx, "Failed to get bot instance to send invalid quantity error: %v", botErr)
			} else {
				_, sendErr := bot.Send(errMsg)
				if sendErr != nil {
					g.Log().Errorf(ctx, "Failed to send invalid quantity error message: %v", sendErr)
				}
			}
			return true, nil // Indicate message was handled (by showing error)
		}

		// 5. Handle Valid Input
		g.Log().Infof(ctx, "User %d provided valid red packet quantity: %d", telegramID, quantity)

		// 5.1 Prepare for the next state (Waiting for Amount)
		tokenSymbol, okSymbol := userState.Context[consts.RpContextKeyTokenSymbol] // Use symbol key
		rpType, okType := userState.Context[consts.RpContextKeyType]

		if !okSymbol || !okType { // Check symbol
			g.Log().Errorf(ctx, "Missing token symbol or type in user state context for user %d", telegramID)
			_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
			errMsg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "{#SystemError}"))
			errMsg.ReplyToMessageID = message.MessageID
			bot, botErr := getBotFromContext(ctx)
			if botErr != nil {
				g.Log().Errorf(ctx, "Failed to get bot instance to send missing context error: %v", botErr)
			} else {
				_, sendErr := bot.Send(errMsg)
				if sendErr != nil {
					g.Log().Errorf(ctx, "Failed to send missing context error message: %v", sendErr)
				}
			}
			return true, nil // Handled by sending error
		}

		// 5.2 Create context for the next state (map[string]string)
		nextContext := map[string]string{
			consts.RpContextKeyTokenSymbol: tokenSymbol, // Pass symbol along
			consts.RpContextKeyType:        rpType,
			consts.RpContextKeyQuantity:    strconv.Itoa(quantity), // Convert quantity to string
		}

		// 5.3 Define the next state
		nextState := model.UserState{
			State:             consts.UserStateWaitingRedPacketAmount,
			Context:           nextContext,
			ExpectedInputType: model.InputTypeText, // We'll parse decimal manually
			TTL:               300,                 // 5 minutes
			MaxRetries:        3,
			RetryCount:        0,                         // Reset retry count for the new state
			MessageIDToEdit:   userState.MessageIDToEdit, // Preserve the message ID to edit
		}

		// 5.4 Set the new user state
		errSet := service.UserState().SetUserStateByTelegramId(ctx, telegramID, &nextState)
		if errSet != nil {
			g.Log().Errorf(ctx, "Failed to set UserStateWaitingRedPacketAmount for user %d: %v", telegramID, errSet)
			errMsg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "{#SetUserStateError}"))
			errMsg.ReplyToMessageID = message.MessageID
			bot, botErr := getBotFromContext(ctx)
			if botErr != nil {
				g.Log().Errorf(ctx, "Failed to get bot instance to send SetUserState error: %v", botErr)
			} else {
				_, sendErr := bot.Send(errMsg)
				if sendErr != nil {
					g.Log().Errorf(ctx, "Failed to send SetUserState error message: %v", sendErr)
				}
			}
			_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
			return true, nil // Handled by sending error
		}

		// 5.5 Delete user's input message and edit original message to show amount prompt
		bot, botErr := getBotFromContext(ctx)
		if botErr != nil {
			g.Log().Errorf(ctx, "Failed to get bot instance: %v", botErr)
			return true, nil
		}

		// Delete user's input message
		deleteMsg := tgbotapi.NewDeleteMessage(chatID, message.MessageID)
		_, deleteErr := bot.Send(deleteMsg)
		if deleteErr != nil {
			g.Log().Warningf(ctx, "Failed to delete user input message %d: %v", message.MessageID, deleteErr)
		}

		// Prepare amount prompt text based on type
		promptText := ""
		if rpType == string(consts.RedPacketTypeRandom) {
			promptText = i18n.Tf(ctx, "{#EnterRedPacketTotalAmount}", tokenSymbol)
		} else if rpType == string(consts.RedPacketTypeFixed) {
			promptText = i18n.Tf(ctx, "{#EnterRedPacketSingleAmount}", tokenSymbol)
		} else {
			g.Log().Errorf(ctx, "Invalid red packet type '%s' encountered when prompting for amount for user %d", rpType, telegramID)
			_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
			errMsg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "{#SystemError}"))
			_, _ = bot.Send(errMsg)
			return true, nil
		}

		// Create keyboard with back button only
		keyboard := tgbotapi.NewInlineKeyboardMarkup(
			tgbotapi.NewInlineKeyboardRow(
				tgbotapi.NewInlineKeyboardButtonData(
					"↩️ "+i18n.T(ctx, "{#BackButton}"),
					fmt.Sprintf("rp:back_quantity:%s:%s", tokenSymbol, rpType),
				),
			),
		)

		// Edit the original message to show amount prompt
		if userState.MessageIDToEdit > 0 {
			editMsg := tgbotapi.NewEditMessageText(chatID, int(userState.MessageIDToEdit), promptText)
			editMsg.ReplyMarkup = &keyboard
			_, editErr := bot.Send(editMsg)
			if editErr != nil {
				g.Log().Errorf(ctx, "Failed to edit message %d to show amount prompt: %v", userState.MessageIDToEdit, editErr)
				// Fallback: send new message
				promptMsg := tgbotapi.NewMessage(chatID, promptText)
				promptMsg.ReplyMarkup = keyboard
				_, _ = bot.Send(promptMsg)
			}
		} else {
			// Fallback: send new message if no message ID to edit
			promptMsg := tgbotapi.NewMessage(chatID, promptText)
			promptMsg.ReplyMarkup = keyboard
			_, _ = bot.Send(promptMsg)
		}
		return true, nil // Handled quantity state

	case consts.UserStateWaitingRedPacketAmount:
		// --- Handle Amount Input ---
		// 3. Get context
		tokenSymbol, okSymbol := userState.Context[consts.RpContextKeyTokenSymbol] // Use symbol key
		rpType, okType := userState.Context[consts.RpContextKeyType]
		quantityStr, okQuantity := userState.Context[consts.RpContextKeyQuantity]

		if !okSymbol || !okType || !okQuantity { // Check symbol
			g.Log().Errorf(ctx, "Missing context in UserStateWaitingRedPacketAmount for user %d", telegramID)
			_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
			return true, nil
		}

		quantity, err := strconv.Atoi(quantityStr)
		if err != nil {
			g.Log().Errorf(ctx, "Invalid quantity '%s' in context for UserStateWaitingRedPacketAmount for user %d", quantityStr, telegramID)
			_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
			return true, nil
		}

		// Check if amount already exists in context - if yes, this is a duplicate amount input
		existingAmount, hasAmount := userState.Context[consts.RpContextKeyAmount]
		if hasAmount && existingAmount != "" {
			// User has already entered amount, this is a duplicate input
			g.Log().Infof(ctx, "User %d entered duplicate amount in amount state.", telegramID)
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#InvalidOperationOrExpired}"))
			return true, nil
		}

		// 4. Get Token Info (including Decimals) by Symbol
		tokenInfo, err := service.Token().GetTokenBySymbol(ctx, tokenSymbol) // Use GetTokenBySymbol
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get token info for symbol %s: %v", tokenSymbol, err)
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}")) // Consider {#GetTokenInfoError}
			return true, nil
		}
		if tokenInfo == nil { // Should not happen if GetTokenBySymbol returns nil error, but check anyway
			g.Log().Errorf(ctx, "Token info is nil for symbol %s", tokenSymbol)
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
			return true, nil
		}
		// tokenSymbol is already available from context

		// 5. Amount Parsing and Validation
		amountDecimal, err := decimal.NewFromString(message.Text)
		if err != nil {
			errorMsg := i18n.T(ctx, "{#InvalidAmountFormat}")
			return handleInvalidAmountInput(ctx, userState, telegramID, chatID, message.MessageID, errorMsg)
		}
		// Use comprehensive validation to check amount validity (including decimal places)
		isValid, errorKey := utility.ValidateAmountDecimal(ctx, amountDecimal, tokenSymbol)
		if !isValid {
			g.Log().Errorf(ctx, "Invalid amount in ChosenInlineResult query: %s %s, error: %s", amountDecimal.String(), tokenSymbol, errorKey)
			return handleInvalidAmountInput(ctx, userState, telegramID, chatID, message.MessageID, errorKey) // Non-retryable validation error
		}
		// 5.2 Positive Check
		if !amountDecimal.IsPositive() {
			errorMsg := i18n.T(ctx, "{#AmountMustBePositive}")
			return handleInvalidAmountInput(ctx, userState, telegramID, chatID, message.MessageID, errorMsg)
		}

		// 6. Minimum Amount Check
		minAmountMap, err := config.GetMap(ctx, "red_packet_setting.min_single_amount")
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get min_single_amount from config: %v", err)
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
			return true, nil
		}

		var minThreshold decimal.Decimal
		if minAmountMap != nil && minAmountMap[tokenSymbol] != nil {
			minThreshold, err = decimal.NewFromString(minAmountMap[tokenSymbol].(string))
			if err != nil {
				g.Log().Errorf(ctx, "Failed to parse min_single_amount for %s: %v", tokenSymbol, err)
				minThreshold = decimal.NewFromFloat(0.001) // 使用默认值
			}
		} else {
			minThreshold = decimal.NewFromFloat(0.001) // 使用默认值
			g.Log().Warningf(ctx, "No min_single_amount config found for token %s, using default: %s", tokenSymbol, minThreshold.String())
		}

		var minAmountPerPacket decimal.Decimal
		if rpType == string(consts.RedPacketTypeFixed) {
			minAmountPerPacket = amountDecimal // For fixed, the input is the amount per packet
		} else { // Random
			quantityDecimal := decimal.NewFromInt(int64(quantity))
			if quantityDecimal.IsZero() { // Avoid division by zero, though quantity should be > 0 from previous step
				g.Log().Errorf(ctx, "Quantity is zero in UserStateWaitingRedPacketAmount for user %d", telegramID)
				_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
				sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
				return true, nil
			}
			// For random, check average amount, assuming total amount is input
			minAmountPerPacket = amountDecimal.Div(quantityDecimal)
		}

		if minAmountPerPacket.LessThan(minThreshold) {
			// 使用国际化格式化消息，注入最小金额
			errorMsg := i18n.Tf(ctx, "{#RedPacketMinAmountError}", minThreshold.String())
			return handleInvalidAmountInput(ctx, userState, telegramID, chatID, message.MessageID, errorMsg)
		}

		// 校验最大金额
		maxAmountMap, err := config.GetMap(ctx, "red_packet_setting.max_single_amount")
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get max_single_amount from config: %v", err)
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
			return true, nil
		}

		var maxThreshold decimal.Decimal
		if maxAmountMap != nil && maxAmountMap[tokenSymbol] != nil {
			maxThreshold, err = decimal.NewFromString(maxAmountMap[tokenSymbol].(string))
			if err != nil {
				g.Log().Errorf(ctx, "Failed to parse max_single_amount for %s: %v", tokenSymbol, err)
				maxThreshold = decimal.NewFromFloat(100000.0) // 使用默认值
			}
		} else {
			maxThreshold = decimal.NewFromFloat(100000.0) // 使用默认值
			g.Log().Warningf(ctx, "No max_single_amount config found for token %s, using default: %s", tokenSymbol, maxThreshold.String())
		}

		var maxAmountPerPacket decimal.Decimal
		if rpType == string(consts.RedPacketTypeFixed) {
			maxAmountPerPacket = amountDecimal // For fixed, the input is the amount per packet
		} else { // Random
			quantityDecimal := decimal.NewFromInt(int64(quantity))
			if quantityDecimal.IsZero() { // Avoid division by zero, though quantity should be > 0 from previous step
				g.Log().Errorf(ctx, "Quantity is zero in UserStateWaitingRedPacketAmount for user %d", telegramID)
				_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
				sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
				return true, nil
			}
			maxAmountPerPacket = amountDecimal.Div(quantityDecimal)
		}

		if maxAmountPerPacket.GreaterThan(maxThreshold) {
			// 使用国际化格式化消息，注入最大金额
			errorMsg := i18n.Tf(ctx, "{#RedPacketMaxAmountError}", maxThreshold.String())
			return handleInvalidAmountInput(ctx, userState, telegramID, chatID, message.MessageID, errorMsg)
		}

		// 7. Balance Check
		// Get user's database User ID first
		user, userErr := service.User().GetUserByTelegramId(ctx, telegramID)
		if userErr != nil {
			g.Log().Errorf(ctx, "Failed to get user %d for balance check: %v", telegramID, userErr)
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
			return true, nil
		}
		userDbID := user.Id

		balanceInfo, err := wallet.Manager().GetBalance(ctx, userDbID, tokenSymbol)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get user %d balance for token %s: %v", telegramID, tokenSymbol, err)
			// Don't increment retry count for system errors like this
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#GetBalanceError}"))
			return true, nil
		}

		// Use the available balance directly from wallet module (it's already in display format)
		displayBalanceDecimal := balanceInfo.AvailableBalance

		// For fixed amount, the total cost is amount * quantity
		totalAmountToCheck := amountDecimal
		if rpType == string(consts.RedPacketTypeFixed) {
			quantityDecimal := decimal.NewFromInt(int64(quantity))
			totalAmountToCheck = amountDecimal.Mul(quantityDecimal)
		}

		if totalAmountToCheck.GreaterThan(displayBalanceDecimal) {
			errorMsg := i18n.T(ctx, "{#InsufficientBalance}")
			return handleInvalidAmountInput(ctx, userState, telegramID, chatID, message.MessageID, errorMsg)
		}

		// 8. Handle Valid Amount
		g.Log().Infof(ctx, "User %d provided valid red packet amount: %s %s (Type: %s, Qty: %d)",
			telegramID, amountDecimal.String(), tokenSymbol, rpType, quantity)

		// 8.1 Create red packet record immediately with IsPay=0 (unpaid)
		redPacketID, err := createUnpaidRedPacket(ctx, telegramID, tokenSymbol, rpType, quantity, amountDecimal, userState.Context)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to create unpaid red packet for user %d: %v", telegramID, err)
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#CreateRedPacketFailed}"))
			_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
			return true, nil
		}

		// 8.2 Update user state with red packet ID and move to payment confirmation
		userState.Context[consts.RpContextKeyAmount] = amountDecimal.String() // Store amount in context
		userState.Context[consts.RpContextKeyBlessing] = ""                   // Set empty blessing (will use default)
		userState.Context[consts.RpContextKeyChatID] = gconv.String(chatID)   // Store ChatID for later use
		userState.Context["red_packet_id"] = gconv.String(redPacketID)        // Store red packet ID
		userState.Context["red_packet_uuid"] = ""                             // Will be filled when we get the record

		// 8.3 Set user state to payment confirmation
		newState := model.NewUserState(localConsts.UserStateWaitingRedPacketPaymentConfirm, userState.Context, "", 0, 600) // 10 minutes TTL
		newState.MessageIDToEdit = int64(message.MessageID)                                                                // Store message ID to edit
		errSet := service.UserState().SetUserStateByTelegramId(ctx, telegramID, newState)
		if errSet != nil {
			g.Log().Errorf(ctx, "Failed to update user state to payment confirmation for user %d: %v", telegramID, errSet)
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SetUserStateError}"))
			_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID) // Attempt to clear if update fails
			return true, nil                                                    // Handled by sending error
		}

		g.Log().Infof(ctx, "User %d provided amount, moving to payment confirmation", telegramID)

		// 8.3 Delete user's input message and edit original message to show payment confirmation
		bot, botErr := getBotFromContext(ctx)
		if botErr != nil {
			g.Log().Errorf(ctx, "Failed to get bot instance: %v", botErr)
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
			return true, nil
		}

		// Show payment confirmation page using unified approach
		err = ShowPaymentConfirmationPageFromAmount(ctx, bot, chatID, message.MessageID, redPacketID, telegramID)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to show payment confirmation page: %v", err)
			sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
			return true, nil
		}

		return true, nil // Handled amount state, moved to payment confirmation

	case consts.UserStateWaitingRedPacketBlessing:
		// This state should no longer be used since we skip blessing step
		// Clear the state and redirect to main menu
		g.Log().Warningf(ctx, "User %d in deprecated blessing state, clearing and redirecting to main menu", telegramID)
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#InvalidOperationOrExpired}"))
		return true, nil

	// +++ Add Case for Cover Upload +++
	case consts.UserStateWaitingRedPacketCover:
		return handleRedPacketCoverUpload(ctx, message, userState) // Call the new handler

	case localConsts.UserStateWaitingRedPacketMemo:
		return handleRedPacketMemoInput(ctx, message, userState)

	case localConsts.UserStateWaitingBettingAmount:
		return handleBettingAmountInput(ctx, message, userState)

	case localConsts.UserStateWaitingRedPacketPaymentConfirm:
		// User is in payment confirmation state but sent a message
		// This should not clear the state, just ignore the message
		g.Log().Debugf(ctx, "User %d sent message '%s' while in payment confirmation state, ignoring", telegramID, message.Text)
		return true, nil // Mark as handled to prevent state clearing

	default:
		// Unknown or unhandled state for this specific handler
		g.Log().Debugf(ctx, "HandleRedPacketMessage received unhandled state '%s' for user %d", userState.State, telegramID)
		return false, nil
	}
}

// handleRedPacketCoverUpload handles the message when the user is expected to upload a red packet cover image.
func handleRedPacketCoverUpload(ctx context.Context, message *tgbotapi.Message, _ *model.UserState) (handled bool, err error) {
	telegramID := message.From.ID
	chatID := message.Chat.ID
	i18n := service.I18n().Instance()

	// 1. Check if the message contains a photo
	if len(message.Photo) == 0 {
		g.Log().Infof(ctx, "User %d sent non-photo message while in UserStateWaitingRedPacketCover state.", telegramID)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#PleaseSendPhoto}"))
		// Keep the user state, maybe increment retry count if needed?
		// userState.RetryCount++
		// userState.ResetTTL()
		// service.UserState().SetUserStateByTelegramId(ctx, telegramID, userState)
		return true, nil // Handled by sending error message
	}

	// 2. Get User ID from database (needed for saving the image record)
	user, err := service.User().GetUserByTelegramId(ctx, telegramID)
	if err != nil {
		g.Log().Errorf(ctx, "handleRedPacketCoverUpload: Failed to get user %d: %v", telegramID, err)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID) // Clear state on error
		return true, err                                                    // Return error to potentially log higher up
	}
	if user == nil {
		g.Log().Warningf(ctx, "handleRedPacketCoverUpload: User not found for telegram ID %d", telegramID)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#UserNotFound}"))
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID) // Clear state
		return true, nil
	}
	userId := user.Id

	// 3. Extract File ID (use the largest photo for better quality if needed, but API usually handles this)
	// Let's use the last one (usually largest) for potential download quality
	photo := message.Photo[len(message.Photo)-1]
	fileId := photo.FileID
	fileUniqueId := photo.FileUniqueID // Useful for generating unique object names

	g.Log().Infof(ctx, "User %d (%d) uploaded cover photo. FileID: %s, UniqueID: %s, Size: %d",
		telegramID, userId, fileId, fileUniqueId, photo.FileSize)

	// --- MinIO Upload Logic ---
	// 4. Get File URL from Telegram
	bot, err := getBotFromContext(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "handleRedPacketCoverUpload: Failed to get bot instance for user %d: %v", userId, err)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
		return true, err
	}
	fileURL, err := bot.GetFileDirectURL(fileId)
	if err != nil {
		g.Log().Errorf(ctx, "handleRedPacketCoverUpload: Failed to get file direct URL for file %s, user %d: %v", fileId, userId, err)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#FailedToGetFileInfo}"))
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
		return true, err
	}

	// 5. Download the file content
	// Use a background context for the HTTP request to avoid canceling if the original context times out
	bgCtx := gctx.New()
	resp, err := http.Get(fileURL) // Consider adding timeout to http client
	if err != nil {
		g.Log().Errorf(ctx, "handleRedPacketCoverUpload: Failed to download file from URL %s, user %d: %v", fileURL, userId, err)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#FailedToDownloadImage}"))
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
		return true, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		g.Log().Errorf(ctx, "handleRedPacketCoverUpload: Failed to download file, status code %d, body: %s, user %d", resp.StatusCode, string(bodyBytes), userId)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#FailedToDownloadImage}"))
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
		return true, gerror.Newf("download failed with status %d", resp.StatusCode)
	}

	// 6. Upload to MinIO
	// Bucket name seems to be handled internally by the service/library.
	// Generate object name (e.g., user_id/unique_id_timestamp.ext)
	fileExt := filepath.Ext(fileURL) // Try to get extension from URL, might need improvement
	if fileExt == "" {
		// Guess extension based on content type? Or default?
		contentType := resp.Header.Get("Content-Type")
		if strings.Contains(contentType, "jpeg") || strings.Contains(contentType, "jpg") {
			fileExt = ".jpg"
		} else if strings.Contains(contentType, "png") {
			fileExt = ".png"
		} else {
			fileExt = ".jpg" // Default or handle error
			g.Log().Warningf(ctx, "Could not determine file extension for content type '%s', defaulting to .jpg", contentType)
		}
	}
	objectName := fmt.Sprintf("%d/%s_%d%s", userId, fileUniqueId, time.Now().UnixNano(), fileExt)
	contentType := resp.Header.Get("Content-Type") // Get content type from response header
	fileSize := resp.ContentLength                 // Get size from response header (-1 if unknown)

	// Use the Minio service
	minioService := service.Storage() // Assuming service.Minio() returns the MinIO service instance
	// Use the correct UploadObject method. Bucket name seems to be handled internally by the service/library.
	imageUrl, err := minioService.UploadObject(bgCtx, objectName, resp.Body, fileSize, contentType)
	if err != nil {
		g.Log().Errorf(ctx, "handleRedPacketCoverUpload: Failed to upload image to MinIO (object: %s) for user %d: %v", objectName, userId, err) // Added objectName to log
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#FailedToUploadImage}"))
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
		return true, err
	}
	g.Log().Infof(ctx, "User %d uploaded cover to MinIO: %s", userId, imageUrl)

	// --- End MinIO Upload Logic ---

	// 7. Save image info to database
	g.Log().Debugf(ctx, "[RP_COVER_UPLOAD] Attempting to add image record for user %d, fileId: %s, imageUrl: %s", userId, fileId, imageUrl)
	err = service.RedPacketImage().AddImage(ctx, userId, imageUrl, fileId, string(consts.RedPacketImageStatusPendingReview))
	if err != nil {
		g.Log().Errorf(ctx, "[RP_COVER_UPLOAD] Failed to add image record to DB for user %d: %v", userId, err) // Enhanced log
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#FailedToSaveImageInfo}"))
		// Should we try to delete from MinIO here? Maybe not, could be temporary DB issue.
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID) // Attempt to clear state even on DB error
		return true, err
	}
	g.Log().Infof(ctx, "[RP_COVER_UPLOAD] Successfully added image record for user %d, fileId: %s", userId, fileId)

	// 8. Clear user state
	g.Log().Debugf(ctx, "[RP_COVER_UPLOAD] Attempting to clear user state for user %d", telegramID)
	err = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
	if err != nil {
		// Log the error but proceed with sending success message, as the core task succeeded.
		g.Log().Warningf(ctx, "[RP_COVER_UPLOAD] Failed to clear user state for user %d after successful upload: %v", telegramID, err) // Enhanced log
	} else {
		g.Log().Infof(ctx, "[RP_COVER_UPLOAD] Successfully cleared user state for user %d", telegramID)
	}

	// 9. Send success message
	successMsgText := i18n.T(ctx, "{#RedPacketCoverUploadedPendingReview}")
	successMsg := tgbotapi.NewMessage(chatID, successMsgText)
	successMsg.ReplyToMessageID = message.MessageID
	g.Log().Debugf(ctx, "[RP_COVER_UPLOAD] Attempting to send success message to user %d, chat %d", telegramID, chatID)
	_, sendErr := bot.Send(successMsg)
	if sendErr != nil {
		g.Log().Errorf(ctx, "[RP_COVER_UPLOAD] Failed to send success message for user %d: %v", telegramID, sendErr) // Enhanced log
	} else {
		g.Log().Infof(ctx, "[RP_COVER_UPLOAD] Successfully sent success message to user %d", telegramID)
	}

	// 10. Send the first page of the cover list as a new message
	// errSendPage := SendCoverPageMessage(ctx, bot, chatID, int64(userId), 1) // TODO: Fix or replace this call to show cover list after upload
	// if errSendPage != nil {
	// 	// Log the error, but don't block the overall success of the upload
	// 	g.Log().Errorf(ctx, "[RP_COVER_UPLOAD] Failed to send cover page message after upload for user %d: %v", userId, errSendPage)
	// }

	g.Log().Infof(ctx, "[RP_COVER_UPLOAD] Completed handling cover upload for user %d.", telegramID)
	return true, nil // Successfully handled the cover upload
}

// handleInvalidAmountInput is a helper to manage invalid amount inputs, update state, and send feedback.
// It requires telegramID to update the state correctly.
// It returns (true, nil) indicating the message was handled by sending an error/retry prompt.
func handleInvalidAmountInput(ctx context.Context, userState *model.UserState, telegramID int64, chatID int64, replyToMsgID int, errorMessage string) (bool, error) {
	i18n := service.I18n().Instance()

	userState.RetryCount++
	errMsgText := ""

	if userState.RetryCount > userState.MaxRetries {
		errMsgText = i18n.T(ctx, "{#TooManyAttempts}")
		errClear := service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
		if errClear != nil {
			g.Log().Warningf(ctx, "Failed to clear user state after max amount retries for user %d: %v", telegramID, errClear)
		}
	} else {
		errMsgText = errorMessage // 直接使用已经国际化的错误消息
		userState.ResetTTL()
		errSet := service.UserState().SetUserStateByTelegramId(ctx, telegramID, userState)
		if errSet != nil {
			g.Log().Errorf(ctx, "Failed to update user state after invalid amount input for user %d: %v", telegramID, errSet)
			errMsgText = i18n.T(ctx, "{#SetUserStateError}") // Overwrite with system error
			_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
		}
	}

	sendErrorMessage(ctx, chatID, replyToMsgID, errMsgText)
	return true, nil
}

// sendErrorMessage is a helper to send an error message, simplifying the main logic.
func sendErrorMessage(ctx context.Context, chatID int64, replyToMsgID int, text string) {
	errMsg := tgbotapi.NewMessage(chatID, text)
	if replyToMsgID != 0 {
		errMsg.ReplyToMessageID = replyToMsgID
	}
	bot, botErr := getBotFromContext(ctx)
	if botErr != nil {
		g.Log().Errorf(ctx, "Failed to get bot instance to send error message: %v", botErr)
	} else {
		_, sendErr := bot.Send(errMsg)
		if sendErr != nil {
			g.Log().Errorf(ctx, "Failed to send error message: %v", sendErr)
		}
	}
}

// createUnpaidRedPacket creates a red packet record with IsPay=0 (unpaid status)
func createUnpaidRedPacket(ctx context.Context, telegramID int64, tokenSymbol, rpType string, quantity int, amount decimal.Decimal, context map[string]string) (int64, error) {
	// Get user information
	user, err := service.User().GetUserByTelegramId(ctx, telegramID)
	if err != nil || user == nil {
		return 0, gerror.Newf("Failed to get user for telegram ID %d: %v", telegramID, err)
	}

	// Get token information
	token, err := service.Token().GetTokenBySymbol(ctx, tokenSymbol)
	if err != nil || token == nil {
		return 0, gerror.Newf("Failed to get token for symbol %s: %v", tokenSymbol, err)
	}

	// Calculate total amount based on type
	totalAmount := amount
	if rpType == string(consts.RedPacketTypeFixed) {
		// For fixed type, amount is per packet, so total = amount * quantity
		quantityDecimal := decimal.NewFromInt(int64(quantity))
		totalAmount = amount.Mul(quantityDecimal)
	}

	// Generate UUID for red packet
	originalUUID := uuid.NewString()
	hash := md5.Sum([]byte(originalUUID))
	redPacketUUID := hex.EncodeToString(hash[:])[:16] // Use first 16 characters of MD5

	// Ensure UUID uniqueness
	for {
		exists, err := dao.RedPackets.Ctx(ctx).Where("uuid", redPacketUUID).Count()
		if err != nil {
			return 0, gerror.Wrap(err, "failed to check UUID uniqueness")
		}
		if exists == 0 {
			break
		}
		// If collision, generate new UUID
		originalUUID = uuid.NewString()
		hash = md5.Sum([]byte(originalUUID))
		redPacketUUID = hex.EncodeToString(hash[:])[:16]
	}

	// Handle blessing - default to empty string
	blessing := context[consts.RpContextKeyBlessing]
	// No default blessing - keep it empty if not provided

	// Set expiration time
	expireMinutes := g.Cfg().MustGet(ctx, "timeouts.redPacketExpirationMinutes", 1440).Int()
	expireDuration := time.Duration(expireMinutes) * time.Minute
	now := gtime.Now()
	// Get telegram username from user_backup_accounts table
	var backupAccount *entity.UserBackupAccounts
	err = dao.UserBackupAccounts.Ctx(ctx).
		Where(dao.UserBackupAccounts.Columns().UserId, user.Id).
		Where(dao.UserBackupAccounts.Columns().IsMaster, 1).
		Scan(&backupAccount)

	creatorUsername := user.Account // Default fallback
	if err == nil && backupAccount != nil && backupAccount.TelegramUsername != "" {
		creatorUsername = backupAccount.TelegramUsername
	}

	// Create red packet record with IsPay=0
	redPacket := entity.RedPackets{
		TenantId:          user.TenantId,
		Uuid:              redPacketUUID,
		CreatorUserId:     telegramID,
		CreatorUsername:   creatorUsername,
		TokenId:           int(token.TokenId),
		Symbol:            tokenSymbol,
		Type:              rpType,
		RedPacketType:     localConsts.RedPacketTypePrivate, // Set red packet type for private chat red packets
		Quantity:          quantity,
		TotalAmount:       totalAmount,
		RemainingAmount:   totalAmount,
		RemainingQuantity: quantity,
		Memo:              blessing,
		Status:            string(consts.RedPacketStatusActive),
		CreatedAt:         now,
		ExpiresAt:         now.Add(expireDuration),
		SenderUserId:      uint64(user.Id),
		IsPay:             0,  // Unpaid status
		MessageId:         "", // Will be set later when message is sent
	}

	// Insert the record, manually excluding only the DrawStatus field for normal red packets
	data := g.Map{
		"tenant_id":             redPacket.TenantId,
		"uuid":                  redPacket.Uuid,
		"red_packet_images_id":  redPacket.RedPacketImagesId, // Include even if 0
		"creator_user_id":       redPacket.CreatorUserId,
		"creator_username":      redPacket.CreatorUsername,
		"cover_file_id":         redPacket.CoverFileId,
		"thumb_url":             redPacket.ThumbUrl,
		"token_id":              redPacket.TokenId,
		"total_amount":          redPacket.TotalAmount,
		"quantity":              redPacket.Quantity,
		"remaining_amount":      redPacket.RemainingAmount,
		"remaining_quantity":    redPacket.RemainingQuantity,
		"type":                  redPacket.Type,
		"red_packet_type":       redPacket.RedPacketType,
		"chat_id":               redPacket.ChatId,
		"group_message_id":      redPacket.GroupMessageId,
		"memo":                  redPacket.Memo,
		"status":                redPacket.Status,
		"created_at":            redPacket.CreatedAt,
		"expires_at":            redPacket.ExpiresAt,
		"sender_user_id":        redPacket.SenderUserId,
		"symbol":                redPacket.Symbol,
		"transaction_id":        redPacket.TransactionId,
		"message_id":            redPacket.MessageId,
		"is_pay":                redPacket.IsPay,
		"betting_volume":        redPacket.BettingVolume,
		"betting_volume_days":   redPacket.BettingVolumeDays,
		"group_title":           redPacket.GroupTitle,
		"group_id":              redPacket.GroupId,
		"group_invitation_link": redPacket.GroupInvitationLink,
		"specify_group":         redPacket.SpecifyGroup,
		"specify_betting":       redPacket.SpecifyBetting,
		"is_premium":            redPacket.IsPremium,
		"is_verification_code":  redPacket.IsVerificationCode,
		// Note: DrawStatus and DrawTime are intentionally omitted for normal red packets
		// They will be NULL in the database, which is correct for non-lucky red packets
	}

	result, err := dao.RedPackets.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "failed to create unpaid red packet record")
	}

	redPacketID, err := result.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "failed to get red packet ID")
	}

	g.Log().Infof(ctx, "Created unpaid red packet ID %d with UUID %s for user %d", redPacketID, redPacketUUID, telegramID)
	return redPacketID, nil
}

// handleRedPacketMemoInput handles user input for red packet memo
func handleRedPacketMemoInput(ctx context.Context, message *tgbotapi.Message, userState *model.UserState) (bool, error) {
	telegramID := message.From.ID
	chatID := message.Chat.ID
	i18n := service.I18n().Instance()

	// Validate text input
	if message.Text == "" {
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "请输入文字内容"))
		return true, nil
	}

	// Check memo length (150 characters max)
	if len([]rune(message.Text)) > 150 {
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "备注信息不能超过150字"))
		return true, nil
	}

	// Get red packet UUID from state context
	redPacketUUID, ok := userState.Context["red_packet_uuid"]
	if !ok || redPacketUUID == "" {
		g.Log().Errorf(ctx, "handleRedPacketMemoInput: No red packet UUID in state for user %d", telegramID)
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}"))
		return true, nil
	}

	// Update red packet memo in database
	_, err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", telegramID).
		Data(g.Map{"memo": message.Text}).
		Update()

	if err != nil {
		g.Log().Errorf(ctx, "handleRedPacketMemoInput: Failed to update memo for red packet %s: %v", redPacketUUID, err)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
		return true, nil
	}

	// Clear user state
	_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)

	// Delete user's input message
	bot, botErr := getBotFromContext(ctx)
	if botErr != nil {
		g.Log().Errorf(ctx, "Failed to get bot instance: %v", botErr)
		return true, nil
	}

	deleteMsg := tgbotapi.NewDeleteMessage(chatID, message.MessageID)
	_, _ = bot.Send(deleteMsg)

	// Show success notification
	successMsg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "✅ 备注设置成功"))
	_, _ = bot.Send(successMsg)

	// Get red packet record to show details page
	var redPacket *entity.RedPackets
	err = dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", telegramID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "handleRedPacketMemoInput: Failed to get red packet %s: %v", redPacketUUID, err)
		return true, nil
	}

	// Build and send details page
	detailsText := BuildRedPacketDetailsText(ctx, redPacket)
	detailsKeyboard := BuildRedPacketDetailsKeyboard(ctx, redPacket.Uuid)

	if redPacket.CoverFileId != "" {
		// Send photo with details
		photoConfig := tgbotapi.NewPhoto(chatID, tgbotapi.FileID(redPacket.CoverFileId))
		photoConfig.Caption = detailsText
		photoConfig.ParseMode = "HTML"
		photoConfig.ReplyMarkup = detailsKeyboard
		_, _ = bot.Send(photoConfig)
	} else {
		// Send text message
		msg := tgbotapi.NewMessage(chatID, detailsText)
		msg.ParseMode = "HTML"
		msg.ReplyMarkup = detailsKeyboard
		_, _ = bot.Send(msg)
	}

	return true, nil
}

// handleBettingAmountInput handles the betting amount input from users
func handleBettingAmountInput(ctx context.Context, message *tgbotapi.Message, userState *model.UserState) (handled bool, err error) {
	telegramID := message.From.ID
	chatID := message.Chat.ID
	i18n := service.I18n().Instance()

	// Extract red packet UUID from user state
	redPacketUUID, ok := userState.Context["red_packet_uuid"]
	if !ok || redPacketUUID == "" {
		g.Log().Errorf(ctx, "handleBettingAmountInput: No red packet UUID found in user state for user %d", telegramID)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#ErrorStateExpiredOrInvalid}"))
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
		return true, nil
	}

	// Parse betting amount
	amountStr := strings.TrimSpace(message.Text)
	amount, err := decimal.NewFromString(amountStr)
	if err != nil {
		g.Log().Infof(ctx, "handleBettingAmountInput: Invalid amount format '%s' from user %d: %v", amountStr, telegramID, err)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#InvalidAmountFormat}"))
		// Keep user state for retry
		return true, nil
	}

	// Validate amount (must be positive)
	if amount.LessThanOrEqual(decimal.Zero) {
		g.Log().Infof(ctx, "handleBettingAmountInput: Invalid amount %s from user %d (must be positive)", amount.String(), telegramID)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#AmountMustBePositive}"))
		// Keep user state for retry
		return true, nil
	}

	// Update red packet with betting amount and specify_betting flag
	_, err = dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", telegramID).
		Data(g.Map{
			"betting_volume":  amount,
			"specify_betting": 1,
		}).
		Update()

	if err != nil {
		g.Log().Errorf(ctx, "handleBettingAmountInput: Failed to update betting volume for red packet %s: %v", redPacketUUID, err)
		sendErrorMessage(ctx, chatID, message.MessageID, i18n.T(ctx, "{#SystemError}"))
		return true, nil
	}

	// Clear user state
	_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)

	// Delete user's input message
	bot, botErr := getBotFromContext(ctx)
	if botErr != nil {
		g.Log().Errorf(ctx, "Failed to get bot instance: %v", botErr)
		return true, nil
	}

	deleteMsg := tgbotapi.NewDeleteMessage(chatID, message.MessageID)
	_, _ = bot.Send(deleteMsg)

	// Show success notification
	successMsg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "✅ 流水金额设置成功"))
	_, _ = bot.Send(successMsg)

	// Get red packet record to show betting settings page
	var redPacket *entity.RedPackets
	err = dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", telegramID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "handleBettingAmountInput: Failed to get red packet %s: %v", redPacketUUID, err)
		return true, nil
	}

	// Build and send conditions page
	text := buildRedPacketConditionsText(ctx, redPacket)
	keyboard := buildRedPacketConditionsKeyboard(ctx, redPacket)

	msg := tgbotapi.NewMessage(chatID, text)
	msg.ParseMode = "HTML"
	msg.ReplyMarkup = keyboard
	_, _ = bot.Send(msg)

	return true, nil
}

// handleChatSharedMessage handles ChatShared messages when users select a group using KeyboardButtonRequestChat
func handleChatSharedMessage(ctx context.Context, message *tgbotapi.Message) (handled bool, err error) {
	telegramID := message.From.ID
	chatID := message.Chat.ID
	i18n := service.I18n().Instance()

	// Log incoming message details
	g.Log().Infof(ctx, "[GROUP_SELECT] Received message from user %d in chat %d, checking for ChatShared content", telegramID, chatID)

	if message.ChatShared == nil {
		//g.Log().Debugf(ctx, "[GROUP_SELECT] Message from user %d does not contain ChatShared, ignoring", telegramID)
		return false, nil
	}

	chatShared := message.ChatShared
	g.Log().Infof(ctx, "[GROUP_SELECT] User %d shared chat - RequestID: %d, SharedChatID: %d",
		telegramID, chatShared.RequestID, chatShared.ChatID)

	// Log detailed chat shared information including Title and Username
	var titleStr, usernameStr string
	if chatShared.Title != nil {
		titleStr = *chatShared.Title
	} else {
		titleStr = "<nil>"
	}
	if chatShared.Username != nil {
		usernameStr = *chatShared.Username
	} else {
		usernameStr = "<nil>"
	}

	g.Log().Debugf(ctx, "[GROUP_SELECT] ChatShared Details - UserID: %d, RequestID: %d, ChatID: %d, Title: %s, Username: %s, MessageID: %d",
		telegramID, chatShared.RequestID, chatShared.ChatID, titleStr, usernameStr, message.MessageID)

	// godump.Dump(message)

	// Get bot instance
	//g.Log().Debugf(ctx, "[GROUP_SELECT] Getting bot instance for tenant")
	bot, err := getBotFromContext(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get bot instance: %v", err)
		return true, nil
	}
	//g.Log().Debugf(ctx, "[GROUP_SELECT] Successfully got bot instance")

	// Remove the reply keyboard first
	removeKeyboard := tgbotapi.NewRemoveKeyboard(true)

	// Get tenant ID from context
	tenantId, ok := middleware.GetTenantIdFromContext(ctx)
	if !ok {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get tenant ID from context")
		msg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "{#SystemError}"))
		bot.Send(msg)
		return true, nil
	}

	// Check if bot is in the group and has admin rights
	g.Log().Infof(ctx, "[GROUP_SELECT] Checking group %d for tenant %d", chatShared.ChatID, tenantId)
	var telegramGroup *entity.TelegramGroups
	err = dao.TelegramGroups.Ctx(ctx).
		Where("chat_id", chatShared.ChatID).
		Where("tenant_id", tenantId).
		Where("bot_status", "active").
		Scan(&telegramGroup)

	if err != nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to query telegram_groups for chat %d: %v", chatShared.ChatID, err)
		msg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "{#SystemError}"))
		bot.Send(msg)
		return true, nil
	}

	if telegramGroup == nil {
		g.Log().Warningf(ctx, "[GROUP_SELECT] Bot is not in group %d or group not found", chatShared.ChatID)
		msg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "❌ 机器人未加入该群组，请先将机器人添加到群组"))
		bot.Send(msg)
		return true, nil
	}

	// Check if group type is supergroup
	if telegramGroup.Type != "supergroup" {
		g.Log().Warningf(ctx, "[GROUP_SELECT] Group %d is not a supergroup, type: %s", chatShared.ChatID, telegramGroup.Type)
		msg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "❌ 只支持超级群组，请选择超级群组"))
		bot.Send(msg)
		return true, nil
	}

	// Validate bot admin rights via Telegram API and update database
	isAdmin, err := validateAndUpdateBotAdminRights(ctx, bot, chatShared.ChatID)
	if err != nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to validate bot admin rights for chat %d: %v", chatShared.ChatID, err)
		// Continue with database value if API call fails
		if telegramGroup.BotIsAdmin != 1 {
			g.Log().Warningf(ctx, "[GROUP_SELECT] Bot does not have admin rights in group %d (from database)", chatShared.ChatID)
			msg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "❌ 机器人在该群组中没有管理员权限，请先设置机器人为管理员"))
			bot.Send(msg)
			return true, nil
		}
	} else if !isAdmin {
		g.Log().Warningf(ctx, "[GROUP_SELECT] Bot does not have admin rights in group %d (verified via API)", chatShared.ChatID)
		msg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "❌ 机器人在该群组中没有管理员权限，请先设置机器人为管理员"))
		bot.Send(msg)
		return true, nil
	}

	// Update group information if title or username has changed
	needUpdate := false
	updateData := g.Map{}

	// Check and update title if changed
	if chatShared.Title != nil && *chatShared.Title != telegramGroup.Title {
		g.Log().Infof(ctx, "[GROUP_SELECT] Group %d title changed from '%s' to '%s'",
			chatShared.ChatID, telegramGroup.Title, *chatShared.Title)
		updateData["title"] = *chatShared.Title
		needUpdate = true
	}

	// Check and update username if changed
	if chatShared.Username != nil && *chatShared.Username != telegramGroup.Username {
		g.Log().Infof(ctx, "[GROUP_SELECT] Group %d username changed from '%s' to '%s'",
			chatShared.ChatID, telegramGroup.Username, *chatShared.Username)
		updateData["username"] = *chatShared.Username
		needUpdate = true
	}

	// Perform update if needed
	if needUpdate {
		_, err = dao.TelegramGroups.Ctx(ctx).
			Where("chat_id", chatShared.ChatID).
			Where("tenant_id", tenantId).
			Data(updateData).
			Update()

		if err != nil {
			g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to update telegram_groups for chat %d: %v", chatShared.ChatID, err)
			// Continue with the process even if update fails
		} else {
			g.Log().Infof(ctx, "[GROUP_SELECT] Successfully updated telegram_groups for chat %d", chatShared.ChatID)
		}
	}

	// Get red packet UUID from Redis state
	stateKey := fmt.Sprintf("group_select_state:%d", telegramID)
	redPacketUUID, err := g.Redis().Get(ctx, stateKey)
	if err != nil || redPacketUUID.IsNil() || redPacketUUID.IsEmpty() {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get red packet UUID from Redis for user %d: %v", telegramID, err)
		msg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "❌ 会话已过期，请重新操作"))
		bot.Send(msg)
		return true, nil
	}

	// Generate invitation link
	var inviteLink string
	if telegramGroup.Username != "" {
		inviteLink = fmt.Sprintf("https://t.me/%s", telegramGroup.Username)
		g.Log().Infof(ctx, "[GROUP_SELECT] Generated invitation link for group %d: %s", chatShared.ChatID, inviteLink)
	} else {
		g.Log().Warningf(ctx, "[GROUP_SELECT] Group %d has no username, cannot generate invitation link", chatShared.ChatID)
	}

	// Update red packet with group_id, specify_group flag, group_title and invitation link
	g.Log().Infof(ctx, "[GROUP_SELECT] Updating red packet %s with group_id %d, title %s", redPacketUUID.String(), chatShared.ChatID, telegramGroup.Title)
	redPacketUpdateData := g.Map{
		"group_id":      gconv.String(chatShared.ChatID),
		"specify_group": 1,
		"group_title":   telegramGroup.Title,
	}

	// Add invitation link if available
	if inviteLink != "" {
		redPacketUpdateData["group_invitation_link"] = inviteLink
	}

	_, err = dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID.String()).
		Where("creator_user_id", telegramID).
		Data(redPacketUpdateData).
		Update()

	if err != nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to update red packet %s with group info: %v", redPacketUUID.String(), err)
		msg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "{#SystemError}"))
		bot.Send(msg)
		return true, nil
	}

	// Clear the Redis state
	g.Redis().Del(ctx, stateKey)

	// Get the updated red packet record
	var redPacket *entity.RedPackets
	err = dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID.String()).
		Where("creator_user_id", telegramID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get red packet %s after update: %v", redPacketUUID.String(), err)
		msg := tgbotapi.NewMessage(chatID, i18n.T(ctx, "{#SystemError}"))
		bot.Send(msg)
		return true, nil
	}

	// Send success message and show the group selection page again
	successMsg := tgbotapi.NewMessage(chatID, fmt.Sprintf("✅ %s", i18n.T(ctx, "群组设置成功")))
	successMsg.ReplyMarkup = removeKeyboard
	bot.Send(successMsg)

	// Build group selection page with selected group info
	text := fmt.Sprintf(
		"👥 <b>%s</b>\n\n%s: %s",
		i18n.T(ctx, "指定群组红包"),
		i18n.T(ctx, "群组"),
		telegramGroup.Title,
	)

	// Create keyboard with options
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"📤 "+i18n.T(ctx, "选择群组"),
				fmt.Sprintf("rp:show_group_selection:%s", redPacketUUID.String()),
			),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"🔗 "+i18n.T(ctx, "生成邀请链接"),
				fmt.Sprintf("rp:generate_invite:%s", redPacketUUID.String()),
			),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"↩️ "+i18n.T(ctx, "返回"),
				fmt.Sprintf("rp:set_conditions:%s", redPacketUUID.String()),
			),
		),
	)

	// Send the group selection page
	msg := tgbotapi.NewMessage(chatID, text)
	msg.ParseMode = "HTML"
	msg.ReplyMarkup = keyboard
	_, err = bot.Send(msg)
	if err != nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to send group selection page to user %d: %v", telegramID, err)
	}

	if inviteLink != "" {
		g.Log().Infof(ctx, "[GROUP_SELECT] Successfully updated red packet %s with group %s (ID: %d) and invitation link: %s",
			redPacketUUID.String(), telegramGroup.Title, chatShared.ChatID, inviteLink)
	} else {
		g.Log().Infof(ctx, "[GROUP_SELECT] Successfully updated red packet %s with group %s (ID: %d), no invitation link generated",
			redPacketUUID.String(), telegramGroup.Title, chatShared.ChatID)
	}
	return true, nil
}
