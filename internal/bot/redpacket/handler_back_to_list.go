package redpacket

import (
	"context"
	"fmt"

	"telegram-bot-api/internal/model/callback"
	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/service"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/a19ba14d/tg-bot-common/consts"
)

// handleBackToRedPacketList handles returning to the red packet list at the specified page
func handleBackToRedPacketList(ctx context.Context, cq *tgbotapi.CallbackQuery, page int, status string) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Get the list data using the service
	pageSize := 5
	var statusFilter []string
	if status == "active" {
		statusFilter = []string{"active"}
	} else if status == "expired_empty" {
		statusFilter = []string{"expired", "empty", "cancelled"}
	} else if status == "all" {
		statusFilter = []string{"active", "expired", "empty", "cancelled"}
	}

	packets, total, err := service.RedPacket().GetSentRedPacketsPage(ctx, userID, statusFilter, page, pageSize)
	if err != nil {
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), err
	}

	// Calculate total pages
	totalPages := (total + pageSize - 1) / pageSize

	// Handle no records found
	if total == 0 || len(packets) == 0 {
		// Build no records message
		var noRecordTextKey string
		if status == "active" {
			noRecordTextKey = "{#NoActiveRedPackets}"
		} else if status == "expired_empty" {
			noRecordTextKey = "{#NoEndedRedPackets}"
		} else {
			noRecordTextKey = "{#NoSentRedPackets}"
		}

		text := i18n.T(ctx, noRecordTextKey)
		keyboard := tgbotapi.NewInlineKeyboardMarkup(
			tgbotapi.NewInlineKeyboardRow(
				tgbotapi.NewInlineKeyboardButtonData(
					"🔙 "+i18n.T(ctx, "返回"),
					"rp_hist:back_menu:1",
				),
			),
		)

		resp := callback.NewDeleteAndSendResponse(chatID, messageID, text, &keyboard)
		resp.CallbackQueryID = cq.ID
		resp.ParseMode = tgbotapi.ModeHTML
		return resp, nil
	}

	// Build list view text and keyboard
	text := buildRedPacketListTitle(ctx, status)
	keyboard, err := buildRedPacketListKeyboard(ctx, packets, page, totalPages, status)
	if err != nil {
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), err
	}

	// Delete the current message and send a new text message
	// This is necessary because the current message might be a photo,
	// and we can't edit a photo message to become a text message
	resp := callback.NewDeleteAndSendResponse(chatID, messageID, text, &keyboard)
	resp.CallbackQueryID = cq.ID
	resp.ParseMode = tgbotapi.ModeHTML
	return resp, nil
}

// buildRedPacketListTitle creates the title text for the list view based on status
func buildRedPacketListTitle(ctx context.Context, status string) string {
	i18n := service.I18n().Instance()

	switch status {
	case "active":
		return "🧧 " + i18n.T(ctx, "进行中")
	case "expired_empty":
		return "🧧 " + i18n.T(ctx, "已结束")
	case "all":
		return "🧧 " + i18n.T(ctx, "全部")
	default:
		return "🧧 " + i18n.T(ctx, "红包记录")
	}
}

// buildRedPacketListKeyboard builds the keyboard for the list view of red packets
func buildRedPacketListKeyboard(ctx context.Context, packets []*entity.RedPackets, currentPage, totalPages int, status string) (tgbotapi.InlineKeyboardMarkup, error) {
	i18n := service.I18n().Instance()
	rows := [][]tgbotapi.InlineKeyboardButton{}

	// Add a button for each red packet in the list
	for _, packet := range packets {
		// Format time: MM-DD HH:MM
		timeStr := packet.CreatedAt

		// Build button text based on status
		var buttonText string
		if packet.Status == string(consts.RedPacketStatusActive) {
			// Active: show remaining/total
			buttonText = fmt.Sprintf("🧧 [%s] %d/%d - %s %s",
				timeStr,
				packet.RemainingQuantity,
				packet.Quantity,
				packet.TotalAmount.String(),
				packet.Symbol,
			)
		} else {
			// Ended: show "over"
			buttonText = fmt.Sprintf("🧧 [%s] %d/%d - %s %s (over)",
				timeStr,
				packet.RemainingQuantity,
				packet.Quantity,
				packet.TotalAmount.String(),
				packet.Symbol,
			)
		}

		// Create callback data to show details with page and status info
		callbackData := fmt.Sprintf("rp:details:%s:%d:%s", packet.Uuid, currentPage, status)

		// Add button to its own row
		rows = append(rows, tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(buttonText, callbackData),
		))
	}

	// Add pagination row if needed
	if totalPages > 1 {
		navRow := []tgbotapi.InlineKeyboardButton{}

		// Previous page button
		if currentPage > 1 {
			prevData := &callback.RedPacketHistoryData{
				BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix},
				Action:           callback.RphActionSentPage,
				Status:           status,
				Page:             currentPage - 1,
			}
			navRow = append(navRow, tgbotapi.NewInlineKeyboardButtonData("⬅️ "+i18n.T(ctx, "上一页"), prevData.String()))
		}

		// Page indicator
		pageText := fmt.Sprintf("%d/%d", currentPage, totalPages)
		navRow = append(navRow, tgbotapi.NewInlineKeyboardButtonData(pageText, "noop"))

		// Next page button
		if currentPage < totalPages {
			nextData := &callback.RedPacketHistoryData{
				BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix},
				Action:           callback.RphActionSentPage,
				Status:           status,
				Page:             currentPage + 1,
			}
			navRow = append(navRow, tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "下一页")+" ➡️", nextData.String()))
		}

		rows = append(rows, navRow)
	}

	// Add back button
	backData := &callback.RedPacketHistoryData{
		BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix},
		Action:           callback.RphActionBackMenu,
		Page:             1,
	}
	rows = append(rows, tgbotapi.NewInlineKeyboardRow(
		tgbotapi.NewInlineKeyboardButtonData("🔙 "+i18n.T(ctx, "返回"), backData.String()),
	))

	return tgbotapi.NewInlineKeyboardMarkup(rows...), nil
}
