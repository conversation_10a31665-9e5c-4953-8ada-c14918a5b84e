package redpacket

import (
	"context"
	"telegram-bot-api/internal/dao"
	"telegram-bot-api/internal/middleware"
	
	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"
)

// validateAndUpdateBotAdminRights validates bot admin rights via Telegram API and updates database
func validateAndUpdateBotAdminRights(ctx context.Context, bot *tgbotapi.BotAPI, chatID int64) (bool, error) {
	g.Log().Infof(ctx, "[GROUP_SELECT] Validating bot admin rights for chat %d via Telegram API", chatID)
	
	// Get bot's own ID
	botUser := bot.Self
	if botUser.ID == 0 {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get bot user info")
		return false, nil
	}
	
	// Call Telegram API to get bot's member status in the chat
	chatMemberConfig := tgbotapi.GetChatMemberConfig{
		ChatConfigWithUser: tgbotapi.ChatConfigWithUser{
			ChatID: chatID,
			UserID: botUser.ID,
		},
	}
	
	member, err := bot.GetChatMember(chatMemberConfig)
	if err != nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get chat member info for bot in chat %d: %v", chatID, err)
		return false, err
	}
	
	// Check if bot is admin
	isAdmin := member.Status == "administrator" || member.Status == "creator"
	
	g.Log().Infof(ctx, "[GROUP_SELECT] Bot status in chat %d: %s, IsAdmin: %v", chatID, member.Status, isAdmin)
	
	// Get tenant ID from context
	tenantId, ok := middleware.GetTenantIdFromContext(ctx)
	if !ok {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get tenant ID from context")
		return isAdmin, nil
	}
	
	// Update database with actual bot permissions
	updateData := g.Map{
		"bot_is_admin": 0,
		"bot_can_manage_chat": 0,
		"bot_can_delete_messages": 0,
		"bot_can_manage_video_chats": 0,
		"bot_can_restrict_members": 0,
		"bot_can_promote_members": 0,
		"bot_can_change_info": 0,
		"bot_can_invite_users": 0,
		"bot_can_pin_messages": 0,
		"bot_can_manage_topics": 0,
	}
	
	if isAdmin {
		updateData["bot_is_admin"] = 1
		
		// Extract specific permissions if bot is admin
		if member.CanManageChat {
			updateData["bot_can_manage_chat"] = 1
		}
		if member.CanDeleteMessages {
			updateData["bot_can_delete_messages"] = 1
		}
		if member.CanManageVideoChats {
			updateData["bot_can_manage_video_chats"] = 1
		}
		if member.CanRestrictMembers {
			updateData["bot_can_restrict_members"] = 1
		}
		if member.CanPromoteMembers {
			updateData["bot_can_promote_members"] = 1
		}
		if member.CanChangeInfo {
			updateData["bot_can_change_info"] = 1
		}
		if member.CanInviteUsers {
			updateData["bot_can_invite_users"] = 1
		}
		if member.CanPinMessages {
			updateData["bot_can_pin_messages"] = 1
		}
		// Note: CanManageTopics might not exist in the current version of tgbotapi
		// Commenting it out for now
		// if member.CanManageTopics {
		// 	updateData["bot_can_manage_topics"] = 1
		// }
	}
	
	// Update database
	_, err = dao.TelegramGroups.Ctx(ctx).
		Where("chat_id", chatID).
		Where("tenant_id", tenantId).
		Data(updateData).
		Update()
	
	if err != nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to update bot permissions in database for chat %d: %v", chatID, err)
		// Don't return error, just log it
	} else {
		g.Log().Infof(ctx, "[GROUP_SELECT] Successfully updated bot permissions in database for chat %d, IsAdmin: %v", chatID, isAdmin)
	}
	
	return isAdmin, nil
}