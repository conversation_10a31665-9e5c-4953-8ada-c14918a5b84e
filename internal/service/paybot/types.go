package paybot

import (
	"encoding/json"
	"time"

	"github.com/shopspring/decimal"
)

// PayBotTime 自定义时间类型，处理PayBot API返回的时间格式
type PayBotTime struct {
	time.Time
}

// UnmarshalJSON 自定义JSON反序列化，处理PayBot的时间格式
// func (pt *PayBotTime) UnmarshalJSON(data []byte) error {
// 	var timeStr string
// 	if err := json.Unmarshal(data, &timeStr); err != nil {
// 		return err
// 	}

// 	// PayBot API返回的时间格式: "2025-07-10 16:48:51"
// 	parsedTime, err := time.Parse("2006-01-02 15:04:05", timeStr)
// 	if err != nil {
// 		return err
// 	}

// 	pt.Time = parsedTime
// 	return nil
// }

// MarshalJSON 自定义JSON序列化
func (pt PayBotTime) MarshalJSON() ([]byte, error) {
	return json.Marshal(pt.Time.String())
}

// 授权支付相关结构

// CreateAuthPaymentRequest 创建授权支付请求
type CreateAuthPaymentRequest struct {
	UserID           uint64          `json:"-"`                          // 内部数据库用户ID（不传给API）
	UserAccount      string          `json:"userAccount"`                // 用户账户（users.account）
	OrderType        string          `json:"orderType"`                  // 订单类型: add/deduct
	TokenSymbol      string          `json:"tokenSymbol"`                // 代币符号
	Amount           decimal.Decimal `json:"amount"`                     // 金额
	AuthReason       string          `json:"authReason"`                 // 授权原因
	MerchantOrderNo  string          `json:"merchantOrderNo"`            // 商户订单号
	ExpireMinutes    int             `json:"expireMinutes"`              // 过期时间(分钟)
	CallbackUrl      string          `json:"callbackUrl"`                // 回调URL
	CallbackRobotUrl string          `json:"callbackRobotUrl,omitempty"` // 回调机器人URL
}

// AuthPaymentDataResponse 授权支付嵌套数据响应结构
type AuthPaymentDataResponse struct {
	Code    int               `json:"code"`
	Message string            `json:"message"`
	Data    AuthPaymentResult `json:"data"`
}

// AuthPaymentResult 授权支付结果
type AuthPaymentResult struct {
	OrderNo          string          `json:"orderNo"`          // PayBot订单号
	MerchantOrderNo  string          `json:"merchantOrderNo"`  // 商户订单号
	UserAccount      string          `json:"userAccount"`      // 用户账户
	OrderType        string          `json:"orderType"`        // 订单类型
	TokenSymbol      string          `json:"tokenSymbol"`      // 代币符号
	Amount           decimal.Decimal `json:"amount"`           // 金额
	AuthReason       string          `json:"authReason"`       // 授权原因
	Status           string          `json:"status"`           // 状态
	CallbackStatus   string          `json:"callbackStatus"`   // 回调状态
	ExpireAt         *time.Time      `json:"expireAt"`         // 过期时间
	CompletedAt      *time.Time      `json:"completedAt"`      // 完成时间
	ErrorMessage     string          `json:"errorMessage"`     // 错误信息
	CreatedAt        time.Time       `json:"createdAt"`        // 创建时间
	CallbackRobotUrl string          `json:"callbackRobotUrl"` // 回调机器人链接
}

// QueryAuthPaymentRequest 查询授权支付请求
type QueryAuthPaymentRequest struct {
	OrderNo         string `json:"orderNo"`         // 系统订单号
	MerchantOrderNo string `json:"merchantOrderNo"` // 商户订单号
}

// ListAuthPaymentsRequest 列出授权支付请求
type ListAuthPaymentsRequest struct {
	Page        int    `json:"page"`        // 页码
	PageSize    int    `json:"pageSize"`    // 每页数量
	OrderType   string `json:"orderType"`   // 订单类型
	Status      string `json:"status"`      // 状态
	UserAccount string `json:"userAccount"` // 用户账户
	StartTime   string `json:"startTime"`   // 开始时间
	EndTime     string `json:"endTime"`     // 结束时间
}

// AuthPaymentListResult 授权支付列表结果
type AuthPaymentListResult struct {
	Total    int                  `json:"total"`    // 总数
	Page     int                  `json:"page"`     // 当前页
	PageSize int                  `json:"pageSize"` // 每页数量
	List     []*AuthPaymentResult `json:"list"`     // 数据列表
}

// 充值相关结构

// GetDepositAddressRequest 获取充值地址请求
type GetDepositAddressRequest struct {
	UserLabel string `json:"userLabel"` // 用户标识（users.account）
	Chain     string `json:"chain"`     // 区块链: TRX/TRC20/ETH/ERC20
	Token     string `json:"token"`     // 代币: native或合约地址
}

// DepositAddressResult 充值地址结果
type DepositAddressResult struct {
	Address   string     `json:"address"`     // 充值地址
	UserLabel string     `json:"user_label"`  // 用户标识
	Chain     string     `json:"chain"`       // 区块链
	Token     string     `json:"token"`       // 代币
	IsReused  bool       `json:"is_reused"`   // 是否复用
	QRCode    string     `json:"qr_code"`     // 二维码（base64数据）
	QRCodeURL string     `json:"qr_code_url"` // 二维码URL
	CreatedAt PayBotTime `json:"created_at"`  // 创建时间
}

// PayBotNestedResponse PayBot API的双层嵌套响应结构
type PayBotNestedResponse struct {
	Code    int                  `json:"code"`    // 响应代码
	Message string               `json:"message"` // 响应消息
	Data    DepositAddressResult `json:"data"`    // 实际的充值地址数据
}

// QueryDepositsRequest 查询充值记录请求
type QueryDepositsRequest struct {
	Page      int    `json:"page"`      // 页码
	PageSize  int    `json:"pageSize"`  // 每页数量
	Chain     string `json:"chain"`     // 区块链
	Status    string `json:"status"`    // 状态
	UserLabel string `json:"userLabel"` // 用户标识
	StartTime string `json:"startTime"` // 开始时间
	EndTime   string `json:"endTime"`   // 结束时间
	MinAmount string `json:"minAmount"` // 最小金额
	MaxAmount string `json:"maxAmount"` // 最大金额
}

// DepositRecord 充值记录
type DepositRecord struct {
	ID            int             `json:"id"`            // 记录ID
	MerchantID    int             `json:"merchantId"`    // 商户ID
	UserLabel     string          `json:"userLabel"`     // 用户标识
	Address       string          `json:"address"`       // 充值地址
	Chain         string          `json:"chain"`         // 区块链
	Token         string          `json:"token"`         // 代币
	Amount        decimal.Decimal `json:"amount"`        // 金额
	TxHash        string          `json:"txHash"`        // 交易哈希
	Status        string          `json:"status"`        // 状态
	Confirmations int             `json:"confirmations"` // 确认数
	CallbackSent  bool            `json:"callbackSent"`  // 回调状态
	CreatedAt     time.Time       `json:"createdAt"`     // 创建时间
}

// DepositListResult 充值列表结果
type DepositListResult struct {
	Total    int              `json:"total"`    // 总数
	Page     int              `json:"page"`     // 当前页
	PageSize int              `json:"pageSize"` // 每页数量
	Data     []*DepositRecord `json:"data"`     // 数据列表
}

// GetDepositDetailRequest 获取充值详情请求
type GetDepositDetailRequest struct {
	DepositID int    `json:"depositId"` // 充值记录ID
	TxHash    string `json:"txHash"`    // 交易哈希
}

// DepositDetailResult 充值详情结果
type DepositDetailResult struct {
	*DepositRecord
	// 可以添加更多详细信息
}

// ListDepositAddressesRequest 列出充值地址请求
type ListDepositAddressesRequest struct {
	Page      int    `json:"page"`      // 页码
	PageSize  int    `json:"pageSize"`  // 每页数量
	UserLabel string `json:"userLabel"` // 用户标识
	Chain     string `json:"chain"`     // 区块链
	Status    string `json:"status"`    // 状态
}

// DepositAddressListResult 充值地址列表结果
type DepositAddressListResult struct {
	Total    int                     `json:"total"`    // 总数
	Page     int                     `json:"page"`     // 当前页
	PageSize int                     `json:"pageSize"` // 每页数量
	Data     []*DepositAddressResult `json:"data"`     // 数据列表
}

// 交易查询相关结构

// QueryTransactionsRequest 查询交易请求
type QueryTransactionsRequest struct {
	Page     int    `json:"page"`     // 页码
	PageSize int    `json:"pageSize"` // 每页数量
	Type     string `json:"type"`     // 交易类型
	Status   string `json:"status"`   // 状态
}

// TransactionRecord 交易记录
type TransactionRecord struct {
	ID         int             `json:"id"`         // 记录ID
	MerchantID int             `json:"merchantId"` // 商户ID
	Type       string          `json:"type"`       // 交易类型
	Chain      string          `json:"chain"`      // 区块链
	Token      string          `json:"token"`      // 代币
	Amount     decimal.Decimal `json:"amount"`     // 金额
	Status     string          `json:"status"`     // 状态
	CreatedAt  time.Time       `json:"createdAt"`  // 创建时间
}

// TransactionListResult 交易列表结果
type TransactionListResult struct {
	Total    int                  `json:"total"`    // 总数
	Page     int                  `json:"page"`     // 当前页
	PageSize int                  `json:"pageSize"` // 每页数量
	Data     []*TransactionRecord `json:"data"`     // 数据列表
}

// GetTransactionDetailRequest 获取交易详情请求
type GetTransactionDetailRequest struct {
	TransactionID int `json:"transactionId"` // 交易记录ID
}

// TransactionDetailResult 交易详情结果
type TransactionDetailResult struct {
	*TransactionRecord
	// 可以添加更多详细信息
}

// 提现相关结构

// QueryWithdrawalsRequest 查询提现请求
type QueryWithdrawalsRequest struct {
	Page      int    `json:"page"`      // 页码
	PageSize  int    `json:"pageSize"`  // 每页数量
	Status    string `json:"status"`    // 状态
	UserLabel string `json:"userLabel"` // 用户标识
}

// WithdrawalRecord 提现记录
type WithdrawalRecord struct {
	ID           int             `json:"id"`           // 记录ID
	MerchantID   int             `json:"merchantId"`   // 商户ID
	UserLabel    string          `json:"userLabel"`    // 用户标识
	Chain        string          `json:"chain"`        // 区块链
	Token        string          `json:"token"`        // 代币
	Amount       decimal.Decimal `json:"amount"`       // 金额
	ActualAmount decimal.Decimal `json:"actualAmount"` // 实际金额
	HandlingFee  decimal.Decimal `json:"handlingFee"`  // 手续费
	Address      string          `json:"address"`      // 提现地址
	TxHash       string          `json:"txHash"`       // 交易哈希
	Status       string          `json:"status"`       // 状态
	CreatedAt    time.Time       `json:"createdAt"`    // 创建时间
}

// WithdrawalListResult 提现列表结果
type WithdrawalListResult struct {
	Total    int                 `json:"total"`    // 总数
	Page     int                 `json:"page"`     // 当前页
	PageSize int                 `json:"pageSize"` // 每页数量
	Data     []*WithdrawalRecord `json:"data"`     // 数据列表
}

// GetWithdrawalDetailRequest 获取提现详情请求
type GetWithdrawalDetailRequest struct {
	WithdrawalID int `json:"withdrawalId"` // 提现记录ID
}

// WithdrawalDetailResult 提现详情结果
type WithdrawalDetailResult struct {
	*WithdrawalRecord
	// 可以添加更多详细信息
}

// 回调相关结构

// DepositCallbackRequest 充值回调请求
type DepositCallbackRequest struct {
	EventType     string          `json:"eventType"`     // 事件类型
	OrderNo       string          `json:"orderNo"`       // 订单号
	MerchantID    int             `json:"merchantId"`    // 商户ID
	Amount        decimal.Decimal `json:"amount"`        // 金额
	Currency      string          `json:"currency"`      // 币种
	FromAddress   string          `json:"fromAddress"`   // 来源地址
	ToAddress     string          `json:"toAddress"`     // 目标地址
	TxHash        string          `json:"txHash"`        // 交易哈希
	Confirmations int             `json:"confirmations"` // 确认数
	CompletedAt   time.Time       `json:"completedAt"`   // 完成时间
	Timestamp     int64           `json:"timestamp"`     // 时间戳
	Signature     string          `json:"signature"`     // 签名
}

// WithdrawalCallbackRequest 提现回调请求
type WithdrawalCallbackRequest struct {
	EventType    string          `json:"eventType"`    // 事件类型
	OrderNo      string          `json:"orderNo"`      // 订单号
	MerchantID   int             `json:"merchantId"`   // 商户ID
	Amount       decimal.Decimal `json:"amount"`       // 金额
	Currency     string          `json:"currency"`     // 币种
	ActualAmount decimal.Decimal `json:"actualAmount"` // 实际金额
	HandlingFee  decimal.Decimal `json:"handlingFee"`  // 手续费
	TxHash       string          `json:"txHash"`       // 交易哈希
	CompletedAt  time.Time       `json:"completedAt"`  // 完成时间
	Timestamp    int64           `json:"timestamp"`    // 时间戳
	Signature    string          `json:"signature"`    // 签名
}

// OkpayCallbackResult Okpay回调处理结果
type OkpayCallbackResult struct {
	Success     bool   `json:"success"`     // 处理是否成功
	Message     string `json:"message"`     // 返回给用户的消息
	OrderStatus string `json:"orderStatus"` // 订单状态
	Amount      string `json:"amount"`      // 金额
	Currency    string `json:"currency"`    // 币种
	NeedsNotify bool   `json:"needsNotify"` // 是否需要异步通知
	NotifyMsg   string `json:"notifyMsg"`   // 异步通知消息
}
