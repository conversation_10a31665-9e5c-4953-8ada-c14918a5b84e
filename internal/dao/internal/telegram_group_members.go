// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TelegramGroupMembersDao is the data access object for the table telegram_group_members.
type TelegramGroupMembersDao struct {
	table    string                      // table is the underlying table name of the DAO.
	group    string                      // group is the database configuration group name of the current DAO.
	columns  TelegramGroupMembersColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler          // handlers for customized model modification.
}

// TelegramGroupMembersColumns defines and stores column names for the table telegram_group_members.
type TelegramGroupMembersColumns struct {
	Id        string // 主键ID
	TenantId  string // 租户 id
	ChatId    string // Telegram 群组 ChatID(负数)
	UserId    string // Telegram 用户 ID
	Username  string // 用户名
	FirstName string // FirstName
	LastName  string // LastName
	IsBot     string // 是否机器人
	JoinedAt  string // 加入时间
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
}

// telegramGroupMembersColumns holds the columns for the table telegram_group_members.
var telegramGroupMembersColumns = TelegramGroupMembersColumns{
	Id:        "id",
	TenantId:  "tenant_id",
	ChatId:    "chat_id",
	UserId:    "user_id",
	Username:  "username",
	FirstName: "first_name",
	LastName:  "last_name",
	IsBot:     "is_bot",
	JoinedAt:  "joined_at",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewTelegramGroupMembersDao creates and returns a new DAO object for table data access.
func NewTelegramGroupMembersDao(handlers ...gdb.ModelHandler) *TelegramGroupMembersDao {
	return &TelegramGroupMembersDao{
		group:    "default",
		table:    "telegram_group_members",
		columns:  telegramGroupMembersColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TelegramGroupMembersDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TelegramGroupMembersDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TelegramGroupMembersDao) Columns() TelegramGroupMembersColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TelegramGroupMembersDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TelegramGroupMembersDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TelegramGroupMembersDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
