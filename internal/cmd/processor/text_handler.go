package processor

import (
	"context"
	"fmt"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/i18n/gi18n"

	"github.com/a19ba14d/tg-bot-common/codes"
	"github.com/a19ba14d/tg-bot-common/consts"

	"telegram-bot-api/internal/bot/commands"
	"telegram-bot-api/internal/bot/profile"
	"telegram-bot-api/internal/bot/redpacket"
	"telegram-bot-api/internal/bot/shared"
	"telegram-bot-api/internal/model"
	"telegram-bot-api/internal/model/callback"
	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/registry"
	"telegram-bot-api/internal/service"
)

// handleReplyKeyboardButton handles text messages that match reply keyboard buttons
func handleReplyKeyboardButton(ctx context.Context, message *tgbotapi.Message) (commands.CommandResponse, error) {
	if message.Text == "" {
		return nil, nil // Not a text message
	}

	i18n := service.I18n().Instance()
	chatID := message.Chat.ID

	// Map keyboard button texts to their respective actions
	buttonActions := map[string]string{
		i18n.T(ctx, "{#DepositButton}"):   "deposit",
		i18n.T(ctx, "{#WithdrawButton}"):  "withdraw",
		i18n.T(ctx, "{#ReceiveButton}"):   "receive",
		i18n.T(ctx, "{#RedPacketButton}"): "redpacket",
		i18n.T(ctx, "{#ProfileButton}"):   "profile",
		i18n.T(ctx, "{#LanguageButton}"):  "language",
	}

	// Check if the message text matches any keyboard button
	action, exists := buttonActions[message.Text]
	if !exists {
		return nil, nil // Not a keyboard button press
	}

	g.Log().Infof(ctx, "User %d pressed keyboard button: %s (action: %s)", message.From.ID, message.Text, action)

	// Global state clearing strategy: All keyboard button presses clear user state
	err := service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
	if err != nil {
		g.Log().Warningf(ctx, "Failed to clear user state for user %d when pressing button %s: %v",
			message.From.ID, message.Text, err)
	} else {
		g.Log().Debugf(ctx, "Cleared user state for user %d on keyboard button %s",
			message.From.ID, message.Text)
	}

	// Handle actions directly without using callback system for keyboard buttons
	// This avoids the "message can't be edited" error since we're creating new messages

	var responseText string
	var keyboard *tgbotapi.InlineKeyboardMarkup

	switch action {
	case "deposit":
		// Check if user has payment password first
		securityStatus, err := service.User().GetUserSecurityStatus(ctx, message.From.ID)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get user security status for user %d: %v", message.From.ID, err)
			responseText = i18n.T(ctx, "{#SystemError}")
		} else if !securityStatus.HasPaymentPassword {
			// User hasn't set payment password, show prompt
			responseText = i18n.T(ctx, "{#PaymentPasswordDisabled}")
			kb := createNoPaymentPasswordKeyboard(ctx, i18n)
			keyboard = &kb
		} else {
			responseText = i18n.T(ctx, "{#DepositSelectCoinPrompt}")
			kb := createDepositKeyboard(ctx, i18n)
			keyboard = &kb
		}

	case "withdraw":
		// Check if user has payment password first
		securityStatus, err := service.User().GetUserSecurityStatus(ctx, message.From.ID)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get user security status for user %d: %v", message.From.ID, err)
			responseText = i18n.T(ctx, "{#SystemError}")
		} else if !securityStatus.HasPaymentPassword {
			// User hasn't set payment password, show prompt
			responseText = i18n.T(ctx, "{#WithdrawNoPaymentPassword}")
			kb := createNoPaymentPasswordKeyboard(ctx, i18n)
			keyboard = &kb
		} else {
			responseText = i18n.T(ctx, "{#WithdrawSelectSymbol}")
			// Get available withdrawal symbols and create keyboard
			symbols, err := service.Token().GetActiveWithdrawalSymbols(ctx)
			if err != nil {
				g.Log().Errorf(ctx, "Failed to get withdrawal symbols for user %d: %v", message.From.ID, err)
				responseText = i18n.T(ctx, "{#SystemError}")
			} else if len(symbols) == 0 {
				responseText = i18n.T(ctx, "{#WithdrawNoSymbolsAvailable}")
			} else {
				kb := createWithdrawKeyboard(symbols, ctx, i18n)
				keyboard = &kb
			}
		}

	case "redpacket":
		// Check if user has payment password first
		securityStatus, err := service.User().GetUserSecurityStatus(ctx, message.From.ID)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get user security status for user %d: %v", message.From.ID, err)
			responseText = i18n.T(ctx, "{#SystemError}")
		} else if !securityStatus.HasPaymentPassword {
			// User hasn't set payment password, show prompt
			responseText = i18n.T(ctx, "{#PleaseSetPaymentPasswordFirst}")
			kb := createNoPaymentPasswordKeyboard(ctx, i18n)
			keyboard = &kb
		} else {
			// User has password, show red packet menu
			responseText = i18n.T(ctx, "{#RedPacketMenuTitle}")
			// Get the red packet menu keyboard
			kb := createRedPacketKeyboard(ctx, i18n)
			keyboard = &kb
		}
	case "profile":
		// Build profile text using the shared function
		profileText, err := shared.StartCommandText(ctx)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to build profile text: %v", err)
			responseText = i18n.T(ctx, "{#SystemError}")
		} else {
			responseText = i18n.T(ctx, "{#PersonalInfo}") + "\n\n" + profileText
			// Create profile menu keyboard
			kb := createProfileKeyboard(ctx)
			keyboard = &kb
		}
	case "receive":
		// Check if user has payment password first
		securityStatus, err := service.User().GetUserSecurityStatus(ctx, message.From.ID)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get user security status for user %d: %v", message.From.ID, err)
			responseText = i18n.T(ctx, "{#SystemError}")
		} else if !securityStatus.HasPaymentPassword {
			// User hasn't set payment password, show prompt
			responseText = i18n.T(ctx, "{#PaymentPasswordDisabled}")
			kb := createNoPaymentPasswordKeyboard(ctx, i18n)
			keyboard = &kb
		} else {
			// Get active receive tokens
			tokens, err := service.Token().GetActiveReceiveTokens(ctx)
			if err != nil {
				g.Log().Errorf(ctx, "Failed to get receive tokens for user %d: %v", message.From.ID, err)
				responseText = i18n.T(ctx, "{#SystemError}")
			} else if len(tokens) == 0 {
				responseText = i18n.T(ctx, "{#ReceiveNoTokensMessage}")
			} else {
				responseText = i18n.T(ctx, "{#ReceivePromptSelectToken}")
				kb := createReceiveKeyboard(tokens, ctx, i18n)
				keyboard = &kb
			}
		}
	case "language":
		responseText = i18n.T(ctx, "{#SelectLanguage}")
		kb := createLanguageKeyboard(ctx, i18n)
		keyboard = &kb
	default:
		responseText = i18n.T(ctx, "{#FeatureUnderDevelopment}")
	}

	return &commands.StartCommandResponse{
		ChatID:         chatID,
		Text:           responseText,
		ParseMode:      "HTML", // Set parse mode to HTML for proper formatting
		InlineKeyboard: keyboard,
	}, nil
}

// handleTextMessage handles the logic for text messages.
func handleTextMessage(ctx context.Context, message *tgbotapi.Message) error {
	var replyMsg model.InternalReplyMessage
	var handlerError error
	updateID := message.MessageID // Use MessageID consistently

	// 1. Try Command Handler
	cmdResponse, cmdErr := commands.HandleCommand(ctx, &tgbotapi.Update{Message: message}) // Wrap in Update
	if cmdErr != nil {
		g.Log().Errorf(ctx, "Error handling command for Update %d: %v", updateID, cmdErr)
		handlerError = cmdErr // Store error but continue trying other handlers
	}
	if cmdResponse != nil && cmdErr == nil {
		// Check if it's a NoReplyResponse (command processed but no message needed)
		if _, ok := cmdResponse.(*commands.NoReplyResponse); ok {
			g.Log().Debugf(ctx, "Update %d handled by Command handler with no reply needed.", updateID)
			return nil
		}

		replyMsg = model.InternalReplyMessage{
			ChatID:         cmdResponse.GetChatID(),
			Text:           cmdResponse.GetText(),
			ParseMode:      cmdResponse.GetParseMode(),
			InlineKeyboard: convertInlineKeyboardToModel(cmdResponse.GetReplyMarkup()),
		}

		// Check if it's StartCommandResponse and needs reply keyboard
		if startResp, ok := cmdResponse.(*commands.StartCommandResponse); ok && startResp.SendReplyKeyboard {
			// First send the welcome message with reply keyboard
			// i18n := service.I18n().Instance()
			// welcomeText := i18n.T(ctx, "{#WelcomeReplyKeyboard}")

			// g.Log().Debugf(ctx, "Sending welcome message for chat %d: %s", startResp.GetChatID(), welcomeText)

			// keyboard := shared.GetMainMenuKeyboard(ctx)
			// keyboardMsg := InternalReplyMessage{
			// 	ChatID: startResp.GetChatID(),
			// 	Text:   welcomeText,
			// ReplyMarkup: &keyboard,
			// }
			// err := sendReplyToKafka(ctx, updateID, keyboardMsg)
			// if err != nil {
			// 	return err
			// }

			// Then send the main menu message with a delay (only if text is not empty)
			if replyMsg.Text != "" {
				// go func(parentCtx context.Context) {
				// Wait for 200ms before sending the main menu
				// time.Sleep(200 * time.Millisecond)

				// Check if the parent context is still valid
				// select {
				// case <-parentCtx.Done():
				// 	g.Log().Warningf(parentCtx, "Context cancelled, skipping delayed main menu message")
				// 	return
				// default:
				// 	// Context is still valid, proceed
				// }

				if err := sendReplyToKafka(ctx, updateID+1000000, replyMsg); err != nil {
					g.Log().Errorf(ctx, "Failed to send delayed main menu message: %v", err)
				}
				// }(ctx)
			}
		} else {
			// For non-start commands, send the response immediately (only if text is not empty)
			if replyMsg.Text != "" {
				err := sendReplyToKafka(ctx, updateID, replyMsg)
				if err != nil {
					return err
				}
			}
		}

		g.Log().Debugf(ctx, "Update %d handled by Command handler.", updateID)
		return nil
	}

	// 1.5. Try Reply Keyboard Button Handler
	keyboardResponse, keyboardErr := handleReplyKeyboardButton(ctx, message)
	if keyboardResponse != nil && keyboardErr == nil {
		replyMsg = model.InternalReplyMessage{
			ChatID:    keyboardResponse.GetChatID(),
			Text:      keyboardResponse.GetText(),
			ParseMode: keyboardResponse.GetParseMode(),
			// TODO: Convert ReplyMarkup to InlineKeyboard format
			// ReplyMarkup: keyboardResponse.GetReplyMarkup(),
		}

		// Check if needs reply keyboard
		if startResp, ok := keyboardResponse.(*commands.StartCommandResponse); ok && startResp.SendReplyKeyboard {
			// First send the welcome message with reply keyboard
			// i18n := service.I18n().Instance()
			// keyboard := shared.GetMainMenuKeyboard(ctx)
			// keyboardMsg := InternalReplyMessage{
			// 	ChatID: startResp.GetChatID(),
			// 	Text:   i18n.T(ctx, "{#WelcomeReplyKeyboard}"),
			// ReplyMarkup: &keyboard,
			// }
			// err := sendReplyToKafka(ctx, updateID, keyboardMsg)
			// if err != nil {
			// 	return err
			// }

			// Then send the main response with a delay
			// go func() {
			// Wait for 200ms before sending the main response
			// time.Sleep(200 * time.Millisecond)

			// Use a new context since the original might be cancelled
			// newCtx := context.Background()
			if err := sendReplyToKafka(ctx, updateID+2000000, replyMsg); err != nil {
				g.Log().Errorf(ctx, "Failed to send delayed main response: %v", err)
			}
			// }()
		} else {
			// For non-start responses, send immediately
			err := sendReplyToKafka(ctx, updateID, replyMsg)
			if err != nil {
				return err
			}
		}

		g.Log().Debugf(ctx, "Update %d handled by Reply Keyboard handler.", updateID)
		return nil
	}

	// 2. Try State Input Handler
	var stateInputResponse *tgbotapi.MessageConfig
	var stateInputErr error
	userState, stateErr := service.UserState().GetUserStateByTelegramId(ctx, message.From.ID)
	if stateErr != nil {
		g.Log().Warningf(ctx, "Failed to get user state while checking for input handler for Update %d: %v", updateID, stateErr)
		// Continue without state handling if error occurs
	} else if userState != nil { // Changed from 'if userState != nil' to 'else if' to chain correctly
		// Dispatch based on state
		if userState.State == consts.UserStateWaitingRedPacketCover { // Check for cover upload state FIRST
			g.Log().Debugf(ctx, "Update %d matches state %s, routing to HandleRedPacketCoverMessage.", updateID, consts.UserStateWaitingRedPacketCover)
			stateInputErr = redpacket.HandleRedPacketCoverMessage(ctx, &tgbotapi.Update{Message: message}) // Call the handler
			if stateInputErr != nil {
				g.Log().Errorf(ctx, "Error from HandleRedPacketCoverMessage for Update %d: %v", updateID, stateInputErr)
				handlerError = stateInputErr // Store error
				// Let fallback logic handle sending the error message
			} else {
				// If HandleRedPacketCoverMessage handled it successfully (returned nil), stop further processing.
				return nil
			}
		} else if userState.State == "waiting_backup_account_id" { // Existing state handlers
			stateInputResponse, stateInputErr = profile.HandleBackupAccountIdInput(ctx, message)
			if stateInputErr != nil {
				g.Log().Errorf(ctx, "Error from state input handler '%s' for Update %d: %v", userState.State, updateID, stateInputErr)
				handlerError = stateInputErr // Store error
			}
		}
		// Add more 'else if userState.State == ...' blocks here for other states
	}

	if stateInputResponse != nil && stateInputErr == nil {
		replyMsg = model.InternalReplyMessage{
			ChatID:    stateInputResponse.ChatID,
			Text:      stateInputResponse.Text,
			ParseMode: stateInputResponse.ParseMode,
		}
		// TODO: Convert ReplyMarkup to InlineKeyboard format
		// if stateInputResponse.ReplyMarkup != nil {
		// 	if keyboard, ok := stateInputResponse.ReplyMarkup.(tgbotapi.InlineKeyboardMarkup); ok {
		// 		replyMsg.ReplyMarkup = &keyboard
		// 	} else {
		// 		g.Log().Warningf(ctx, "State input handler returned unhandled ReplyMarkup type for state %s, Update %d", userState.State, updateID)
		// 	}
		// }
		g.Log().Debugf(ctx, "Update %d handled by State input handler for state '%s'.", updateID, userState.State)
		return sendReplyToKafka(ctx, updateID, replyMsg)
	}

	// 3. Try General Message Handler
	msgResponse, msgErr := registry.HandleMessage(ctx, message)
	// Check if the error is the sentinel error indicating handled state
	isHandledByState := gerror.Is(msgErr, codes.ErrMessageHandled)
	if isHandledByState {
		g.Log().Debugf(ctx, "Message for Update %d was handled by a registered state handler.", updateID)
		return nil // Message handled, stop processing here, no fallback
	}
	// Handle other actual errors from the registry
	if msgErr != nil {
		g.Log().Errorf(ctx, "Error handling message via registry for Update %d: %v", updateID, msgErr)
		if handlerError == nil { // Prioritize earlier errors if any
			handlerError = msgErr
		}
		// Continue to fallback logic to potentially report the error
	}
	// If msgErr was nil, but msgResponse is also nil, it means no handler processed it.
	if msgResponse != nil && msgErr == nil {
		replyMsg = model.InternalReplyMessage{
			ChatID:    msgResponse.ChatID,
			Text:      msgResponse.Text,
			ParseMode: msgResponse.ParseMode,
		}
		// TODO: Convert ReplyMarkup to InlineKeyboard format
		// if msgResponse.ReplyMarkup != nil {
		// 	if keyboard, ok := msgResponse.ReplyMarkup.(tgbotapi.InlineKeyboardMarkup); ok {
		// 		replyMsg.ReplyMarkup = &keyboard
		// 	}
		// }
		g.Log().Debugf(ctx, "Update %d handled by General Message handler.", updateID)
		return sendReplyToKafka(ctx, updateID, replyMsg)
	}

	// 4. Fallback Logic (if no handler processed the message)
	if replyMsg.ChatID == 0 { // Check if replyMsg is still empty
		if handlerError != nil {
			// An error occurred in one of the handlers, send a generic error message
			g.Log().Errorf(ctx, "Error occurred during message handling for Update %d, sending generic error reply. Last error: %v", updateID, handlerError)
			replyMsg = model.InternalReplyMessage{
				ChatID: message.Chat.ID,
				Text:   service.I18n().T(ctx, "{#error_occurred}"), // Generic error message
			}
		} else if message.IsCommand() {
			// Command was not handled by any handler
			g.Log().Warningf(ctx, "Command '%s' from Update %d was not handled by any registered handler.", message.Command(), updateID)
			replyMsg = model.InternalReplyMessage{
				ChatID: message.Chat.ID,
				Text:   fmt.Sprintf(service.I18n().T(ctx, "{#UnknownCommand}"), message.Command()),
			}
		} else {
			// Simple echo for non-command text messages not handled otherwise
			replyText := service.I18n().T(ctx, "{#EchoPrefix}") + message.Text
			replyMsg = model.InternalReplyMessage{
				ChatID: message.Chat.ID,
				Text:   replyText,
			}
			g.Log().Debugf(ctx, "Update %d is a non-command text message, using echo fallback.", updateID)

			return nil
		}
		return sendReplyToKafka(ctx, updateID, replyMsg)
	}

	// Should not reach here if logic is correct, but return nil to commit offset
	return nil
}

// createDepositKeyboard creates the deposit coin selection keyboard
func createDepositKeyboard(ctx context.Context, i18n *gi18n.Manager) tgbotapi.InlineKeyboardMarkup {
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("TRX", "deposit_select_coin_TRX"),
			tgbotapi.NewInlineKeyboardButtonData("ETH", "deposit_select_coin_ETH"),
			tgbotapi.NewInlineKeyboardButtonData("USDT", "deposit_select_coin_USDT"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 "+i18n.T(ctx, "{#BackButton}"), "back_to_start"),
		),
	)
	return keyboard
}

// createWithdrawKeyboard creates the withdraw symbol selection keyboard
func createWithdrawKeyboard(symbols []string, ctx context.Context, i18n *gi18n.Manager) tgbotapi.InlineKeyboardMarkup {
	var rows [][]tgbotapi.InlineKeyboardButton

	// Create buttons for symbols, 3 per row
	var currentRow []tgbotapi.InlineKeyboardButton
	for i, symbol := range symbols {
		button := tgbotapi.NewInlineKeyboardButtonData(symbol, consts.WithdrawSelectSymbol+symbol)
		currentRow = append(currentRow, button)

		// Add row when we have 3 buttons or reached the end
		if len(currentRow) == 2 || i == len(symbols)-1 {
			rows = append(rows, currentRow)
			currentRow = []tgbotapi.InlineKeyboardButton{}
		}
	}

	// Add back button
	rows = append(rows, tgbotapi.NewInlineKeyboardRow(
		tgbotapi.NewInlineKeyboardButtonData("◀️ 返回", "back_to_start"),
	))

	return tgbotapi.NewInlineKeyboardMarkup(rows...)
}

// createRedPacketKeyboard creates the red packet menu keyboard
func createRedPacketKeyboard(ctx context.Context, i18n *gi18n.Manager) tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				i18n.T(ctx, "{#RedPacketOngoingButton}"),
				(&callback.RedPacketHistoryData{
					BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix},
					Action:           callback.RphActionSent,
					Status:           "active",
					Page:             1,
				}).String(), // Red packet history - ongoing
			),
			tgbotapi.NewInlineKeyboardButtonData(
				i18n.T(ctx, "{#RedPacketEndedButton}"),
				(&callback.RedPacketHistoryData{
					BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix},
					Action:           callback.RphActionSent,
					Status:           "expired_empty",
					Page:             1,
				}).String(), // Red packet history - ended
			),
			tgbotapi.NewInlineKeyboardButtonData(
				i18n.T(ctx, "{#RedPacketAllButton}"),
				(&callback.RedPacketHistoryData{
					BaseCallbackData: callback.BaseCallbackData{Prefix: callback.RphPrefix},
					Action:           callback.RphActionSent,
					Status:           "all",
					Page:             1,
				}).String(), // Red packet history - all
			),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				i18n.T(ctx, "{#RedPacketAddButton}"),
				consts.CallbackRedPacketAdd, // Create new red packet
			),
			tgbotapi.NewInlineKeyboardButtonData(
				i18n.T(ctx, "{#RedPacketSetCoverButton}"),
				consts.CallbackRedPacketSetCover, // Set cover
			),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"🔙 "+i18n.T(ctx, "{#BackButton}"),
				"navigation:main", // Back button - consistent with keyboards.go
			),
		),
	)
}

// createProfileKeyboard creates the profile menu keyboard
func createProfileKeyboard(ctx context.Context) tgbotapi.InlineKeyboardMarkup {
	return profile.GetProfileMenuKeyboard(ctx)
}

// createReceiveKeyboard creates the receive token selection keyboard
func createReceiveKeyboard(tokens []*entity.Tokens, ctx context.Context, i18n *gi18n.Manager) tgbotapi.InlineKeyboardMarkup {
	var keyboardRows [][]tgbotapi.InlineKeyboardButton
	var currentRow []tgbotapi.InlineKeyboardButton

	for _, token := range tokens {
		button := tgbotapi.NewInlineKeyboardButtonData(
			token.Symbol,
			fmt.Sprintf("%s%s", consts.CallbackPrefixReceiveSelectToken, token.Symbol),
		)
		currentRow = append(currentRow, button)

		// 2 buttons per row for better layout
		if len(currentRow) == 2 {
			keyboardRows = append(keyboardRows, currentRow)
			currentRow = []tgbotapi.InlineKeyboardButton{}
		}
	}

	// Add the last row if it's not full
	if len(currentRow) > 0 {
		keyboardRows = append(keyboardRows, currentRow)
	}

	// Add back button
	keyboardRows = append(keyboardRows, tgbotapi.NewInlineKeyboardRow(
		tgbotapi.NewInlineKeyboardButtonData("🔙 "+i18n.T(ctx, "{#BackButton}"), consts.CallbackNavigateBack),
	))

	return tgbotapi.NewInlineKeyboardMarkup(keyboardRows...)
}

// createLanguageKeyboard creates the language selection keyboard
func createLanguageKeyboard(ctx context.Context, i18n *gi18n.Manager) tgbotapi.InlineKeyboardMarkup {
	// Create language buttons using local consts
	var languageButtons []tgbotapi.InlineKeyboardButton
	for _, v := range consts.TypeList {
		item := v.(consts.LanguageType)
		languageButtons = append(
			languageButtons,
			tgbotapi.NewInlineKeyboardButtonData(item.Text, "language_"+string(item.Code)),
		)
	}

	// Create back button
	backButton := tgbotapi.NewInlineKeyboardButtonData("🔙 "+i18n.T(ctx, "{#BackButton}"), "back_to_start")

	// Create language selection menu with language buttons on one row and back button on separate row
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(languageButtons...), // First row: language buttons
		tgbotapi.NewInlineKeyboardRow(backButton),         // Second row: back button
	)

	return keyboard
}
