package admin

import (
	"context"
	"fmt"
	"strings"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"telegram-bot-api/internal/model/callback"
	"telegram-bot-api/internal/service"
)

// handlePlatformStatsTimePeriod handles platform statistics with time period selection
func handlePlatformStatsTimePeriod(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	// Extract the time period from callback data
	parts := strings.Split(callbackQuery.Data, ":")
	if len(parts) != 2 {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "InvalidRequest")), nil
	}
	period := parts[1]

	// Verify admin permission
	isAdmin, err := service.Admin().IsCurrentUserAdmin(ctx)
	if err != nil {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionError")), nil
	}
	if !isAdmin {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionDenied")), nil
	}

	// Get platform statistics for the specified period
	stats, err := service.Admin().GetPlatformStatsDetail(ctx, period)
	if err != nil {
		text := service.I18n().T(ctx, "AdminPlatformStatsError")
		keyboard := BuildPlatformStatsKeyboard(ctx)

		return &callback.EditMessageResponse{
			CallbackQueryID: callbackQuery.ID,
			ChatID:          callbackQuery.Message.Chat.ID,
			MessageID:       callbackQuery.Message.MessageID,
			Text:            text,
			ParseMode:       "HTML",
			InlineKeyboard:  &keyboard,
		}, nil
	}

	// Build detailed statistics message
	text := BuildDetailedPlatformStatsMessage(ctx, stats)
	keyboard := BuildPlatformStatsKeyboard(ctx)

	return &callback.EditMessageResponse{
		CallbackQueryID: callbackQuery.ID,
		ChatID:          callbackQuery.Message.Chat.ID,
		MessageID:       callbackQuery.Message.MessageID,
		Text:            text,
		ParseMode:       "HTML",
		InlineKeyboard:  &keyboard,
	}, nil
}

// BuildPlatformStatsKeyboard builds the keyboard for platform statistics time period selection
func BuildPlatformStatsKeyboard(ctx context.Context) tgbotapi.InlineKeyboardMarkup {
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		// First row - Today and Yesterday
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				service.I18n().T(ctx, "AdminStatsToday"),
				"admin_stats_period:today",
			),
			tgbotapi.NewInlineKeyboardButtonData(
				service.I18n().T(ctx, "AdminStatsYesterday"),
				"admin_stats_period:yesterday",
			),
		),
		// Second row - This Week and This Month
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				service.I18n().T(ctx, "AdminStatsThisWeek"),
				"admin_stats_period:this_week",
			),
			tgbotapi.NewInlineKeyboardButtonData(
				service.I18n().T(ctx, "AdminStatsThisMonth"),
				"admin_stats_period:this_month",
			),
		),
		// Third row - Last Month
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				service.I18n().T(ctx, "AdminStatsLastMonth"),
				"admin_stats_period:last_month",
			),
		),
		// Fourth row - Export and Refresh
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				service.I18n().T(ctx, "AdminStatsExport"),
				"admin_stats_export",
			),
			tgbotapi.NewInlineKeyboardButtonData(
				service.I18n().T(ctx, "AdminStatsRefresh"),
				"admin_stats_refresh",
			),
		),
		// Back button
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				service.I18n().T(ctx, "BackToAdminCenter"),
				"admin_center",
			),
		),
	)

	return keyboard
}

// BuildDetailedPlatformStatsMessage builds a detailed platform statistics message
func BuildDetailedPlatformStatsMessage(ctx context.Context, stats *service.PlatformStatsDetail) string {
	if stats == nil {
		return service.I18n().T(ctx, "AdminPlatformStatsNoData")
	}

	// Format time period display
	periodDisplay := getPeriodDisplay(ctx, stats.Period)
	timeRange := ""
	if stats.StartTime != nil && stats.EndTime != nil {
		timeRange = fmt.Sprintf("%s - %s",
			stats.StartTime.String(),
			stats.EndTime.String())
	}

	// Build the message with sections
	var sections []string

	// Header
	sections = append(sections, fmt.Sprintf("<b>📊 %s</b>",
		service.I18n().T(ctx, "AdminPlatformStatsTitle")))
	sections = append(sections, fmt.Sprintf("<b>%s</b>: %s",
		service.I18n().T(ctx, "AdminStatsPeriod"), periodDisplay))
	if timeRange != "" {
		sections = append(sections, fmt.Sprintf("<code>%s</code>", timeRange))
	}
	sections = append(sections, "") // Empty line

	// User Statistics Section
	sections = append(sections, fmt.Sprintf("<b>👥 %s</b>",
		service.I18n().T(ctx, "AdminStatsUserSection")))
	sections = append(sections, fmt.Sprintf("• %s: <b>%d</b>",
		service.I18n().T(ctx, "AdminStatsTotalUsers"), stats.TotalUsers))
	sections = append(sections, fmt.Sprintf("• %s: <b>%d</b>",
		service.I18n().T(ctx, "AdminStatsNewUsers"), stats.NewUsers))
	sections = append(sections, fmt.Sprintf("• %s: <b>%d</b>",
		service.I18n().T(ctx, "AdminStatsActiveUsers"), stats.ActiveUsers))
	sections = append(sections, "") // Empty line

	// Financial Statistics Section
	sections = append(sections, fmt.Sprintf("<b>💰 %s</b>",
		service.I18n().T(ctx, "AdminStatsFinancialSection")))
	sections = append(sections, fmt.Sprintf("• %s: <b>%s %s</b> (%d)",
		service.I18n().T(ctx, "AdminStatsTotalDeposits"),
		stats.TotalDeposits.String(), stats.Currency, stats.DepositCount))
	sections = append(sections, fmt.Sprintf("• %s: <b>%s %s</b> (%d)",
		service.I18n().T(ctx, "AdminStatsTotalWithdrawals"),
		stats.TotalWithdrawals.String(), stats.Currency, stats.WithdrawalCount))
	sections = append(sections, fmt.Sprintf("• %s: <b>%s %s</b>",
		service.I18n().T(ctx, "AdminStatsNetRevenue"),
		stats.NetRevenue.String(), stats.Currency))
	sections = append(sections, "") // Empty line

	// Manual Adjustments Section (if any)
	if !stats.ManualBalanceAdjustments.IsZero() || !stats.ManualBonusAdjustments.IsZero() {
		sections = append(sections, fmt.Sprintf("<b>🔧 %s</b>",
			service.I18n().T(ctx, "AdminStatsManualAdjustments")))
		if !stats.ManualBalanceAdjustments.IsZero() {
			sections = append(sections, fmt.Sprintf("• %s: <b>%s %s</b>",
				service.I18n().T(ctx, "AdminStatsBalanceAdjustments"),
				stats.ManualBalanceAdjustments.String(), stats.Currency))
		}
		if !stats.ManualBonusAdjustments.IsZero() {
			sections = append(sections, fmt.Sprintf("• %s: <b>%s %s</b>",
				service.I18n().T(ctx, "AdminStatsBonusAdjustments"),
				stats.ManualBonusAdjustments.String(), stats.Currency))
		}
		sections = append(sections, "") // Empty line
	}

	// Game Statistics Section (if available)
	if !stats.GameProfit.IsZero() || !stats.GameLoss.IsZero() || !stats.GameNetProfit.IsZero() {
		sections = append(sections, fmt.Sprintf("<b>🎮 %s</b>",
			service.I18n().T(ctx, "AdminStatsGameSection")))
		sections = append(sections, fmt.Sprintf("• %s: <b>%s %s</b>",
			service.I18n().T(ctx, "AdminStatsGameProfit"),
			stats.GameProfit.String(), stats.Currency))
		sections = append(sections, fmt.Sprintf("• %s: <b>%s %s</b>",
			service.I18n().T(ctx, "AdminStatsGameLoss"),
			stats.GameLoss.String(), stats.Currency))
		sections = append(sections, fmt.Sprintf("• %s: <b>%s %s</b>",
			service.I18n().T(ctx, "AdminStatsGameNetProfit"),
			stats.GameNetProfit.String(), stats.Currency))
		sections = append(sections, "") // Empty line
	}

	// Commission Section (if available)
	if !stats.TotalCommission.IsZero() {
		sections = append(sections, fmt.Sprintf("<b>💸 %s</b>",
			service.I18n().T(ctx, "AdminStatsCommissionSection")))
		sections = append(sections, fmt.Sprintf("• %s: <b>%s %s</b>",
			service.I18n().T(ctx, "AdminStatsTotalCommission"),
			stats.TotalCommission.String(), stats.Currency))
		sections = append(sections, "") // Empty line
	}

	// Footer note
	sections = append(sections, fmt.Sprintf("<i>%s</i>",
		service.I18n().T(ctx, "AdminStatsNote")))

	return strings.Join(sections, "\n")
}

// getPeriodDisplay returns the localized display name for a period
func getPeriodDisplay(ctx context.Context, period string) string {
	switch period {
	case "today":
		return service.I18n().T(ctx, "AdminStatsPeriodToday")
	case "yesterday":
		return service.I18n().T(ctx, "AdminStatsPeriodYesterday")
	case "this_week":
		return service.I18n().T(ctx, "AdminStatsPeriodThisWeek")
	case "this_month":
		return service.I18n().T(ctx, "AdminStatsPeriodThisMonth")
	case "last_month":
		return service.I18n().T(ctx, "AdminStatsPeriodLastMonth")
	default:
		return period
	}
}

// handlePlatformStatsRefresh handles refresh of platform statistics
func handlePlatformStatsRefresh(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	// Show loading indicator
	answerConfig := tgbotapi.NewCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminStatsRefreshing"))
	answerConfig.ShowAlert = false

	// Simply redirect to today's stats by default
	callbackQuery.Data = "admin_stats_period:today"
	return handlePlatformStatsTimePeriod(ctx, callbackQuery)
}

// handlePlatformStatsExport handles export of platform statistics
func handlePlatformStatsExport(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	// For now, just show a message that export is not yet implemented
	return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminStatsExportNotImplemented")), nil
}
