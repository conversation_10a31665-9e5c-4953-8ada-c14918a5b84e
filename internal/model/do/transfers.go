// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Transfers is the golang structure of table transfers for DAO operations like Where/Data.
type Transfers struct {
	g.Meta                `orm:"table:transfers, do:true"`
	TransferId            interface{} // 转账记录 ID (主键)
	TenantId              interface{} // 租户 id
	MessageId             interface{} // 转账记录 ID (主键)
	ChatId                interface{} // 转账记录 ID (主键)
	SenderUserId          interface{} // 发送方用户 ID (外键, 指向 users.user_id)
	ReceiverUserId        interface{} // 接收方用户 ID (外键, 指向 users.user_id)
	TokenId               interface{} // 代币 ID (外键, 指向 tokens.token_id)
	Amount                interface{} // 转账金额 (最小单位)
	SenderTransactionId   interface{} // 关联的发送方资金扣除交易 ID (外键, 指向 transactions.transaction_id)
	ReceiverTransactionId interface{} // 关联的接收方资金增加交易 ID (外键, 指向 transactions.transaction_id)
	Memo                  interface{} // 转账备注
	Status                interface{} // 状态 (pending_pass, pending_collection, completed, expired)
	HoldId                interface{} // 钱包服务返回的冻结 ID
	CreatedAt             *gtime.Time // 转账发起时间 (记录创建时间)
	ExpiresAt             *gtime.Time // 过期时间 (created_at + 24 小时)
	UpdatedAt             *gtime.Time // 最后更新时间
	DeletedAt             *gtime.Time // 软删除时间
	NeedPass              interface{} // 是否需要支付密码
	Key                   interface{} //
	Symbol                interface{} //
	Message               interface{} //
	InlineMessageId       interface{} // 内联消息 ID，用于后续编辑
	SenderUsername        interface{} // 发送方用户名
	ReceiverUsername      interface{} // 接收方用户名
	NotificationSent      interface{} // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt    *gtime.Time // 通知发送时间
}
