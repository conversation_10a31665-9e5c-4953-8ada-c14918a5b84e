// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// PaymentRequests is the golang structure for table payment_requests.
type PaymentRequests struct {
	RequestId              uint64          `json:"requestId"              orm:"request_id"               description:"收款请求 ID (主键)"`                                                        // 收款请求 ID (主键)
	TenantId               int             `json:"tenantId"               orm:"tenant_id"                description:"租户 id"`                                                               // 租户 id
	RequesterUserId        uint64          `json:"requesterUserId"        orm:"requester_user_id"        description:"收款发起者用户 ID (外键, 指向 users.user_id)"`                                   // 收款发起者用户 ID (外键, 指向 users.user_id)
	RequesterUsername      string          `json:"requesterUsername"      orm:"requester_username"       description:"收款发起者telegram username"`                                              // 收款发起者telegram username
	PayerUserId            uint64          `json:"payerUserId"            orm:"payer_user_id"            description:"指定付款人用户 ID (如果为空则任何人可付, 外键, 指向 users.user_id)"`                       // 指定付款人用户 ID (如果为空则任何人可付, 外键, 指向 users.user_id)
	PayerUsername          string          `json:"payerUsername"          orm:"payer_username"           description:"指定付款人用户telegram username"`                                            // 指定付款人用户telegram username
	TokenId                uint            `json:"tokenId"                orm:"token_id"                 description:"收款代币 ID (外键, 指向 tokens.token_id)"`                                    // 收款代币 ID (外键, 指向 tokens.token_id)
	Amount                 decimal.Decimal `json:"amount"                 orm:"amount"                   description:"收款金额"`                                                                // 收款金额
	Memo                   string          `json:"memo"                   orm:"memo"                     description:"收款说明/备注"`                                                             // 收款说明/备注
	Status                 uint            `json:"status"                 orm:"status"                   description:"请求状态: 1-待支付(pending), 2-已支付(paid), 3-已过期(expired), 4-已取消(cancelled)"` // 请求状态: 1-待支付(pending), 2-已支付(paid), 3-已过期(expired), 4-已取消(cancelled)
	PaymentTransactionId   uint64          `json:"paymentTransactionId"   orm:"payment_transaction_id"   description:"关联的支付交易记录ID (外键, 指向 transactions.transaction_id)"`                    // 关联的支付交易记录ID (外键, 指向 transactions.transaction_id)
	RequesterTransactionId uint64          `json:"requesterTransactionId" orm:"requester_transaction_id" description:"关联的支付交易记录ID (外键, 指向 transactions.transaction_id)"`                    // 关联的支付交易记录ID (外键, 指向 transactions.transaction_id)
	TelegramChatId         int64           `json:"telegramChatId"         orm:"telegram_chat_id"         description:"发起请求的 Telegram 聊天 ID (用于更新消息)"`                                       // 发起请求的 Telegram 聊天 ID (用于更新消息)
	TelegramMessageId      int             `json:"telegramMessageId"      orm:"telegram_message_id"      description:"原始收款请求消息的 Telegram 消息 ID (用于更新消息)"`                                   // 原始收款请求消息的 Telegram 消息 ID (用于更新消息)
	CreatedAt              *gtime.Time     `json:"createdAt"              orm:"created_at"               description:"创建时间"`                                                                // 创建时间
	ExpiresAt              *gtime.Time     `json:"expiresAt"              orm:"expires_at"               description:"过期时间 (例如: 创建时间 + 24小时)"`                                              // 过期时间 (例如: 创建时间 + 24小时)
	PaidAt                 *gtime.Time     `json:"paidAt"                 orm:"paid_at"                  description:"支付时间"`                                                                // 支付时间
	CancelledAt            *gtime.Time     `json:"cancelledAt"            orm:"cancelled_at"             description:"取消时间"`                                                                // 取消时间
	InlineMessageId        string          `json:"inlineMessageId"        orm:"inline_message_id"        description:"内联消息 ID，用于后续编辑"`                                                      // 内联消息 ID，用于后续编辑
	UpdatedAt              *gtime.Time     `json:"updatedAt"              orm:"updated_at"               description:"最后更新时间"`                                                              // 最后更新时间
	DeletedAt              *gtime.Time     `json:"deletedAt"              orm:"deleted_at"               description:"软删除的时间戳"`                                                             // 软删除的时间戳
	Symbol                 string          `json:"symbol"                 orm:"symbol"                   description:""`                                                                    //
}
