package main

import (
	"telegram-bot-api/internal/cmd"
	_ "telegram-bot-api/internal/logic"  // 注册业务逻辑 (如果需要自动注册) - 暂时注释，待 logic 包实现后取消注释
	_ "telegram-bot-api/internal/packed" // 打包静态资源 (如果使用)

	// Import packages for functionality
	_ "telegram-bot-api/internal/bot/callback"     // Import callback package for group red packet callbacks
	_ "telegram-bot-api/internal/bot/profile"
	_ "telegram-bot-api/internal/bot/verification" // Import verification package for backup account verification

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/v2/os/gctx"
)

func main() {
	// 传递 "processor" 或类似标识符区分应用类型
	// 回调处理程序现在在 cmd.Main 中自动注册
	cmd.Main(gctx.New(), "processor")
}
