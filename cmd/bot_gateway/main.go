package main

import (
	_ "telegram-bot-api/internal/bot/profile"
	_ "telegram-bot-api/internal/logic"  // 注册业务逻辑 (如果需要自动注册) - 暂时注释，待 logic 包实现后取消注释
	_ "telegram-bot-api/internal/packed" // 打包静态资源 (如果使用)

	"telegram-bot-api/internal/cmd"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/v2/os/gctx"
)

func main() {
	// 使用 internal/cmd 包中的 Main 函数启动网关服务
	// 传递 "gateway" 或类似标识符区分应用类型
	cmd.Main(gctx.New(), "gateway")
}
