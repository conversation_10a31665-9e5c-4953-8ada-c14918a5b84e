# Task Runner - 定时任务测试工具

这个工具允许你单独运行和测试项目中的定时任务。

## 构建

```bash
go build -o task-runner cmd/task-runner/main.go
```

## 使用方法

### 列出所有可用任务

```bash
./task-runner -list
```

输出示例：
```
Available tasks:
  - SyncGameBetDetails
  - HandleExpiredRedPackets
  - HandleCompletedRecharges
  - HandleWithdrawNotifications
  - HandleRedPacketClaimNotifications
  - HandleRedPacketCoverAuditNotify
  - HandleRedPacketCoverProcessing
  - HandleDailyCommission
  - HandlePayBotAuthPaymentNotifications
```

### 运行特定任务

```bash
# 运行游戏投注记录同步任务
./task-runner -task SyncGameBetDetails

# 运行过期红包处理任务
./task-runner -task HandleExpiredRedPackets

# 运行充值通知处理任务
./task-runner -task HandleCompletedRecharges
```

### 中断任务

按 `Ctrl+C` 可以优雅地中断正在运行的任务。

## 任务说明

- **SyncGameBetDetails**: 同步 TCGaming 投注记录（昨天和今天）
- **HandleExpiredRedPackets**: 处理过期的红包
- **HandleCompletedRecharges**: 处理已完成的充值通知
- **HandleWithdrawNotifications**: 处理提现通知
- **HandleRedPacketClaimNotifications**: 处理红包领取通知
- **HandleRedPacketCoverAuditNotify**: 处理红包封面审核通知
- **HandleRedPacketCoverProcessing**: 处理红包封面
- **HandleDailyCommission**: 处理每日佣金
- **HandlePayBotAuthPaymentNotifications**: 处理 PayBot 授权支付通知

## 注意事项

1. 运行任务前确保配置正确（Consul、数据库等）
2. 任务使用协调机制，同一时间只能有一个相同任务在运行
3. 所有任务都有详细的日志输出，可以查看执行进度
4. 任务执行失败会返回非零退出码