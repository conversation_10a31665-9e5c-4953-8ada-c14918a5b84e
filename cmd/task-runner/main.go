package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"telegram-bot-api/internal/boot"
	"telegram-bot-api/internal/service"
	"telegram-bot-api/internal/task"
	"time"

	// Import packages for functionality
	_ "telegram-bot-api/internal/bot/profile"
	_ "telegram-bot-api/internal/bot/verification" // Import verification package for backup account verification

	_ "telegram-bot-api/internal/logic"

	"github.com/gogf/gf/v2/frame/g"

	_ "telegram-bot-api/internal/packed"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
)

var (
	taskName  = flag.String("task", "", "Task name to run (e.g., SyncGameBetDetails, HandleExpiredRedPackets)")
	listTasks = flag.Bool("list", false, "List all available tasks")
)

// availableTasks 定义所有可用的任务
var availableTasks = map[string]func(context.Context) error{
	// 游戏投注记录同步
	"SyncGameBetDetails":                   task.SyncGameBetDetails,
	"HandleExpiredRedPackets":              task.HandleExpiredRedPackets,
	"HandleCompletedRecharges":             task.HandleCompletedRecharges,
	"HandleWithdrawNotifications":          task.HandleWithdrawNotifications,
	"HandleRedPacketClaimNotifications":    task.HandleRedPacketClaimNotifications,
	"HandleRedPacketCoverAuditNotify":      task.HandleRedPacketCoverAuditNotify,
	"HandleRedPacketCoverProcessing":       task.HandleRedPacketCoverProcessing,
	"HandleDailyCommission":                task.HandleDailyCommission,
	"HandleDailyBettingBonus":              task.HandleDailyBettingBonus,
	"HandlePayBotAuthPaymentNotifications": task.HandlePayBotAuthPaymentNotifications,
	"HandleCommissionNotifications":        task.HandleCommissionNotifications,
	// 客服群通知提醒
	"HandleCustomerServiceNotifications": task.HandleCustomerServiceNotifications,
}

func main() {
	flag.Parse()

	// 列出所有可用任务
	if *listTasks {
		fmt.Println("Available tasks:")
		for name := range availableTasks {
			fmt.Printf("  - %s\n", name)
		}
		return
	}

	// 检查是否指定了任务名称
	if *taskName == "" {
		fmt.Println("Error: Please specify a task name with -task flag")
		fmt.Println("Use -list to see all available tasks")
		flag.Usage()
		os.Exit(1)
	}

	// 检查任务是否存在
	taskFunc, exists := availableTasks[*taskName]
	if !exists {
		fmt.Printf("Error: Task '%s' not found\n", *taskName)
		fmt.Println("Use -list to see all available tasks")
		os.Exit(1)
	}

	// 初始化应用
	ctx := context.Background()

	// 初始化启动器
	boot.Initialize(ctx)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 创建任务执行的 context
	taskCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	// 任务完成通道
	doneChan := make(chan error, 1)

	// 在 goroutine 中执行任务
	go func() {
		g.Log().Infof(taskCtx, "Starting task: %s", *taskName)

		// 使用任务协调器执行任务，避免并发冲突
		task.ExecuteWithCoordination(taskCtx, *taskName, func(ctx context.Context) error {
			return taskFunc(ctx)
		})

		// ExecuteWithCoordination 会处理错误，这里返回 nil
		doneChan <- nil
	}()

	// 等待任务完成或收到终止信号
	select {
	case err := <-doneChan:
		if err != nil {
			g.Log().Errorf(ctx, "Task %s failed: %v", *taskName, err)
			os.Exit(1)
		}
		g.Log().Infof(ctx, "Task %s completed successfully", *taskName)

		// 任务完成后，等待 Kafka 消息发送完成
		flushKafkaMessages(ctx)

	case sig := <-sigChan:
		g.Log().Infof(ctx, "Received signal %v, stopping task...", sig)
		cancel()

		// 等待任务完成（最多等待 30 秒）
		select {
		case <-doneChan:
			g.Log().Info(ctx, "Task stopped gracefully")
			// 任务停止后，也要等待 Kafka 消息发送完成
			flushKafkaMessages(ctx)
		case <-time.After(30 * time.Second):
			g.Log().Warning(ctx, "Task did not stop in time, forcing exit")
		}
	}
}

// flushKafkaMessages 等待 Kafka 消息发送完成
func flushKafkaMessages(ctx context.Context) {
	// 导入 service 包来访问 Kafka 服务
	// 由于已经在 boot.Initialize 中初始化了服务，可以直接使用

	// 创建一个带超时的 context，最多等待 15 秒
	flushCtx, cancel := context.WithTimeout(ctx, 15*time.Second)
	defer cancel()

	g.Log().Info(flushCtx, "Waiting for Kafka messages to be sent...")

	// 等待 Kafka 生产者刷新
	if err := service.Kafka().FlushProducer(flushCtx); err != nil {
		g.Log().Warningf(flushCtx, "Failed to flush Kafka producer: %v", err)
	} else {
		g.Log().Info(flushCtx, "Kafka messages flushed successfully")
	}
}
