package main

import (
	"context"
	"flag"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gogf/gf/v2/frame/g"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	"telegram-bot-api/internal/boot"
	"telegram-bot-api/internal/price-monitor/config"
	"telegram-bot-api/internal/price-monitor/service"

	// Register Redis driver
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
)

func main() {
	// 命令行参数
	var configPath string
	flag.StringVar(&configPath, "config", "", "配置文件路径")
	flag.Parse()

	// 初始化基础服务（加载配置、初始化数据库、Redis等）
	boot.Initialize(context.Background())

	// 加载价格监控服务配置
	cfg, err := config.LoadConfig()
	if err != nil {
		g.Log().Fatalf(context.Background(), "Failed to load config: %v", err)
	}

	// 设置日志级别
	// g.Log().SetLevel(cfg.Service.LogLevel) // TODO: 需要将字符串转换为日志级别

	// 创建价格监控服务
	priceMonitor, err := service.NewPriceMonitorService(cfg)
	if err != nil {
		g.Log().Fatalf(context.Background(), "Failed to create price monitor service: %v", err)
	}

	// 创建根context
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动服务
	if err := priceMonitor.Start(ctx); err != nil {
		g.Log().Fatalf(ctx, "Failed to start price monitor service: %v", err)
	}

	g.Log().Info(ctx, "Price monitor service started successfully")
	g.Log().Infof(ctx, "Service: %s", cfg.Service.Name)
	g.Log().Infof(ctx, "Port: %d", cfg.Service.Port)
	g.Log().Infof(ctx, "Prometheus Port: %d", cfg.Monitoring.PrometheusPort)
	g.Log().Infof(ctx, "Symbols: %v", cfg.Symbols)

	// 等待退出信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	sig := <-quit

	g.Log().Infof(ctx, "Received signal: %v, shutting down...", sig)

	// 优雅关闭
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	if err := priceMonitor.Stop(shutdownCtx); err != nil {
		g.Log().Errorf(shutdownCtx, "Error during shutdown: %v", err)
	}

	g.Log().Info(ctx, "Price monitor service stopped")
	os.Exit(0)
}
