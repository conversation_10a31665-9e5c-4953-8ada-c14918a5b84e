package main

import (
	"telegram-bot-api/internal/cmd"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/v2/os/gctx"

	_ "telegram-bot-api/internal/logic"  // 注册业务逻辑 (如果需要自动注册) - 暂时注释，待 logic 包实现后取消注释
	_ "telegram-bot-api/internal/packed" // 打包静态资源 (如果使用)

	// Import packages for functionality
	_ "telegram-bot-api/internal/bot/profile"
	_ "telegram-bot-api/internal/bot/verification" // Import verification package for backup account verification
)

func main() {
	// 传递 "cron" 标识符启动定时任务服务
	cmd.Main(gctx.New(), "cron")
}
